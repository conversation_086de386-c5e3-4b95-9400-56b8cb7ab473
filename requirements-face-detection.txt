# World's Best Face Detection Dependencies
# Install these for optimal face detection accuracy

# Tier 1 - Best Accuracy (Recommended)
insightface>=0.7.3          # SCRFD - State-of-the-art accuracy
onnxruntime>=1.15.0         # Required for InsightFace
retina-face>=0.0.13         # RetinaFace - Excellent accuracy

# Tier 2 - Good Balance
ultralytics>=8.0.0          # YOLOv8 Face - Latest YOLO with face detection
mediapipe>=0.10.0           # Google's MediaPipe - Fast and reliable
mtcnn>=0.1.1               # MTCNN - Good for small faces

# Core dependencies (already in main requirements)
opencv-python>=4.5.0
numpy>=1.21.0
torch>=1.9.0               # For some models
torchvision>=0.10.0        # For some models

# Optional GPU acceleration
# onnxruntime-gpu>=1.15.0   # Uncomment for GPU support with InsightFace
