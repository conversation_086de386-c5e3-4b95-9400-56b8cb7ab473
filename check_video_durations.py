#!/usr/bin/env python3
"""
Script to check video durations in the most recent pipeline output
"""

import os
import sys
import glob
import cv2

def check_video_durations():
    """Check durations of all video files in the most recent output directory"""

    # Find the most recent output directory
    output_dirs = glob.glob("output/*")
    if not output_dirs:
        print("No output directories found")
        return

    # Sort by modification time to get the most recent
    most_recent = max(output_dirs, key=os.path.getmtime)
    print(f"Checking videos in: {most_recent}")
    print("=" * 60)

    def get_video_duration(video_path):
        """Get video duration using OpenCV"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None

            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            cap.release()

            if fps > 0:
                duration = frame_count / fps
                return duration
            else:
                return None
        except Exception as e:
            print(f"Error getting duration for {video_path}: {e}")
            return None

    # Check clips directory
    clips_dir = os.path.join(most_recent, "clips")
    if os.path.exists(clips_dir):
        print("\n📹 CLIP VIDEOS:")
        print("-" * 40)

        clip_files = glob.glob(os.path.join(clips_dir, "*.mp4"))
        clip_files.sort()

        total_duration = 0
        min_duration_violations = []

        for clip_file in clip_files:
            duration = get_video_duration(clip_file)
            if duration is not None:
                total_duration += duration

                filename = os.path.basename(clip_file)
                print(f"{filename}: {duration:.2f} seconds")

                # Check if below 10 seconds
                if duration < 10.0:
                    min_duration_violations.append((filename, duration))
            else:
                print(f"Could not get duration for {clip_file}")

        print(f"\nTotal clips: {len(clip_files)}")
        print(f"Total duration: {total_duration:.2f} seconds")

        if min_duration_violations:
            print(f"\n⚠️  MINIMUM DURATION VIOLATIONS ({len(min_duration_violations)}):")
            for filename, duration in min_duration_violations:
                print(f"  - {filename}: {duration:.2f}s (< 10.0s)")
        else:
            print("\n✅ All clips meet 10-second minimum duration requirement")

    # Check highlights directory
    highlights_dir = os.path.join(most_recent, "highlights")
    if os.path.exists(highlights_dir):
        print("\n🎯 HIGHLIGHT VIDEOS:")
        print("-" * 40)

        highlight_files = glob.glob(os.path.join(highlights_dir, "*.mp4"))
        highlight_files.sort()

        for highlight_file in highlight_files:
            duration = get_video_duration(highlight_file)
            if duration is not None:
                filename = os.path.basename(highlight_file)
                print(f"{filename}: {duration:.2f} seconds")
            else:
                print(f"Could not get duration for {highlight_file}")

    # Check captioned directory
    captioned_dir = os.path.join(most_recent, "captioned")
    if os.path.exists(captioned_dir):
        print("\n📝 CAPTIONED VIDEOS:")
        print("-" * 40)

        captioned_files = glob.glob(os.path.join(captioned_dir, "*.mp4"))
        captioned_files.sort()

        for captioned_file in captioned_files:
            duration = get_video_duration(captioned_file)
            if duration is not None:
                filename = os.path.basename(captioned_file)
                print(f"{filename}: {duration:.2f} seconds")
            else:
                print(f"Could not get duration for {captioned_file}")

if __name__ == "__main__":
    check_video_durations()
