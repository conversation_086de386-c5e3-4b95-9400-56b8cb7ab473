#!/usr/bin/env python3
"""
Simple test for highlight duration validation without external dependencies
"""

import unittest
import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Test the validation logic directly without importing config
MIN_HIGHLIGHT_DURATION_SECONDS = 10.0

class SimpleHighlightValidationUtils:
    """Simple version of validation utils for testing"""
    
    @staticmethod
    def validate_highlight_duration(duration, min_duration=None):
        """Validate highlight duration"""
        min_required = min_duration if min_duration is not None else MIN_HIGHLIGHT_DURATION_SECONDS
        
        result = {
            'valid': False,
            'duration': duration,
            'min_required': min_required,
            'meets_minimum': False,
            'errors': []
        }
        
        try:
            # Check if duration is a valid number
            if not isinstance(duration, (int, float)):
                result['errors'].append(f"Invalid duration type: {type(duration)}. Expected number.")
                return result
            
            # Check if duration is positive
            if duration <= 0:
                result['errors'].append(f"Duration must be positive, got: {duration}")
                return result
            
            # Check minimum duration requirement
            if duration < min_required:
                result['errors'].append(
                    f"Highlight duration ({duration:.1f}s) is below minimum requirement ({min_required:.1f}s). "
                    f"Highlights must be at least {min_required} seconds long for optimal viewer engagement."
                )
                return result
            
            # All validations passed
            result['valid'] = True
            result['meets_minimum'] = True
            
            return result
            
        except Exception as e:
            result['errors'].append(f"Unexpected error during duration validation: {str(e)}")
            return result
    
    @staticmethod
    def enforce_minimum_duration(highlights, min_duration=None, log_filtered=True):
        """Filter highlights to enforce minimum duration"""
        min_required = min_duration if min_duration is not None else MIN_HIGHLIGHT_DURATION_SECONDS
        filtered = []
        
        for highlight in highlights:
            # Extract duration from highlight
            duration = highlight.get('duration')
            if duration is None:
                # Try alternative duration fields
                start_time = highlight.get('start_time', highlight.get('start', 0))
                end_time = highlight.get('end_time', highlight.get('end', 0))
                if start_time is not None and end_time is not None:
                    duration = end_time - start_time
            
            if duration is not None and duration >= min_required:
                filtered.append(highlight)
        
        return filtered


class TestSimpleDurationValidation(unittest.TestCase):
    """Test cases for simple duration validation"""

    def setUp(self):
        """Set up test fixtures"""
        self.min_duration = MIN_HIGHLIGHT_DURATION_SECONDS
        self.validator = SimpleHighlightValidationUtils()

    def test_validate_duration_valid(self):
        """Test validation of valid durations"""
        for duration in [10.0, 15.5, 30.0, 60.0]:
            with self.subTest(duration=duration):
                result = self.validator.validate_highlight_duration(duration)
                self.assertTrue(result['valid'])
                self.assertTrue(result['meets_minimum'])
                self.assertEqual(result['duration'], duration)
                self.assertEqual(len(result['errors']), 0)

    def test_validate_duration_invalid(self):
        """Test validation of invalid durations"""
        for duration in [0.0, 5.0, 9.9]:
            with self.subTest(duration=duration):
                result = self.validator.validate_highlight_duration(duration)
                self.assertFalse(result['valid'])
                self.assertFalse(result['meets_minimum'])
                self.assertGreater(len(result['errors']), 0)

    def test_validate_duration_edge_cases(self):
        """Test edge cases"""
        # Exactly minimum duration
        result = self.validator.validate_highlight_duration(10.0)
        self.assertTrue(result['valid'])
        
        # Just below minimum
        result = self.validator.validate_highlight_duration(9.999)
        self.assertFalse(result['valid'])
        
        # Just above minimum
        result = self.validator.validate_highlight_duration(10.001)
        self.assertTrue(result['valid'])

    def test_validate_duration_invalid_types(self):
        """Test with invalid types"""
        for invalid_duration in ["10", None, [], {}]:
            with self.subTest(duration=invalid_duration):
                result = self.validator.validate_highlight_duration(invalid_duration)
                self.assertFalse(result['valid'])
                self.assertGreater(len(result['errors']), 0)

    def test_enforce_minimum_duration(self):
        """Test enforcement of minimum duration"""
        highlights = [
            {'duration': 15.0, 'start_time': 0, 'end_time': 15},  # Valid
            {'duration': 5.0, 'start_time': 20, 'end_time': 25},  # Invalid
            {'duration': 12.0, 'start_time': 30, 'end_time': 42}, # Valid
            {'start_time': 50, 'end_time': 55},                   # 5s - Invalid
            {'start_time': 60, 'end_time': 75},                   # 15s - Valid
        ]
        
        filtered = self.validator.enforce_minimum_duration(highlights, log_filtered=False)
        
        # Should have 3 valid highlights
        self.assertEqual(len(filtered), 3)
        
        # Verify all filtered highlights meet minimum duration
        for highlight in filtered:
            duration = highlight.get('duration')
            if duration is None:
                start_time = highlight.get('start_time', highlight.get('start', 0))
                end_time = highlight.get('end_time', highlight.get('end', 0))
                duration = end_time - start_time
            self.assertGreaterEqual(duration, self.min_duration)

    def test_enforce_minimum_duration_custom_minimum(self):
        """Test enforcement with custom minimum"""
        highlights = [
            {'duration': 15.0},  # Valid for 10s, invalid for 20s
            {'duration': 25.0},  # Valid for both
            {'duration': 5.0},   # Invalid for both
        ]
        
        # Test with 20s minimum
        filtered = self.validator.enforce_minimum_duration(highlights, min_duration=20.0, log_filtered=False)
        self.assertEqual(len(filtered), 1)  # Only 25s highlight should remain
        self.assertEqual(filtered[0]['duration'], 25.0)

    def test_minimum_duration_constant(self):
        """Test that the minimum duration constant is correct"""
        self.assertEqual(MIN_HIGHLIGHT_DURATION_SECONDS, 10.0)
        self.assertIsInstance(MIN_HIGHLIGHT_DURATION_SECONDS, (int, float))

    def test_error_messages_contain_duration_info(self):
        """Test that error messages contain useful duration information"""
        result = self.validator.validate_highlight_duration(5.0)
        self.assertFalse(result['valid'])
        
        error_msg = result['errors'][0]
        self.assertIn("5.0s", error_msg)
        self.assertIn("10.0s", error_msg)
        self.assertIn("minimum requirement", error_msg)

    def test_custom_minimum_override(self):
        """Test custom minimum duration override"""
        custom_min = 15.0
        
        # Valid with default, invalid with custom
        result = self.validator.validate_highlight_duration(12.0, custom_min)
        self.assertFalse(result['valid'])
        self.assertEqual(result['min_required'], custom_min)
        
        # Valid with custom
        result = self.validator.validate_highlight_duration(20.0, custom_min)
        self.assertTrue(result['valid'])
        self.assertEqual(result['min_required'], custom_min)

    def test_performance_with_large_list(self):
        """Test performance with large highlight list"""
        import time
        
        # Create large list of highlights
        large_highlights = []
        for i in range(1000):
            duration = 5.0 + (i % 20)  # Mix of valid and invalid durations
            large_highlights.append({'duration': duration})
        
        start_time = time.time()
        filtered = self.validator.enforce_minimum_duration(large_highlights, log_filtered=False)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should complete quickly
        self.assertLess(processing_time, 1.0, "Processing should complete within 1 second")
        
        # Verify results
        self.assertGreater(len(filtered), 0)
        for highlight in filtered:
            self.assertGreaterEqual(highlight['duration'], self.min_duration)


if __name__ == '__main__':
    print(f"Testing highlight duration validation with minimum: {MIN_HIGHLIGHT_DURATION_SECONDS}s")
    unittest.main(verbosity=2)
