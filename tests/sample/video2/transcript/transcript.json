{"duration": 824.739990234375, "language": "english", "text": "What's that Insight of yours. We think we can't change ourselves, but we can we think we can change other people, but we can't exactly I think to add to that you can't change other people you can change your reaction to them You can change yourself But other people only change through trauma or their own insight on their own schedule and never in a way that you like yeah, <PERSON><PERSON> says that People do sometimes change but rarely in relationships and never when they're told to Absolutely, yeah the fastest way to kind of alienate somebody's to tell them to change in fact the Dale Carnegie School of Public Speaking you know the way that operates is They get you up there and they realize that the number one problem with public speaking is that people are very self-conscious and so People who are practicing in the Dale Carnegie School of Public Speaking. I don't know. I never went through it I heard the second hands I could be wrong, but I like the story Where they get up and they start speaking and the people in the audience are only allowed to compliment them genuine compliments Not fake compliments on things that they did well But you're not allowed to criticize them on things that they did poorly and eventually they kind of get through it and they developed self-confidence the same way There's like the Michelle Thomas School of language learning and on that one What they do is you listen to a teacher talking to a student. They're not teaching you You're not expected to remember or memorize anything. You just listen to a student stumbling over the language It's a better way to learn because you yourself don't feel flustered. You're being tested graded Oh, so you're not in your own head as much correct you're not in your own head and you're just you might even be laughing at the student or you might be agreeing with the teacher or vice versa or sympathizing with a student but because you are a passive observer you can be more objective about it and you aren't threatened or fearful and you can learn better and coming back to the original point of You can't change people if you do want to change someone's behavior I think the only effective way to do it is to compliment them when they do something you want not to positive Yeah, exactly not to insult them or be negative or critical when they do something you don't want and we can't help it It's obviously in our nature to criticize and I do it as well. But it just reminds me that like When somebody does something praiseworthy don't forget to praise them like definitely go out of your way and it'll be genuine It has to be genuine. It can't be a fake thing This is not you know, one of those Just dropping compliments type thing eventually that people will see through that they want authenticity But just don't forget to praise people when they do something praiseworthy and you'll get more of that behavior There was a really famous thread on reddit about five questions to ask yourself if you're uncertain about your relationship one of the questions was are you truly in love with your partner or just their potential or the idea of them and that's the You know, they show such great promise. They either look at their look at their ability for Change and growth they They're on the right path. The partner matching thing is so hard, you know when people come and ask me like Oh, should I be with this person? Like well if you're asking me It's just clearly though. All right, because you wouldn't have to ask if you were with the right person or when you ask someone like why? They're in a relationship with somebody and they start reading out his or her resume, right? That's also a bad sign It's how what you mean? Oh, it's like oh we have so much in common. We like to golf together It's like it's not a basis for a relationship or oh is you know, she's a ballerina or you know He went to Harvard or what have you this is a resume item. So that's not who the person actually is What's a better answer? I just love being with this person. I just trust them. I you know, I enjoy being around them. I Love how capable he is. I love how kind she is. You know, I love her spirit. I love his energy the more the more Materially and concretely Definable the reasons are you're together the worst they are the ineffable is actually where the sort of true love lies Because real love is a form of unity. It's a form of connection. It's connecting spirits Oh you to my consciousness meets your consciousness. It's a it the the the underlying drive in love in art in Science in Mysticism is the desire for unity It's a desire for connection as you know bore has famously wrote in every human There's a sense of something infinite has been lost. You know, there's a god-shaped hole in you. You're trying to fill and So we're always trying to find that connection. Love is trying to find it in one other person and saying, okay, you're male I'm female or whatever and you know, whatever your predilections are and not now we connect now we form a whole a Connected whole or in mysticism. It's like it's all about. Okay, sit down meditate and you'll feel the whole in science. It's like oh You know Atoms bouncing is mechanics, but that generates heat so thermodynamics and motion or kinetics or one combined theory That's a whole electricity and magnetism or one thing. That's that's the whole creates that sense of awe In art, it's like I feel an emotion I create a piece of art around it and then you see that painting or you see the Sistine Chapel or you read The poem and you feel that emotion. So again, it's it's creating unity. It's creating connection and I think everybody craves that and so When you really love somebody it's because you you feel a sense of wholeness by being around them And that sense of wholeness probably doesn't have anything to do with what school they went to You know or what career they're in? Just sort of tying that into the life is short. Stop fucking about If you're faced with a difficult choice and you cannot decide the answer is no and the reason is modern society is full of options Yeah, knowing this rationally Sounds sounds great but having the courage to Commit to it in reality. I think it's a different task and cutting your losses quickly in the big three relationships jobs and locations is hard, what would you say to someone who may Cerebrally be able to agree with you and say I understand My cousin said this about me. He said that He said what I really he says what I've really noticed about you is your ability to walk away from situations that are Just not great enough for you or good not good enough for you Yes, and I think that is a characteristic that I have. I will not accept second best outcomes in my life And Ultimately you will end up wherever is acceptable to you. You will get out of life. Whatever is acceptable to you And there are certain things to me that are very very important where I will not settle for second best But then there are a lot of other things I just don't care about because if I spend all my time caring about those things I don't have the energy for the few things that matter and So in decision-making of a few heuristics for myself other people can use their own but minor if you can't decide the answer is no if you're offered an opportunity if you have a new thing that you're saying yes or no to that is a Change from where you're starting The answer is by default always no Secondly if you have two decisions if you have a or B and both seem like very equal Take the path that's more painful in the short term The one that's going to be painful immediately because your brain is always trying to avoid pain So any pain that is imminent it is going to treat as much larger than it actually is This is kind of like a decision-making equivalent of Taleb surgeon Tell a surgeon whether you want the surgeon doesn't look as good because he's more likely to be a good surgeon Yeah, it's similar in that appearances are deceiving because you're avoiding conflict you're avoiding pain So take the path is more painful in the short term Because your brain is creating this illusion the short-term pain is greater than the long-term pain because long-term Yeah, you'll commit your future self to all kinds of long-term. I don't know manana. Exactly manana So take the more short-term painful one and then finally the last one which I would try to couple Gupta with is that You want to check it take the choice that will leave you more Equanimous in the long term by quantum assuming is like more peace more mental peace in the long term so whatever clears your mind more and will have you having less self-talk in the future if you can model that out that is probably the better route to go and then I would focus decision-making down on the three things that really matter because everything else is Downstream of these states these three decisions, especially these are early life decisions later in life You have different things to optimize for but early in life. You're trying to figure out who you're with What you're doing and where you live and I think on all three of those you want to think you want to think pretty hard About it and people do some of these unconsciously, you know who you're with very often cycle We were in a relationship with stumble along. It felt okay. I've been enough time. So we got married, right? not great reasons Maybe not terrible reasons either means people who overthink these things sometimes don't get the right answer But maybe here if you are the kind of person that's not going to settle for second best you iterate you iterate on a closed Time frame so you don't run out the clock and then you decide On what you do you try a whole bunch of different things until you find the one that feels like play to you Looks like work to others. You can't lose at it Get some leverage try to find some practical application of it and go into that and then where you live Where you live is really important. I don't think people spend enough time on that one I think people pick cities randomly based on where I went to school or where my family happened to be or where My friend was I visited one weekend. I really liked it You really want to think it through because where you live really constrains and defines your opportunities It It's going to determine your friend circle. It's going to determine your dating pool. It's going to turn your job opportunities It's going to determine the food and air and water quality that you receive It's going to determine your proximity to your family, which might be important as you get older and have kids so very very very important decision whether you know quality of life how much you stay inside or outside and how long you'll live based on that and I think people choose that one probably more poorly than they put a lot less thought into that one than the other two In some ways. Yeah, but also the you're so right how many people fall backward into a relationship and before they know it We're living together. We got a dog We got a kid we would marry with you and yeah And then when you have kids because then that's half of you and half of them running around You're never gonna separate yourself from that So once you have a child with somebody then the most important thing in the world to you is half that other person Whether you like them or not. Mm-hmm. Yeah Jeffrey Miller Had a tweet a long time ago that I always think about and he said Every parenting book in the world could be replaced with one book on behavioral genetics But I'm a big believer in genetics. Yes I do think a lot of behavior is downstream of genetics and I think we underplay that we like to overplay nurture and underplay nurture For a sort of say well underplay nature for societal reasons, but nature is a big deal The temperament of the person you marry is probably going to be reflected in your child by default people can change Securely-attached kid pick a securely-attached partner Well, the secret to a happy relationship is two happy people, right? So I would say if you want to be happy then Be with a happy person Don't think you're gonna be with someone who's unhappy and then make them happy down the road How are if you're okay with them being unhappy, but there are other things you like about them That's fine. But this goes back to their own happiness with other things Yes, and actually we talked a little bit about how people do connect successfully, you know on spirit and and those things but that's maybe a little too abstract if you want to get a little more practical it could be based on values and Values a set of things you won't compromise on values are the tough decisions of oh my parent got sick Do they move in with us or do we put them in a nursing home? You know that do we give the children money or do we not? You know, do we? Do we move across the country to be closer to our family or do we stay put where we are You know, do we argue about politics do we care or do we not right the values are way more important than checklist items And I think if people were to align much more on their values, they would have much more successful relationships. Hmm the emotional pain of Fearing change. I have this thing the job the location to partner. I'm going to enter or not enter this thing for the most part It's leaving. I think we have this sort of loss aversion that we really evolve loss aversion It's just painful separating yourself in front of your friends. It's embarrassing and how you how would you advise people to? Get past themselves with that loss aversion that fear of change. Oh my god. I'm gonna yes the hardest thing in the world Starting over it's back to the zero to one thing. It's a it's the mountain climbing thing You're not gonna find your path to the top of the mountain in the first go-around Sometimes you go up there you get stuck and you come back down and the difference between all the successful people and the ones who Are not is the ones who are successful one is so badly They're willing to go back and start over again and again whether in their career or in their relationships or in anything else In other news this episode is brought to you by function staying on top of your health requires more than just an annual Physical which is why I partnered with function They run lab tests twice a year to track over a hundred biomarkers and monitor for early signs of thousands of diseases They even screen for 50 types of cancer at stage one Which is five times more data than you get from an annual physical you receive insights from a team of expert physicians who provide a detail Written clinician summary for their observations and then phone consultations for any critical findings getting these lab tests done would usually cost thousands, but with function it is only $499 and right now you can get the exact same blood panels that I get and Bypass their waitlist by going to the link in the description below or heading to function health comm slash modern wisdom That's function health comm slash Modern wisdom. Thank you very much for tuning in If you enjoyed that clip with Neval just a mere taster the full-length episode is available right here Go on Watch it", "segments": [{"id": 0, "avg_logprob": -0.272880882024765, "compression_ratio": 1.9674797058105469, "end": 1.4800000190734863, "no_speech_prob": 0.03357846662402153, "seek": 0, "start": 0.0, "temperature": 0.0, "text": " What's that", "tokens": [50364, 708, 311, 300, 50438]}, {"id": 1, "avg_logprob": -0.272880882024765, "compression_ratio": 1.9674797058105469, "end": 7.519999980926514, "no_speech_prob": 0.03357846662402153, "seek": 0, "start": 1.4800000190734863, "temperature": 0.0, "text": " Insight of yours. We think we can't change ourselves, but we can we think we can change other people, but we can't exactly", "tokens": [50438, 9442, 397, 295, 6342, 13, 492, 519, 321, 393, 380, 1319, 4175, 11, 457, 321, 393, 321, 519, 321, 393, 1319, 661, 561, 11, 457, 321, 393, 380, 2293, 50740]}, {"id": 2, "avg_logprob": -0.272880882024765, "compression_ratio": 1.9674797058105469, "end": 11.600000381469727, "no_speech_prob": 0.03357846662402153, "seek": 0, "start": 7.519999980926514, "temperature": 0.0, "text": " I think to add to that you can't change other people you can change your reaction to them", "tokens": [50740, 286, 519, 281, 909, 281, 300, 291, 393, 380, 1319, 661, 561, 291, 393, 1319, 428, 5480, 281, 552, 50944]}, {"id": 3, "avg_logprob": -0.272880882024765, "compression_ratio": 1.9674797058105469, "end": 13.0, "no_speech_prob": 0.03357846662402153, "seek": 0, "start": 11.600000381469727, "temperature": 0.0, "text": " You can change yourself", "tokens": [50944, 509, 393, 1319, 1803, 51014]}, {"id": 4, "avg_logprob": -0.272880882024765, "compression_ratio": 1.9674797058105469, "end": 19.719999313354492, "no_speech_prob": 0.03357846662402153, "seek": 0, "start": 13.0, "temperature": 0.0, "text": " But other people only change through trauma or their own insight on their own schedule and never in a way that you like", "tokens": [51014, 583, 661, 561, 787, 1319, 807, 11407, 420, 641, 1065, 11269, 322, 641, 1065, 7567, 293, 1128, 294, 257, 636, 300, 291, 411, 51350]}, {"id": 5, "avg_logprob": -0.272880882024765, "compression_ratio": 1.9674797058105469, "end": 21.959999084472656, "no_speech_prob": 0.03357846662402153, "seek": 0, "start": 19.959999084472656, "temperature": 0.0, "text": " yeah, <PERSON><PERSON> says that", "tokens": [51362, 1338, 11, 967, 5575, 22338, 266, 1619, 300, 51462]}, {"id": 6, "avg_logprob": -0.272880882024765, "compression_ratio": 1.9674797058105469, "end": 26.639999389648438, "no_speech_prob": 0.03357846662402153, "seek": 0, "start": 22.639999389648438, "temperature": 0.0, "text": " People do sometimes change but rarely in relationships and never when they're told to", "tokens": [51496, 3432, 360, 2171, 1319, 457, 13752, 294, 6159, 293, 1128, 562, 436, 434, 1907, 281, 51696]}, {"id": 7, "avg_logprob": -0.3174664378166199, "compression_ratio": 1.7527272701263428, "end": 33.720001220703125, "no_speech_prob": 0.01853863149881363, "seek": 2664, "start": 27.239999771118164, "temperature": 0.0, "text": " Absolutely, yeah the fastest way to kind of alienate somebody's to tell them to change in fact the Dale Carnegie School of Public Speaking", "tokens": [50394, 7021, 11, 1338, 264, 14573, 636, 281, 733, 295, 12319, 473, 2618, 311, 281, 980, 552, 281, 1319, 294, 1186, 264, 31329, 47301, 5070, 295, 9489, 13069, 50718]}, {"id": 8, "avg_logprob": -0.3174664378166199, "compression_ratio": 1.7527272701263428, "end": 35.880001068115234, "no_speech_prob": 0.01853863149881363, "seek": 2664, "start": 33.880001068115234, "temperature": 0.0, "text": " you know the way that operates is", "tokens": [50726, 291, 458, 264, 636, 300, 22577, 307, 50826]}, {"id": 9, "avg_logprob": -0.3174664378166199, "compression_ratio": 1.7527272701263428, "end": 40.20000076293945, "no_speech_prob": 0.01853863149881363, "seek": 2664, "start": 36.20000076293945, "temperature": 0.0, "text": " They get you up there and they realize that the number one problem with public speaking is", "tokens": [50842, 814, 483, 291, 493, 456, 293, 436, 4325, 300, 264, 1230, 472, 1154, 365, 1908, 4124, 307, 51042]}, {"id": 10, "avg_logprob": -0.3174664378166199, "compression_ratio": 1.7527272701263428, "end": 44.400001525878906, "no_speech_prob": 0.01853863149881363, "seek": 2664, "start": 41.040000915527344, "temperature": 0.0, "text": " that people are very self-conscious and so", "tokens": [51084, 300, 561, 366, 588, 2698, 12, 19877, 293, 370, 51252]}, {"id": 11, "avg_logprob": -0.3174664378166199, "compression_ratio": 1.7527272701263428, "end": 49.84000015258789, "no_speech_prob": 0.01853863149881363, "seek": 2664, "start": 45.7599983215332, "temperature": 0.0, "text": " People who are practicing in the Dale Carnegie School of Public Speaking. I don't know. I never went through it", "tokens": [51320, 3432, 567, 366, 11350, 294, 264, 31329, 47301, 5070, 295, 9489, 13069, 13, 286, 500, 380, 458, 13, 286, 1128, 1437, 807, 309, 51524]}, {"id": 12, "avg_logprob": -0.3174664378166199, "compression_ratio": 1.7527272701263428, "end": 52.36000061035156, "no_speech_prob": 0.01853863149881363, "seek": 2664, "start": 49.84000015258789, "temperature": 0.0, "text": " I heard the second hands I could be wrong, but I like the story", "tokens": [51524, 286, 2198, 264, 1150, 2377, 286, 727, 312, 2085, 11, 457, 286, 411, 264, 1657, 51650]}, {"id": 13, "avg_logprob": -0.26659420132637024, "compression_ratio": 1.9249999523162842, "end": 58.91999816894531, "no_speech_prob": 0.03307640179991722, "seek": 5236, "start": 52.880001068115234, "temperature": 0.0, "text": " Where they get up and they start speaking and the people in the audience are only allowed to compliment them genuine compliments", "tokens": [50390, 2305, 436, 483, 493, 293, 436, 722, 4124, 293, 264, 561, 294, 264, 4034, 366, 787, 4350, 281, 16250, 552, 16699, 35468, 50692]}, {"id": 14, "avg_logprob": -0.26659420132637024, "compression_ratio": 1.9249999523162842, "end": 60.959999084472656, "no_speech_prob": 0.03307640179991722, "seek": 5236, "start": 58.91999816894531, "temperature": 0.0, "text": " Not fake compliments on things that they did well", "tokens": [50692, 1726, 7592, 35468, 322, 721, 300, 436, 630, 731, 50794]}, {"id": 15, "avg_logprob": -0.26659420132637024, "compression_ratio": 1.9249999523162842, "end": 66.27999877929688, "no_speech_prob": 0.03307640179991722, "seek": 5236, "start": 60.959999084472656, "temperature": 0.0, "text": " But you're not allowed to criticize them on things that they did poorly and eventually they kind of get through it and they developed", "tokens": [50794, 583, 291, 434, 406, 4350, 281, 31010, 552, 322, 721, 300, 436, 630, 22271, 293, 4728, 436, 733, 295, 483, 807, 309, 293, 436, 4743, 51060]}, {"id": 16, "avg_logprob": -0.26659420132637024, "compression_ratio": 1.9249999523162842, "end": 68.27999877929688, "no_speech_prob": 0.03307640179991722, "seek": 5236, "start": 66.27999877929688, "temperature": 0.0, "text": " self-confidence the same way", "tokens": [51060, 2698, 12, 47273, 264, 912, 636, 51160]}, {"id": 17, "avg_logprob": -0.26659420132637024, "compression_ratio": 1.9249999523162842, "end": 73.30000305175781, "no_speech_prob": 0.03307640179991722, "seek": 5236, "start": 68.80000305175781, "temperature": 0.0, "text": " There's like the Michelle <PERSON> School of language learning and on that one", "tokens": [51186, 821, 311, 411, 264, 14933, 8500, 5070, 295, 2856, 2539, 293, 322, 300, 472, 51411]}, {"id": 18, "avg_logprob": -0.26659420132637024, "compression_ratio": 1.9249999523162842, "end": 76.95999908447266, "no_speech_prob": 0.03307640179991722, "seek": 5236, "start": 73.30000305175781, "temperature": 0.0, "text": " What they do is you listen to a teacher talking to a student. They're not teaching you", "tokens": [51411, 708, 436, 360, 307, 291, 2140, 281, 257, 5027, 1417, 281, 257, 3107, 13, 814, 434, 406, 4571, 291, 51594]}, {"id": 19, "avg_logprob": -0.26659420132637024, "compression_ratio": 1.9249999523162842, "end": 81.72000122070312, "no_speech_prob": 0.03307640179991722, "seek": 5236, "start": 76.95999908447266, "temperature": 0.0, "text": " You're not expected to remember or memorize anything. You just listen to a student stumbling over the language", "tokens": [51594, 509, 434, 406, 5176, 281, 1604, 420, 27478, 1340, 13, 509, 445, 2140, 281, 257, 3107, 342, 14188, 670, 264, 2856, 51832]}, {"id": 20, "avg_logprob": -0.2655813694000244, "compression_ratio": 1.840390920639038, "end": 86.36000061035156, "no_speech_prob": 0.01150632742792368, "seek": 8172, "start": 81.72000122070312, "temperature": 0.0, "text": " It's a better way to learn because you yourself don't feel flustered. You're being tested graded", "tokens": [50364, 467, 311, 257, 1101, 636, 281, 1466, 570, 291, 1803, 500, 380, 841, 932, 38624, 13, 509, 434, 885, 8246, 2771, 292, 50596]}, {"id": 21, "avg_logprob": -0.2655813694000244, "compression_ratio": 1.840390920639038, "end": 88.44000244140625, "no_speech_prob": 0.01150632742792368, "seek": 8172, "start": 86.36000061035156, "temperature": 0.0, "text": " Oh, so you're not in your own head as much correct", "tokens": [50596, 876, 11, 370, 291, 434, 406, 294, 428, 1065, 1378, 382, 709, 3006, 50700]}, {"id": 22, "avg_logprob": -0.2655813694000244, "compression_ratio": 1.840390920639038, "end": 92.27999877929688, "no_speech_prob": 0.01150632742792368, "seek": 8172, "start": 88.44000244140625, "temperature": 0.0, "text": " you're not in your own head and you're just you might even be laughing at the student or you might be agreeing with the", "tokens": [50700, 291, 434, 406, 294, 428, 1065, 1378, 293, 291, 434, 445, 291, 1062, 754, 312, 5059, 412, 264, 3107, 420, 291, 1062, 312, 36900, 365, 264, 50892]}, {"id": 23, "avg_logprob": -0.2655813694000244, "compression_ratio": 1.840390920639038, "end": 93.87999725341797, "no_speech_prob": 0.01150632742792368, "seek": 8172, "start": 92.27999877929688, "temperature": 0.0, "text": " teacher or vice versa or", "tokens": [50892, 5027, 420, 11964, 25650, 420, 50972]}, {"id": 24, "avg_logprob": -0.2655813694000244, "compression_ratio": 1.840390920639038, "end": 94.91999816894531, "no_speech_prob": 0.01150632742792368, "seek": 8172, "start": 93.87999725341797, "temperature": 0.0, "text": " sympathizing with a student", "tokens": [50972, 22276, 3319, 365, 257, 3107, 51024]}, {"id": 25, "avg_logprob": -0.2655813694000244, "compression_ratio": 1.840390920639038, "end": 100.91999816894531, "no_speech_prob": 0.01150632742792368, "seek": 8172, "start": 94.91999816894531, "temperature": 0.0, "text": " but because you are a passive observer you can be more objective about it and you aren't threatened or fearful and you can learn better and", "tokens": [51024, 457, 570, 291, 366, 257, 14975, 27878, 291, 393, 312, 544, 10024, 466, 309, 293, 291, 3212, 380, 18268, 420, 33014, 293, 291, 393, 1466, 1101, 293, 51324]}, {"id": 26, "avg_logprob": -0.2655813694000244, "compression_ratio": 1.840390920639038, "end": 103.5999984741211, "no_speech_prob": 0.01150632742792368, "seek": 8172, "start": 101.5199966430664, "temperature": 0.0, "text": " coming back to the original point of", "tokens": [51354, 1348, 646, 281, 264, 3380, 935, 295, 51458]}, {"id": 27, "avg_logprob": -0.2655813694000244, "compression_ratio": 1.840390920639038, "end": 108.83999633789062, "no_speech_prob": 0.01150632742792368, "seek": 8172, "start": 104.63999938964844, "temperature": 0.0, "text": " You can't change people if you do want to change someone's behavior", "tokens": [51510, 509, 393, 380, 1319, 561, 498, 291, 360, 528, 281, 1319, 1580, 311, 5223, 51720]}, {"id": 28, "avg_logprob": -0.2636283338069916, "compression_ratio": 1.7912458181381226, "end": 114.5999984741211, "no_speech_prob": 0.0008692743140272796, "seek": 10884, "start": 108.83999633789062, "temperature": 0.0, "text": " I think the only effective way to do it is to compliment them when they do something you want not to positive", "tokens": [50364, 286, 519, 264, 787, 4942, 636, 281, 360, 309, 307, 281, 16250, 552, 562, 436, 360, 746, 291, 528, 406, 281, 3353, 50652]}, {"id": 29, "avg_logprob": -0.2636283338069916, "compression_ratio": 1.7912458181381226, "end": 119.76000213623047, "no_speech_prob": 0.0008692743140272796, "seek": 10884, "start": 114.5999984741211, "temperature": 0.0, "text": " Yeah, exactly not to insult them or be negative or critical when they do something you don't want and we can't help it", "tokens": [50652, 865, 11, 2293, 406, 281, 15285, 552, 420, 312, 3671, 420, 4924, 562, 436, 360, 746, 291, 500, 380, 528, 293, 321, 393, 380, 854, 309, 50910]}, {"id": 30, "avg_logprob": -0.2636283338069916, "compression_ratio": 1.7912458181381226, "end": 124.37999725341797, "no_speech_prob": 0.0008692743140272796, "seek": 10884, "start": 119.76000213623047, "temperature": 0.0, "text": " It's obviously in our nature to criticize and I do it as well. But it just reminds me that like", "tokens": [50910, 467, 311, 2745, 294, 527, 3687, 281, 31010, 293, 286, 360, 309, 382, 731, 13, 583, 309, 445, 12025, 385, 300, 411, 51141]}, {"id": 31, "avg_logprob": -0.2636283338069916, "compression_ratio": 1.7912458181381226, "end": 130.1999969482422, "no_speech_prob": 0.0008692743140272796, "seek": 10884, "start": 124.9000015258789, "temperature": 0.0, "text": " When somebody does something praiseworthy don't forget to praise them like definitely go out of your way and it'll be genuine", "tokens": [51167, 1133, 2618, 775, 746, 13286, 23727, 500, 380, 2870, 281, 13286, 552, 411, 2138, 352, 484, 295, 428, 636, 293, 309, 603, 312, 16699, 51432]}, {"id": 32, "avg_logprob": -0.2636283338069916, "compression_ratio": 1.7912458181381226, "end": 132.1999969482422, "no_speech_prob": 0.0008692743140272796, "seek": 10884, "start": 130.1999969482422, "temperature": 0.0, "text": " It has to be genuine. It can't be a fake thing", "tokens": [51432, 467, 575, 281, 312, 16699, 13, 467, 393, 380, 312, 257, 7592, 551, 51532]}, {"id": 33, "avg_logprob": -0.2636283338069916, "compression_ratio": 1.7912458181381226, "end": 134.24000549316406, "no_speech_prob": 0.0008692743140272796, "seek": 10884, "start": 132.24000549316406, "temperature": 0.0, "text": " This is not you know, one of those", "tokens": [51534, 639, 307, 406, 291, 458, 11, 472, 295, 729, 51634]}, {"id": 34, "avg_logprob": -0.2866751253604889, "compression_ratio": 1.7707006931304932, "end": 139.1199951171875, "no_speech_prob": 0.002472333377227187, "seek": 13424, "start": 134.60000610351562, "temperature": 0.0, "text": " Just dropping compliments type thing eventually that people will see through that they want authenticity", "tokens": [50382, 1449, 13601, 35468, 2010, 551, 4728, 300, 561, 486, 536, 807, 300, 436, 528, 34215, 50608]}, {"id": 35, "avg_logprob": -0.2866751253604889, "compression_ratio": 1.7707006931304932, "end": 143.32000732421875, "no_speech_prob": 0.002472333377227187, "seek": 13424, "start": 139.24000549316406, "temperature": 0.0, "text": " But just don't forget to praise people when they do something praiseworthy and you'll get more of that behavior", "tokens": [50614, 583, 445, 500, 380, 2870, 281, 13286, 561, 562, 436, 360, 746, 13286, 23727, 293, 291, 603, 483, 544, 295, 300, 5223, 50818]}, {"id": 36, "avg_logprob": -0.2866751253604889, "compression_ratio": 1.7707006931304932, "end": 148.9199981689453, "no_speech_prob": 0.002472333377227187, "seek": 13424, "start": 143.32000732421875, "temperature": 0.0, "text": " There was a really famous thread on reddit about five questions to ask yourself if you're uncertain about your relationship", "tokens": [50818, 821, 390, 257, 534, 4618, 7207, 322, 2182, 17975, 466, 1732, 1651, 281, 1029, 1803, 498, 291, 434, 11308, 466, 428, 2480, 51098]}, {"id": 37, "avg_logprob": -0.2866751253604889, "compression_ratio": 1.7707006931304932, "end": 155.27999877929688, "no_speech_prob": 0.002472333377227187, "seek": 13424, "start": 148.9199981689453, "temperature": 0.0, "text": " one of the questions was are you truly in love with your partner or just their potential or the idea of them and that's the", "tokens": [51098, 472, 295, 264, 1651, 390, 366, 291, 4908, 294, 959, 365, 428, 4975, 420, 445, 641, 3995, 420, 264, 1558, 295, 552, 293, 300, 311, 264, 51416]}, {"id": 38, "avg_logprob": -0.2866751253604889, "compression_ratio": 1.7707006931304932, "end": 160.39999389648438, "no_speech_prob": 0.002472333377227187, "seek": 13424, "start": 156.0399932861328, "temperature": 0.0, "text": " You know, they show such great promise. They either look at their look at their ability for", "tokens": [51454, 509, 458, 11, 436, 855, 1270, 869, 6228, 13, 814, 2139, 574, 412, 641, 574, 412, 641, 3485, 337, 51672]}, {"id": 39, "avg_logprob": -0.32884329557418823, "compression_ratio": 1.7120252847671509, "end": 163.39999389648438, "no_speech_prob": 0.0018674583407118917, "seek": 16040, "start": 161.39999389648438, "temperature": 0.0, "text": " Change and growth they", "tokens": [50414, 15060, 293, 4599, 436, 50514]}, {"id": 40, "avg_logprob": -0.32884329557418823, "compression_ratio": 1.7120252847671509, "end": 168.47999572753906, "no_speech_prob": 0.0018674583407118917, "seek": 16040, "start": 163.47999572753906, "temperature": 0.0, "text": " They're on the right path. The partner matching thing is so hard, you know when people come and ask me like", "tokens": [50518, 814, 434, 322, 264, 558, 3100, 13, 440, 4975, 14324, 551, 307, 370, 1152, 11, 291, 458, 562, 561, 808, 293, 1029, 385, 411, 50768]}, {"id": 41, "avg_logprob": -0.32884329557418823, "compression_ratio": 1.7120252847671509, "end": 171.16000366210938, "no_speech_prob": 0.0018674583407118917, "seek": 16040, "start": 168.47999572753906, "temperature": 0.0, "text": " Oh, should I be with this person? Like well if you're asking me", "tokens": [50768, 876, 11, 820, 286, 312, 365, 341, 954, 30, 1743, 731, 498, 291, 434, 3365, 385, 50902]}, {"id": 42, "avg_logprob": -0.32884329557418823, "compression_ratio": 1.7120252847671509, "end": 178.47999572753906, "no_speech_prob": 0.0018674583407118917, "seek": 16040, "start": 172.16000366210938, "temperature": 0.0, "text": " It's just clearly though. All right, because you wouldn't have to ask if you were with the right person or when you ask someone like why?", "tokens": [50952, 467, 311, 445, 4448, 1673, 13, 1057, 558, 11, 570, 291, 2759, 380, 362, 281, 1029, 498, 291, 645, 365, 264, 558, 954, 420, 562, 291, 1029, 1580, 411, 983, 30, 51268]}, {"id": 43, "avg_logprob": -0.32884329557418823, "compression_ratio": 1.7120252847671509, "end": 184.39999389648438, "no_speech_prob": 0.0018674583407118917, "seek": 16040, "start": 178.47999572753906, "temperature": 0.0, "text": " They're in a relationship with somebody and they start reading out his or her resume, right? That's also a bad sign", "tokens": [51268, 814, 434, 294, 257, 2480, 365, 2618, 293, 436, 722, 3760, 484, 702, 420, 720, 15358, 11, 558, 30, 663, 311, 611, 257, 1578, 1465, 51564]}, {"id": 44, "avg_logprob": -0.32884329557418823, "compression_ratio": 1.7120252847671509, "end": 189.16000366210938, "no_speech_prob": 0.0018674583407118917, "seek": 16040, "start": 184.39999389648438, "temperature": 0.0, "text": " It's how what you mean? Oh, it's like oh we have so much in common. We like to golf together", "tokens": [51564, 467, 311, 577, 437, 291, 914, 30, 876, 11, 309, 311, 411, 1954, 321, 362, 370, 709, 294, 2689, 13, 492, 411, 281, 12880, 1214, 51802]}, {"id": 45, "avg_logprob": -0.24814002215862274, "compression_ratio": 1.7941176891326904, "end": 195.0399932861328, "no_speech_prob": 0.0015486746560782194, "seek": 18916, "start": 189.16000366210938, "temperature": 0.0, "text": " It's like it's not a basis for a relationship or oh is you know, she's a ballerina or you know", "tokens": [50364, 467, 311, 411, 309, 311, 406, 257, 5143, 337, 257, 2480, 420, 1954, 307, 291, 458, 11, 750, 311, 257, 2594, 260, 1426, 420, 291, 458, 50658]}, {"id": 46, "avg_logprob": -0.24814002215862274, "compression_ratio": 1.7941176891326904, "end": 199.1999969482422, "no_speech_prob": 0.0015486746560782194, "seek": 18916, "start": 195.0399932861328, "temperature": 0.0, "text": " He went to Harvard or what have you this is a resume item. So that's not who the person actually is", "tokens": [50658, 634, 1437, 281, 13378, 420, 437, 362, 291, 341, 307, 257, 15358, 3174, 13, 407, 300, 311, 406, 567, 264, 954, 767, 307, 50866]}, {"id": 47, "avg_logprob": -0.24814002215862274, "compression_ratio": 1.7941176891326904, "end": 205.83999633789062, "no_speech_prob": 0.0015486746560782194, "seek": 18916, "start": 199.1999969482422, "temperature": 0.0, "text": " What's a better answer? I just love being with this person. I just trust them. I you know, I enjoy being around them. I", "tokens": [50866, 708, 311, 257, 1101, 1867, 30, 286, 445, 959, 885, 365, 341, 954, 13, 286, 445, 3361, 552, 13, 286, 291, 458, 11, 286, 2103, 885, 926, 552, 13, 286, 51198]}, {"id": 48, "avg_logprob": -0.24814002215862274, "compression_ratio": 1.7941176891326904, "end": 212.4600067138672, "no_speech_prob": 0.0015486746560782194, "seek": 18916, "start": 207.0399932861328, "temperature": 0.0, "text": " Love how capable he is. I love how kind she is. You know, I love her spirit. I love his energy", "tokens": [51258, 5956, 577, 8189, 415, 307, 13, 286, 959, 577, 733, 750, 307, 13, 509, 458, 11, 286, 959, 720, 3797, 13, 286, 959, 702, 2281, 51529]}, {"id": 49, "avg_logprob": -0.24814002215862274, "compression_ratio": 1.7941176891326904, "end": 215.8800048828125, "no_speech_prob": 0.0015486746560782194, "seek": 18916, "start": 213.8800048828125, "temperature": 0.0, "text": " the more the more", "tokens": [51600, 264, 544, 264, 544, 51700]}, {"id": 50, "avg_logprob": -0.3275129199028015, "compression_ratio": 1.7401961088180542, "end": 218.24000549316406, "no_speech_prob": 0.00030060805147513747, "seek": 21588, "start": 216.67999267578125, "temperature": 0.0, "text": " Materially and concretely", "tokens": [50404, 19188, 2270, 293, 39481, 736, 50482]}, {"id": 51, "avg_logprob": -0.3275129199028015, "compression_ratio": 1.7401961088180542, "end": 223.10000610351562, "no_speech_prob": 0.00030060805147513747, "seek": 21588, "start": 218.24000549316406, "temperature": 0.0, "text": " Definable the reasons are you're together the worst they are the ineffable is actually where the sort of true love lies", "tokens": [50482, 9548, 259, 712, 264, 4112, 366, 291, 434, 1214, 264, 5855, 436, 366, 264, 7167, 602, 712, 307, 767, 689, 264, 1333, 295, 2074, 959, 9134, 50725]}, {"id": 52, "avg_logprob": -0.3275129199028015, "compression_ratio": 1.7401961088180542, "end": 228.55999755859375, "no_speech_prob": 0.00030060805147513747, "seek": 21588, "start": 223.39999389648438, "temperature": 0.0, "text": " Because real love is a form of unity. It's a form of connection. It's connecting spirits", "tokens": [50740, 1436, 957, 959, 307, 257, 1254, 295, 18205, 13, 467, 311, 257, 1254, 295, 4984, 13, 467, 311, 11015, 16388, 50998]}, {"id": 53, "avg_logprob": -0.3275129199028015, "compression_ratio": 1.7401961088180542, "end": 233.97999572753906, "no_speech_prob": 0.00030060805147513747, "seek": 21588, "start": 228.55999755859375, "temperature": 0.0, "text": " Oh you to my consciousness meets your consciousness. It's a it the the the underlying drive", "tokens": [50998, 876, 291, 281, 452, 10081, 13961, 428, 10081, 13, 467, 311, 257, 309, 264, 264, 264, 14217, 3332, 51269]}, {"id": 54, "avg_logprob": -0.3275129199028015, "compression_ratio": 1.7401961088180542, "end": 236.83999633789062, "no_speech_prob": 0.00030060805147513747, "seek": 21588, "start": 234.72000122070312, "temperature": 0.0, "text": " in love in art in", "tokens": [51306, 294, 959, 294, 1523, 294, 51412]}, {"id": 55, "avg_logprob": -0.3275129199028015, "compression_ratio": 1.7401961088180542, "end": 240.36000061035156, "no_speech_prob": 0.00030060805147513747, "seek": 21588, "start": 238.36000061035156, "temperature": 0.0, "text": " Science in", "tokens": [51488, 8976, 294, 51588]}, {"id": 56, "avg_logprob": -0.27741777896881104, "compression_ratio": 1.7878787517547607, "end": 242.24000549316406, "no_speech_prob": 0.013017818331718445, "seek": 24036, "start": 240.47999572753906, "temperature": 0.0, "text": " Mysticism is the desire for unity", "tokens": [50370, 28510, 26356, 307, 264, 7516, 337, 18205, 50458]}, {"id": 57, "avg_logprob": -0.27741777896881104, "compression_ratio": 1.7878787517547607, "end": 247.72000122070312, "no_speech_prob": 0.013017818331718445, "seek": 24036, "start": 242.24000549316406, "temperature": 0.0, "text": " It's a desire for connection as you know bore has famously wrote in every human", "tokens": [50458, 467, 311, 257, 7516, 337, 4984, 382, 291, 458, 26002, 575, 34360, 4114, 294, 633, 1952, 50732]}, {"id": 58, "avg_logprob": -0.27741777896881104, "compression_ratio": 1.7878787517547607, "end": 253.47999572753906, "no_speech_prob": 0.013017818331718445, "seek": 24036, "start": 247.72000122070312, "temperature": 0.0, "text": " There's a sense of something infinite has been lost. You know, there's a god-shaped hole in you. You're trying to fill and", "tokens": [50732, 821, 311, 257, 2020, 295, 746, 13785, 575, 668, 2731, 13, 509, 458, 11, 456, 311, 257, 3044, 12, 23103, 5458, 294, 291, 13, 509, 434, 1382, 281, 2836, 293, 51020]}, {"id": 59, "avg_logprob": -0.27741777896881104, "compression_ratio": 1.7878787517547607, "end": 260.3599853515625, "no_speech_prob": 0.013017818331718445, "seek": 24036, "start": 254.32000732421875, "temperature": 0.0, "text": " So we're always trying to find that connection. <PERSON> is trying to find it in one other person and saying, okay, you're male", "tokens": [51062, 407, 321, 434, 1009, 1382, 281, 915, 300, 4984, 13, 5956, 307, 1382, 281, 915, 309, 294, 472, 661, 954, 293, 1566, 11, 1392, 11, 291, 434, 7133, 51364]}, {"id": 60, "avg_logprob": -0.27741777896881104, "compression_ratio": 1.7878787517547607, "end": 265.239990234375, "no_speech_prob": 0.013017818331718445, "seek": 24036, "start": 260.3599853515625, "temperature": 0.0, "text": " I'm female or whatever and you know, whatever your predilections are and not now we connect now we form a whole", "tokens": [51364, 286, 478, 6556, 420, 2035, 293, 291, 458, 11, 2035, 428, 3852, 794, 3916, 366, 293, 406, 586, 321, 1745, 586, 321, 1254, 257, 1379, 51608]}, {"id": 61, "avg_logprob": -0.28053978085517883, "compression_ratio": 1.7673611640930176, "end": 266.8800048828125, "no_speech_prob": 0.0050584664568305016, "seek": 26524, "start": 265.760009765625, "temperature": 0.0, "text": " a", "tokens": [50390, 257, 50446]}, {"id": 62, "avg_logprob": -0.28053978085517883, "compression_ratio": 1.7673611640930176, "end": 273.6000061035156, "no_speech_prob": 0.0050584664568305016, "seek": 26524, "start": 266.8800048828125, "temperature": 0.0, "text": " Connected whole or in mysticism. It's like it's all about. Okay, sit down meditate and you'll feel the whole in science. It's like oh", "tokens": [50446, 11653, 292, 1379, 420, 294, 9111, 26356, 13, 467, 311, 411, 309, 311, 439, 466, 13, 1033, 11, 1394, 760, 29989, 293, 291, 603, 841, 264, 1379, 294, 3497, 13, 467, 311, 411, 1954, 50782]}, {"id": 63, "avg_logprob": -0.28053978085517883, "compression_ratio": 1.7673611640930176, "end": 275.760009765625, "no_speech_prob": 0.0050584664568305016, "seek": 26524, "start": 274.3999938964844, "temperature": 0.0, "text": " You know", "tokens": [50822, 509, 458, 50890]}, {"id": 64, "avg_logprob": -0.28053978085517883, "compression_ratio": 1.7673611640930176, "end": 282.6000061035156, "no_speech_prob": 0.0050584664568305016, "seek": 26524, "start": 275.760009765625, "temperature": 0.0, "text": " Atoms bouncing is mechanics, but that generates heat so thermodynamics and motion or kinetics or one combined theory", "tokens": [50890, 1711, 4785, 27380, 307, 12939, 11, 457, 300, 23815, 3738, 370, 8810, 35483, 293, 5394, 420, 15784, 15793, 420, 472, 9354, 5261, 51232]}, {"id": 65, "avg_logprob": -0.28053978085517883, "compression_ratio": 1.7673611640930176, "end": 287.0799865722656, "no_speech_prob": 0.0050584664568305016, "seek": 26524, "start": 282.6000061035156, "temperature": 0.0, "text": " That's a whole electricity and magnetism or one thing. That's that's the whole creates that sense of awe", "tokens": [51232, 663, 311, 257, 1379, 10356, 293, 15211, 1434, 420, 472, 551, 13, 663, 311, 300, 311, 264, 1379, 7829, 300, 2020, 295, 30912, 51456]}, {"id": 66, "avg_logprob": -0.28053978085517883, "compression_ratio": 1.7673611640930176, "end": 289.9200134277344, "no_speech_prob": 0.0050584664568305016, "seek": 26524, "start": 287.55999755859375, "temperature": 0.0, "text": " In art, it's like I feel an emotion", "tokens": [51480, 682, 1523, 11, 309, 311, 411, 286, 841, 364, 8913, 51598]}, {"id": 67, "avg_logprob": -0.28053978085517883, "compression_ratio": 1.7673611640930176, "end": 295.20001220703125, "no_speech_prob": 0.0050584664568305016, "seek": 26524, "start": 289.9200134277344, "temperature": 0.0, "text": " I create a piece of art around it and then you see that painting or you see the Sistine Chapel or you read", "tokens": [51598, 286, 1884, 257, 2522, 295, 1523, 926, 309, 293, 550, 291, 536, 300, 5370, 420, 291, 536, 264, 318, 42745, 48203, 420, 291, 1401, 51862]}, {"id": 68, "avg_logprob": -0.26233839988708496, "compression_ratio": 1.69140625, "end": 300.0, "no_speech_prob": 0.006289468612521887, "seek": 29520, "start": 295.20001220703125, "temperature": 0.0, "text": " The poem and you feel that emotion. So again, it's it's creating unity. It's creating connection", "tokens": [50364, 440, 13065, 293, 291, 841, 300, 8913, 13, 407, 797, 11, 309, 311, 309, 311, 4084, 18205, 13, 467, 311, 4084, 4984, 50604]}, {"id": 69, "avg_logprob": -0.26233839988708496, "compression_ratio": 1.69140625, "end": 303.0799865722656, "no_speech_prob": 0.006289468612521887, "seek": 29520, "start": 300.44000244140625, "temperature": 0.0, "text": " and I think everybody craves that and so", "tokens": [50626, 293, 286, 519, 2201, 2094, 977, 300, 293, 370, 50758]}, {"id": 70, "avg_logprob": -0.26233839988708496, "compression_ratio": 1.69140625, "end": 310.3599853515625, "no_speech_prob": 0.006289468612521887, "seek": 29520, "start": 303.5199890136719, "temperature": 0.0, "text": " When you really love somebody it's because you you feel a sense of wholeness by being around them", "tokens": [50780, 1133, 291, 534, 959, 2618, 309, 311, 570, 291, 291, 841, 257, 2020, 295, 315, 11940, 442, 538, 885, 926, 552, 51122]}, {"id": 71, "avg_logprob": -0.26233839988708496, "compression_ratio": 1.69140625, "end": 315.32000732421875, "no_speech_prob": 0.006289468612521887, "seek": 29520, "start": 310.8399963378906, "temperature": 0.0, "text": " And that sense of wholeness probably doesn't have anything to do with what school they went to", "tokens": [51146, 400, 300, 2020, 295, 315, 11940, 442, 1391, 1177, 380, 362, 1340, 281, 360, 365, 437, 1395, 436, 1437, 281, 51370]}, {"id": 72, "avg_logprob": -0.26233839988708496, "compression_ratio": 1.69140625, "end": 318.0, "no_speech_prob": 0.006289468612521887, "seek": 29520, "start": 316.0, "temperature": 0.0, "text": " You know or what career they're in?", "tokens": [51404, 509, 458, 420, 437, 3988, 436, 434, 294, 30, 51504]}, {"id": 73, "avg_logprob": -0.26233839988708496, "compression_ratio": 1.69140625, "end": 322.1199951171875, "no_speech_prob": 0.006289468612521887, "seek": 29520, "start": 318.32000732421875, "temperature": 0.0, "text": " Just sort of tying that into the life is short. Stop fucking about", "tokens": [51520, 1449, 1333, 295, 32405, 300, 666, 264, 993, 307, 2099, 13, 5535, 5546, 466, 51710]}, {"id": 74, "avg_logprob": -0.2961427569389343, "compression_ratio": 1.5983606576919556, "end": 328.44000244140625, "no_speech_prob": 0.05339639261364937, "seek": 32212, "start": 323.0, "temperature": 0.0, "text": " If you're faced with a difficult choice and you cannot decide the answer is no and the reason is modern society is full of options", "tokens": [50408, 759, 291, 434, 11446, 365, 257, 2252, 3922, 293, 291, 2644, 4536, 264, 1867, 307, 572, 293, 264, 1778, 307, 4363, 4086, 307, 1577, 295, 3956, 50680]}, {"id": 75, "avg_logprob": -0.2961427569389343, "compression_ratio": 1.5983606576919556, "end": 330.6400146484375, "no_speech_prob": 0.05339639261364937, "seek": 32212, "start": 328.6400146484375, "temperature": 0.0, "text": " Yeah, knowing this rationally", "tokens": [50690, 865, 11, 5276, 341, 24258, 379, 50790]}, {"id": 76, "avg_logprob": -0.2961427569389343, "compression_ratio": 1.5983606576919556, "end": 333.3599853515625, "no_speech_prob": 0.05339639261364937, "seek": 32212, "start": 331.3599853515625, "temperature": 0.0, "text": " Sounds sounds great", "tokens": [50826, 14576, 3263, 869, 50926]}, {"id": 77, "avg_logprob": -0.2961427569389343, "compression_ratio": 1.5983606576919556, "end": 335.8800048828125, "no_speech_prob": 0.05339639261364937, "seek": 32212, "start": 333.3599853515625, "temperature": 0.0, "text": " but having the courage to", "tokens": [50926, 457, 1419, 264, 9892, 281, 51052]}, {"id": 78, "avg_logprob": -0.2961427569389343, "compression_ratio": 1.5983606576919556, "end": 344.44000244140625, "no_speech_prob": 0.05339639261364937, "seek": 32212, "start": 336.4800109863281, "temperature": 0.0, "text": " Commit to it in reality. I think it's a different task and cutting your losses quickly in the big three relationships jobs and locations is", "tokens": [51082, 3046, 270, 281, 309, 294, 4103, 13, 286, 519, 309, 311, 257, 819, 5633, 293, 6492, 428, 15352, 2661, 294, 264, 955, 1045, 6159, 4782, 293, 9253, 307, 51480]}, {"id": 79, "avg_logprob": -0.2961427569389343, "compression_ratio": 1.5983606576919556, "end": 348.67999267578125, "no_speech_prob": 0.05339639261364937, "seek": 32212, "start": 345.4800109863281, "temperature": 0.0, "text": " hard, what would you say to someone who may", "tokens": [51532, 1152, 11, 437, 576, 291, 584, 281, 1580, 567, 815, 51692]}, {"id": 80, "avg_logprob": -0.31497761607170105, "compression_ratio": 1.6782609224319458, "end": 352.32000732421875, "no_speech_prob": 0.005729044321924448, "seek": 34868, "start": 349.5199890136719, "temperature": 0.0, "text": " <PERSON><PERSON><PERSON><PERSON> be able to agree with you and say I understand", "tokens": [50406, 383, 323, 1443, 379, 312, 1075, 281, 3986, 365, 291, 293, 584, 286, 1223, 50546]}, {"id": 81, "avg_logprob": -0.31497761607170105, "compression_ratio": 1.6782609224319458, "end": 355.9200134277344, "no_speech_prob": 0.005729044321924448, "seek": 34868, "start": 352.8399963378906, "temperature": 0.0, "text": " My cousin said this about me. He said that", "tokens": [50572, 1222, 16207, 848, 341, 466, 385, 13, 634, 848, 300, 50726]}, {"id": 82, "avg_logprob": -0.31497761607170105, "compression_ratio": 1.6782609224319458, "end": 363.20001220703125, "no_speech_prob": 0.005729044321924448, "seek": 34868, "start": 356.55999755859375, "temperature": 0.0, "text": " He said what I really he says what I've really noticed about you is your ability to walk away from situations that are", "tokens": [50758, 634, 848, 437, 286, 534, 415, 1619, 437, 286, 600, 534, 5694, 466, 291, 307, 428, 3485, 281, 1792, 1314, 490, 6851, 300, 366, 51090]}, {"id": 83, "avg_logprob": -0.31497761607170105, "compression_ratio": 1.6782609224319458, "end": 367.7200012207031, "no_speech_prob": 0.005729044321924448, "seek": 34868, "start": 364.20001220703125, "temperature": 0.0, "text": " Just not great enough for you or good not good enough for you", "tokens": [51140, 1449, 406, 869, 1547, 337, 291, 420, 665, 406, 665, 1547, 337, 291, 51316]}, {"id": 84, "avg_logprob": -0.31497761607170105, "compression_ratio": 1.6782609224319458, "end": 373.1199951171875, "no_speech_prob": 0.005729044321924448, "seek": 34868, "start": 367.7200012207031, "temperature": 0.0, "text": " Yes, and I think that is a characteristic that I have. I will not accept second best outcomes in my life", "tokens": [51316, 1079, 11, 293, 286, 519, 300, 307, 257, 16282, 300, 286, 362, 13, 286, 486, 406, 3241, 1150, 1151, 10070, 294, 452, 993, 51586]}, {"id": 85, "avg_logprob": -0.2822064161300659, "compression_ratio": 1.7916666269302368, "end": 375.4800109863281, "no_speech_prob": 0.004680851474404335, "seek": 37312, "start": 373.9599914550781, "temperature": 0.0, "text": " And", "tokens": [50406, 400, 50482]}, {"id": 86, "avg_logprob": -0.2822064161300659, "compression_ratio": 1.7916666269302368, "end": 382.0799865722656, "no_speech_prob": 0.004680851474404335, "seek": 37312, "start": 375.4800109863281, "temperature": 0.0, "text": " Ultimately you will end up wherever is acceptable to you. You will get out of life. Whatever is acceptable to you", "tokens": [50482, 23921, 291, 486, 917, 493, 8660, 307, 15513, 281, 291, 13, 509, 486, 483, 484, 295, 993, 13, 8541, 307, 15513, 281, 291, 50812]}, {"id": 87, "avg_logprob": -0.2822064161300659, "compression_ratio": 1.7916666269302368, "end": 388.67999267578125, "no_speech_prob": 0.004680851474404335, "seek": 37312, "start": 382.760009765625, "temperature": 0.0, "text": " And there are certain things to me that are very very important where I will not settle for second best", "tokens": [50846, 400, 456, 366, 1629, 721, 281, 385, 300, 366, 588, 588, 1021, 689, 286, 486, 406, 11852, 337, 1150, 1151, 51142]}, {"id": 88, "avg_logprob": -0.2822064161300659, "compression_ratio": 1.7916666269302368, "end": 390.3999938964844, "no_speech_prob": 0.004680851474404335, "seek": 37312, "start": 388.67999267578125, "temperature": 0.0, "text": " But then there are a lot of other things", "tokens": [51142, 583, 550, 456, 366, 257, 688, 295, 661, 721, 51228]}, {"id": 89, "avg_logprob": -0.2822064161300659, "compression_ratio": 1.7916666269302368, "end": 393.7200012207031, "no_speech_prob": 0.004680851474404335, "seek": 37312, "start": 390.3999938964844, "temperature": 0.0, "text": " I just don't care about because if I spend all my time caring about those things", "tokens": [51228, 286, 445, 500, 380, 1127, 466, 570, 498, 286, 3496, 439, 452, 565, 15365, 466, 729, 721, 51394]}, {"id": 90, "avg_logprob": -0.2822064161300659, "compression_ratio": 1.7916666269302368, "end": 396.239990234375, "no_speech_prob": 0.004680851474404335, "seek": 37312, "start": 393.7200012207031, "temperature": 0.0, "text": " I don't have the energy for the few things that matter and", "tokens": [51394, 286, 500, 380, 362, 264, 2281, 337, 264, 1326, 721, 300, 1871, 293, 51520]}, {"id": 91, "avg_logprob": -0.2822064161300659, "compression_ratio": 1.7916666269302368, "end": 403.0400085449219, "no_speech_prob": 0.004680851474404335, "seek": 37312, "start": 397.3999938964844, "temperature": 0.0, "text": " So in decision-making of a few heuristics for myself other people can use their own but minor if you can't decide", "tokens": [51578, 407, 294, 3537, 12, 12402, 295, 257, 1326, 415, 374, 6006, 337, 2059, 661, 561, 393, 764, 641, 1065, 457, 6696, 498, 291, 393, 380, 4536, 51860]}, {"id": 92, "avg_logprob": -0.2819826006889343, "compression_ratio": 1.7896995544433594, "end": 409.20001220703125, "no_speech_prob": 0.00024152746482286602, "seek": 40312, "start": 403.44000244140625, "temperature": 0.0, "text": " the answer is no if you're offered an opportunity if you have a new thing that you're saying yes or no to that is a", "tokens": [50380, 264, 1867, 307, 572, 498, 291, 434, 8059, 364, 2650, 498, 291, 362, 257, 777, 551, 300, 291, 434, 1566, 2086, 420, 572, 281, 300, 307, 257, 50668]}, {"id": 93, "avg_logprob": -0.2819826006889343, "compression_ratio": 1.7896995544433594, "end": 410.760009765625, "no_speech_prob": 0.00024152746482286602, "seek": 40312, "start": 409.20001220703125, "temperature": 0.0, "text": " Change from where you're starting", "tokens": [50668, 15060, 490, 689, 291, 434, 2891, 50746]}, {"id": 94, "avg_logprob": -0.2819826006889343, "compression_ratio": 1.7896995544433594, "end": 412.760009765625, "no_speech_prob": 0.00024152746482286602, "seek": 40312, "start": 410.760009765625, "temperature": 0.0, "text": " The answer is by default always no", "tokens": [50746, 440, 1867, 307, 538, 7576, 1009, 572, 50846]}, {"id": 95, "avg_logprob": -0.2819826006889343, "compression_ratio": 1.7896995544433594, "end": 420.0400085449219, "no_speech_prob": 0.00024152746482286602, "seek": 40312, "start": 413.760009765625, "temperature": 0.0, "text": " Secondly if you have two decisions if you have a or B and both seem like very equal", "tokens": [50896, 19483, 498, 291, 362, 732, 5327, 498, 291, 362, 257, 420, 363, 293, 1293, 1643, 411, 588, 2681, 51210]}, {"id": 96, "avg_logprob": -0.2819826006889343, "compression_ratio": 1.7896995544433594, "end": 423.0400085449219, "no_speech_prob": 0.00024152746482286602, "seek": 40312, "start": 420.44000244140625, "temperature": 0.0, "text": " Take the path that's more painful in the short term", "tokens": [51230, 3664, 264, 3100, 300, 311, 544, 11697, 294, 264, 2099, 1433, 51360]}, {"id": 97, "avg_logprob": -0.2819826006889343, "compression_ratio": 1.7896995544433594, "end": 429.6000061035156, "no_speech_prob": 0.00024152746482286602, "seek": 40312, "start": 423.6000061035156, "temperature": 0.0, "text": " The one that's going to be painful immediately because your brain is always trying to avoid pain", "tokens": [51388, 440, 472, 300, 311, 516, 281, 312, 11697, 4258, 570, 428, 3567, 307, 1009, 1382, 281, 5042, 1822, 51688]}, {"id": 98, "avg_logprob": -0.2794385254383087, "compression_ratio": 1.8615916967391968, "end": 435.8800048828125, "no_speech_prob": 0.0028006613720208406, "seek": 42960, "start": 429.8399963378906, "temperature": 0.0, "text": " So any pain that is imminent it is going to treat as much larger than it actually is", "tokens": [50376, 407, 604, 1822, 300, 307, 44339, 309, 307, 516, 281, 2387, 382, 709, 4833, 813, 309, 767, 307, 50678]}, {"id": 99, "avg_logprob": -0.2794385254383087, "compression_ratio": 1.8615916967391968, "end": 439.6400146484375, "no_speech_prob": 0.0028006613720208406, "seek": 42960, "start": 436.20001220703125, "temperature": 0.0, "text": " This is kind of like a decision-making equivalent of Taleb surgeon", "tokens": [50694, 639, 307, 733, 295, 411, 257, 3537, 12, 12402, 10344, 295, 314, 26053, 22913, 50866]}, {"id": 100, "avg_logprob": -0.2794385254383087, "compression_ratio": 1.8615916967391968, "end": 445.0, "no_speech_prob": 0.0028006613720208406, "seek": 42960, "start": 440.8399963378906, "temperature": 0.0, "text": " Tell a surgeon whether you want the surgeon doesn't look as good because he's more likely to be a good surgeon", "tokens": [50926, 5115, 257, 22913, 1968, 291, 528, 264, 22913, 1177, 380, 574, 382, 665, 570, 415, 311, 544, 3700, 281, 312, 257, 665, 22913, 51134]}, {"id": 101, "avg_logprob": -0.2794385254383087, "compression_ratio": 1.8615916967391968, "end": 449.3399963378906, "no_speech_prob": 0.0028006613720208406, "seek": 42960, "start": 445.0, "temperature": 0.0, "text": " Yeah, it's similar in that appearances are deceiving because you're avoiding conflict you're avoiding pain", "tokens": [51134, 865, 11, 309, 311, 2531, 294, 300, 29174, 366, 14088, 2123, 570, 291, 434, 20220, 6596, 291, 434, 20220, 1822, 51351]}, {"id": 102, "avg_logprob": -0.2794385254383087, "compression_ratio": 1.8615916967391968, "end": 452.0799865722656, "no_speech_prob": 0.0028006613720208406, "seek": 42960, "start": 449.3999938964844, "temperature": 0.0, "text": " So take the path is more painful in the short term", "tokens": [51354, 407, 747, 264, 3100, 307, 544, 11697, 294, 264, 2099, 1433, 51488]}, {"id": 103, "avg_logprob": -0.2794385254383087, "compression_ratio": 1.8615916967391968, "end": 457.5199890136719, "no_speech_prob": 0.0028006613720208406, "seek": 42960, "start": 452.32000732421875, "temperature": 0.0, "text": " Because your brain is creating this illusion the short-term pain is greater than the long-term pain because long-term", "tokens": [51500, 1436, 428, 3567, 307, 4084, 341, 18854, 264, 2099, 12, 7039, 1822, 307, 5044, 813, 264, 938, 12, 7039, 1822, 570, 938, 12, 7039, 51760]}, {"id": 104, "avg_logprob": -0.3657192587852478, "compression_ratio": 1.7560137510299683, "end": 461.7200012207031, "no_speech_prob": 0.00048019058885984123, "seek": 45752, "start": 457.5199890136719, "temperature": 0.0, "text": " Yeah, you'll commit your future self to all kinds of long-term. I don't know manana. Exactly manana", "tokens": [50364, 865, 11, 291, 603, 5599, 428, 2027, 2698, 281, 439, 3685, 295, 938, 12, 7039, 13, 286, 500, 380, 458, 587, 2095, 13, 7587, 587, 2095, 50574]}, {"id": 105, "avg_logprob": -0.3657192587852478, "compression_ratio": 1.7560137510299683, "end": 464.8399963378906, "no_speech_prob": 0.00048019058885984123, "seek": 45752, "start": 462.3599853515625, "temperature": 0.0, "text": " So take the more short-term painful one", "tokens": [50606, 407, 747, 264, 544, 2099, 12, 7039, 11697, 472, 50730]}, {"id": 106, "avg_logprob": -0.3657192587852478, "compression_ratio": 1.7560137510299683, "end": 469.0, "no_speech_prob": 0.00048019058885984123, "seek": 45752, "start": 464.8399963378906, "temperature": 0.0, "text": " and then finally the last one which I would try to couple <PERSON> with is that", "tokens": [50730, 293, 550, 2721, 264, 1036, 472, 597, 286, 576, 853, 281, 1916, 2694, 47366, 365, 307, 300, 50938]}, {"id": 107, "avg_logprob": -0.3657192587852478, "compression_ratio": 1.7560137510299683, "end": 473.5, "no_speech_prob": 0.00048019058885984123, "seek": 45752, "start": 469.2799987792969, "temperature": 0.0, "text": " You want to check it take the choice that will leave you more", "tokens": [50952, 509, 528, 281, 1520, 309, 747, 264, 3922, 300, 486, 1856, 291, 544, 51163]}, {"id": 108, "avg_logprob": -0.3657192587852478, "compression_ratio": 1.7560137510299683, "end": 478.239990234375, "no_speech_prob": 0.00048019058885984123, "seek": 45752, "start": 473.8399963378906, "temperature": 0.0, "text": " Equanimous in the long term by quantum assuming is like more peace more mental peace in the long term", "tokens": [51180, 15624, 17869, 563, 294, 264, 938, 1433, 538, 13018, 1256, 24919, 307, 411, 544, 4336, 544, 4973, 4336, 294, 264, 938, 1433, 51400]}, {"id": 109, "avg_logprob": -0.3657192587852478, "compression_ratio": 1.7560137510299683, "end": 484.32000732421875, "no_speech_prob": 0.00048019058885984123, "seek": 45752, "start": 478.239990234375, "temperature": 0.0, "text": " so whatever clears your mind more and will have you having less self-talk in the future if you can model that out that is probably", "tokens": [51400, 370, 2035, 47033, 428, 1575, 544, 293, 486, 362, 291, 1419, 1570, 2698, 12, 29302, 294, 264, 2027, 498, 291, 393, 2316, 300, 484, 300, 307, 1391, 51704]}, {"id": 110, "avg_logprob": -0.31634899973869324, "compression_ratio": 1.8533333539962769, "end": 491.0400085449219, "no_speech_prob": 0.011863385327160358, "seek": 48432, "start": 484.32000732421875, "temperature": 0.0, "text": " the better route to go and then I would focus decision-making down on the three things that really matter because everything else is", "tokens": [50364, 264, 1101, 7955, 281, 352, 293, 550, 286, 576, 1879, 3537, 12, 12402, 760, 322, 264, 1045, 721, 300, 534, 1871, 570, 1203, 1646, 307, 50700]}, {"id": 111, "avg_logprob": -0.31634899973869324, "compression_ratio": 1.8533333539962769, "end": 495.6400146484375, "no_speech_prob": 0.011863385327160358, "seek": 48432, "start": 491.1199951171875, "temperature": 0.0, "text": " Downstream of these states these three decisions, especially these are early life decisions later in life", "tokens": [50704, 9506, 9291, 295, 613, 4368, 613, 1045, 5327, 11, 2318, 613, 366, 2440, 993, 5327, 1780, 294, 993, 50930]}, {"id": 112, "avg_logprob": -0.31634899973869324, "compression_ratio": 1.8533333539962769, "end": 499.9599914550781, "no_speech_prob": 0.011863385327160358, "seek": 48432, "start": 495.6400146484375, "temperature": 0.0, "text": " You have different things to optimize for but early in life. You're trying to figure out who you're with", "tokens": [50930, 509, 362, 819, 721, 281, 19719, 337, 457, 2440, 294, 993, 13, 509, 434, 1382, 281, 2573, 484, 567, 291, 434, 365, 51146]}, {"id": 113, "avg_logprob": -0.31634899973869324, "compression_ratio": 1.8533333539962769, "end": 506.239990234375, "no_speech_prob": 0.011863385327160358, "seek": 48432, "start": 500.5199890136719, "temperature": 0.0, "text": " What you're doing and where you live and I think on all three of those you want to think you want to think pretty hard", "tokens": [51174, 708, 291, 434, 884, 293, 689, 291, 1621, 293, 286, 519, 322, 439, 1045, 295, 729, 291, 528, 281, 519, 291, 528, 281, 519, 1238, 1152, 51460]}, {"id": 114, "avg_logprob": -0.31634899973869324, "compression_ratio": 1.8533333539962769, "end": 511.67999267578125, "no_speech_prob": 0.011863385327160358, "seek": 48432, "start": 506.239990234375, "temperature": 0.0, "text": " About it and people do some of these unconsciously, you know who you're with very often cycle", "tokens": [51460, 7769, 309, 293, 561, 360, 512, 295, 613, 18900, 356, 11, 291, 458, 567, 291, 434, 365, 588, 2049, 6586, 51732]}, {"id": 115, "avg_logprob": -0.3353889286518097, "compression_ratio": 1.7129032611846924, "end": 516.3599853515625, "no_speech_prob": 0.12932942807674408, "seek": 51168, "start": 511.67999267578125, "temperature": 0.0, "text": " We were in a relationship with stumble along. It felt okay. I've been enough time. So we got married, right?", "tokens": [50364, 492, 645, 294, 257, 2480, 365, 41302, 2051, 13, 467, 2762, 1392, 13, 286, 600, 668, 1547, 565, 13, 407, 321, 658, 5259, 11, 558, 30, 50598]}, {"id": 116, "avg_logprob": -0.3353889286518097, "compression_ratio": 1.7129032611846924, "end": 519.2000122070312, "no_speech_prob": 0.12932942807674408, "seek": 51168, "start": 517.3200073242188, "temperature": 0.0, "text": " not great reasons", "tokens": [50646, 406, 869, 4112, 50740]}, {"id": 117, "avg_logprob": -0.3353889286518097, "compression_ratio": 1.7129032611846924, "end": 523.47998046875, "no_speech_prob": 0.12932942807674408, "seek": 51168, "start": 519.2000122070312, "temperature": 0.0, "text": " Maybe not terrible reasons either means people who overthink these things sometimes don't get the right answer", "tokens": [50740, 2704, 406, 6237, 4112, 2139, 1355, 561, 567, 670, 21074, 613, 721, 2171, 500, 380, 483, 264, 558, 1867, 50954]}, {"id": 118, "avg_logprob": -0.3353889286518097, "compression_ratio": 1.7129032611846924, "end": 529.5599975585938, "no_speech_prob": 0.12932942807674408, "seek": 51168, "start": 523.47998046875, "temperature": 0.0, "text": " But maybe here if you are the kind of person that's not going to settle for second best you iterate you iterate on a closed", "tokens": [50954, 583, 1310, 510, 498, 291, 366, 264, 733, 295, 954, 300, 311, 406, 516, 281, 11852, 337, 1150, 1151, 291, 44497, 291, 44497, 322, 257, 5395, 51258]}, {"id": 119, "avg_logprob": -0.3353889286518097, "compression_ratio": 1.7129032611846924, "end": 532.2000122070312, "no_speech_prob": 0.12932942807674408, "seek": 51168, "start": 529.5599975585938, "temperature": 0.0, "text": " Time frame so you don't run out the clock and then you decide", "tokens": [51258, 6161, 3920, 370, 291, 500, 380, 1190, 484, 264, 7830, 293, 550, 291, 4536, 51390]}, {"id": 120, "avg_logprob": -0.3353889286518097, "compression_ratio": 1.7129032611846924, "end": 539.1599731445312, "no_speech_prob": 0.12932942807674408, "seek": 51168, "start": 532.8800048828125, "temperature": 0.0, "text": " On what you do you try a whole bunch of different things until you find the one that feels like play to you", "tokens": [51424, 1282, 437, 291, 360, 291, 853, 257, 1379, 3840, 295, 819, 721, 1826, 291, 915, 264, 472, 300, 3417, 411, 862, 281, 291, 51738]}, {"id": 121, "avg_logprob": -0.23908568918704987, "compression_ratio": 1.7902097702026367, "end": 541.1599731445312, "no_speech_prob": 0.003324001794680953, "seek": 53916, "start": 539.1599731445312, "temperature": 0.0, "text": " Looks like work to others. You can't lose at it", "tokens": [50364, 10027, 411, 589, 281, 2357, 13, 509, 393, 380, 3624, 412, 309, 50464]}, {"id": 122, "avg_logprob": -0.23908568918704987, "compression_ratio": 1.7902097702026367, "end": 547.5599975585938, "no_speech_prob": 0.003324001794680953, "seek": 53916, "start": 541.8400268554688, "temperature": 0.0, "text": " Get some leverage try to find some practical application of it and go into that and then where you live", "tokens": [50498, 3240, 512, 13982, 853, 281, 915, 512, 8496, 3861, 295, 309, 293, 352, 666, 300, 293, 550, 689, 291, 1621, 50784]}, {"id": 123, "avg_logprob": -0.23908568918704987, "compression_ratio": 1.7902097702026367, "end": 551.1199951171875, "no_speech_prob": 0.003324001794680953, "seek": 53916, "start": 548.1599731445312, "temperature": 0.0, "text": " Where you live is really important. I don't think people spend enough time on that one", "tokens": [50814, 2305, 291, 1621, 307, 534, 1021, 13, 286, 500, 380, 519, 561, 3496, 1547, 565, 322, 300, 472, 50962]}, {"id": 124, "avg_logprob": -0.23908568918704987, "compression_ratio": 1.7902097702026367, "end": 556.9199829101562, "no_speech_prob": 0.003324001794680953, "seek": 53916, "start": 551.1199951171875, "temperature": 0.0, "text": " I think people pick cities randomly based on where I went to school or where my family happened to be or where", "tokens": [50962, 286, 519, 561, 1888, 6486, 16979, 2361, 322, 689, 286, 1437, 281, 1395, 420, 689, 452, 1605, 2011, 281, 312, 420, 689, 51252]}, {"id": 125, "avg_logprob": -0.23908568918704987, "compression_ratio": 1.7902097702026367, "end": 560.2000122070312, "no_speech_prob": 0.003324001794680953, "seek": 53916, "start": 557.280029296875, "temperature": 0.0, "text": " My friend was I visited one weekend. I really liked it", "tokens": [51270, 1222, 1277, 390, 286, 11220, 472, 6711, 13, 286, 534, 4501, 309, 51416]}, {"id": 126, "avg_logprob": -0.23908568918704987, "compression_ratio": 1.7902097702026367, "end": 565.5599975585938, "no_speech_prob": 0.003324001794680953, "seek": 53916, "start": 560.2000122070312, "temperature": 0.0, "text": " You really want to think it through because where you live really constrains and defines your opportunities", "tokens": [51416, 509, 534, 528, 281, 519, 309, 807, 570, 689, 291, 1621, 534, 11525, 1292, 293, 23122, 428, 4786, 51684]}, {"id": 127, "avg_logprob": -0.2868790626525879, "compression_ratio": 2.0, "end": 567.280029296875, "no_speech_prob": 0.00312304450199008, "seek": 56556, "start": 566.2000122070312, "temperature": 0.0, "text": " It", "tokens": [50396, 467, 50450]}, {"id": 128, "avg_logprob": -0.2868790626525879, "compression_ratio": 2.0, "end": 572.4000244140625, "no_speech_prob": 0.00312304450199008, "seek": 56556, "start": 567.280029296875, "temperature": 0.0, "text": " It's going to determine your friend circle. It's going to determine your dating pool. It's going to turn your job opportunities", "tokens": [50450, 467, 311, 516, 281, 6997, 428, 1277, 6329, 13, 467, 311, 516, 281, 6997, 428, 10689, 7005, 13, 467, 311, 516, 281, 1261, 428, 1691, 4786, 50706]}, {"id": 129, "avg_logprob": -0.2868790626525879, "compression_ratio": 2.0, "end": 575.6799926757812, "no_speech_prob": 0.00312304450199008, "seek": 56556, "start": 572.4000244140625, "temperature": 0.0, "text": " It's going to determine the food and air and water quality that you receive", "tokens": [50706, 467, 311, 516, 281, 6997, 264, 1755, 293, 1988, 293, 1281, 3125, 300, 291, 4774, 50870]}, {"id": 130, "avg_logprob": -0.2868790626525879, "compression_ratio": 2.0, "end": 581.0599975585938, "no_speech_prob": 0.00312304450199008, "seek": 56556, "start": 576.3200073242188, "temperature": 0.0, "text": " It's going to determine your proximity to your family, which might be important as you get older and have kids", "tokens": [50902, 467, 311, 516, 281, 6997, 428, 27632, 281, 428, 1605, 11, 597, 1062, 312, 1021, 382, 291, 483, 4906, 293, 362, 2301, 51139]}, {"id": 131, "avg_logprob": -0.2868790626525879, "compression_ratio": 2.0, "end": 588.0399780273438, "no_speech_prob": 0.00312304450199008, "seek": 56556, "start": 581.2000122070312, "temperature": 0.0, "text": " so very very very important decision whether you know quality of life how much you stay inside or outside and how long you'll live based", "tokens": [51146, 370, 588, 588, 588, 1021, 3537, 1968, 291, 458, 3125, 295, 993, 577, 709, 291, 1754, 1854, 420, 2380, 293, 577, 938, 291, 603, 1621, 2361, 51488]}, {"id": 132, "avg_logprob": -0.2868790626525879, "compression_ratio": 2.0, "end": 593.8400268554688, "no_speech_prob": 0.00312304450199008, "seek": 56556, "start": 588.0399780273438, "temperature": 0.0, "text": " on that and I think people choose that one probably more poorly than they put a lot less thought into that one than the", "tokens": [51488, 322, 300, 293, 286, 519, 561, 2826, 300, 472, 1391, 544, 22271, 813, 436, 829, 257, 688, 1570, 1194, 666, 300, 472, 813, 264, 51778]}, {"id": 133, "avg_logprob": -0.3799530863761902, "compression_ratio": 1.6913182735443115, "end": 595.5599975585938, "no_speech_prob": 0.00806148536503315, "seek": 59384, "start": 593.8400268554688, "temperature": 0.0, "text": " other two", "tokens": [50364, 661, 732, 50450]}, {"id": 134, "avg_logprob": -0.3799530863761902, "compression_ratio": 1.6913182735443115, "end": 601.4000244140625, "no_speech_prob": 0.00806148536503315, "seek": 59384, "start": 595.5599975585938, "temperature": 0.0, "text": " In some ways. Yeah, but also the you're so right how many people fall backward into a relationship and before they know it", "tokens": [50450, 682, 512, 2098, 13, 865, 11, 457, 611, 264, 291, 434, 370, 558, 577, 867, 561, 2100, 23897, 666, 257, 2480, 293, 949, 436, 458, 309, 50742]}, {"id": 135, "avg_logprob": -0.3799530863761902, "compression_ratio": 1.6913182735443115, "end": 603.3200073242188, "no_speech_prob": 0.00806148536503315, "seek": 59384, "start": 601.8400268554688, "temperature": 0.0, "text": " We're living together. We got a dog", "tokens": [50764, 492, 434, 2647, 1214, 13, 492, 658, 257, 3000, 50838]}, {"id": 136, "avg_logprob": -0.3799530863761902, "compression_ratio": 1.6913182735443115, "end": 605.4400024414062, "no_speech_prob": 0.00806148536503315, "seek": 59384, "start": 603.3200073242188, "temperature": 0.0, "text": " We got a kid we would marry with you and yeah", "tokens": [50838, 492, 658, 257, 1636, 321, 576, 9747, 365, 291, 293, 1338, 50944]}, {"id": 137, "avg_logprob": -0.3799530863761902, "compression_ratio": 1.6913182735443115, "end": 608.719970703125, "no_speech_prob": 0.00806148536503315, "seek": 59384, "start": 605.4400024414062, "temperature": 0.0, "text": " And then when you have kids because then that's half of you and half of them running around", "tokens": [50944, 400, 550, 562, 291, 362, 2301, 570, 550, 300, 311, 1922, 295, 291, 293, 1922, 295, 552, 2614, 926, 51108]}, {"id": 138, "avg_logprob": -0.3799530863761902, "compression_ratio": 1.6913182735443115, "end": 610.3599853515625, "no_speech_prob": 0.00806148536503315, "seek": 59384, "start": 608.719970703125, "temperature": 0.0, "text": " You're never gonna separate yourself from that", "tokens": [51108, 509, 434, 1128, 799, 4994, 1803, 490, 300, 51190]}, {"id": 139, "avg_logprob": -0.3799530863761902, "compression_ratio": 1.6913182735443115, "end": 615.760009765625, "no_speech_prob": 0.00806148536503315, "seek": 59384, "start": 610.3599853515625, "temperature": 0.0, "text": " So once you have a child with somebody then the most important thing in the world to you is half that other person", "tokens": [51190, 407, 1564, 291, 362, 257, 1440, 365, 2618, 550, 264, 881, 1021, 551, 294, 264, 1002, 281, 291, 307, 1922, 300, 661, 954, 51460]}, {"id": 140, "avg_logprob": -0.3799530863761902, "compression_ratio": 1.6913182735443115, "end": 618.239990234375, "no_speech_prob": 0.00806148536503315, "seek": 59384, "start": 616.0, "temperature": 0.0, "text": " Whether you like them or not. Mm-hmm. Yeah", "tokens": [51472, 8503, 291, 411, 552, 420, 406, 13, 8266, 12, 10250, 13, 865, 51584]}, {"id": 141, "avg_logprob": -0.3799530863761902, "compression_ratio": 1.6913182735443115, "end": 620.9199829101562, "no_speech_prob": 0.00806148536503315, "seek": 59384, "start": 619.6799926757812, "temperature": 0.0, "text": " <PERSON>", "tokens": [51656, 28721, 16932, 51718]}, {"id": 142, "avg_logprob": -0.2963368892669678, "compression_ratio": 1.7418301105499268, "end": 624.0, "no_speech_prob": 0.1460690051317215, "seek": 62092, "start": 620.9199829101562, "temperature": 0.0, "text": " Had a tweet a long time ago that I always think about and he said", "tokens": [50364, 12298, 257, 15258, 257, 938, 565, 2057, 300, 286, 1009, 519, 466, 293, 415, 848, 50518]}, {"id": 143, "avg_logprob": -0.2963368892669678, "compression_ratio": 1.7418301105499268, "end": 628.280029296875, "no_speech_prob": 0.1460690051317215, "seek": 62092, "start": 624.4000244140625, "temperature": 0.0, "text": " Every parenting book in the world could be replaced with one book on behavioral genetics", "tokens": [50538, 2048, 30896, 1446, 294, 264, 1002, 727, 312, 10772, 365, 472, 1446, 322, 19124, 26516, 50732]}, {"id": 144, "avg_logprob": -0.2963368892669678, "compression_ratio": 1.7418301105499268, "end": 631.47998046875, "no_speech_prob": 0.1460690051317215, "seek": 62092, "start": 628.760009765625, "temperature": 0.0, "text": " But I'm a big believer in genetics. Yes", "tokens": [50756, 583, 286, 478, 257, 955, 23892, 294, 26516, 13, 1079, 50892]}, {"id": 145, "avg_logprob": -0.2963368892669678, "compression_ratio": 1.7418301105499268, "end": 638.0, "no_speech_prob": 0.1460690051317215, "seek": 62092, "start": 631.47998046875, "temperature": 0.0, "text": " I do think a lot of behavior is downstream of genetics and I think we underplay that we like to overplay nurture and underplay nurture", "tokens": [50892, 286, 360, 519, 257, 688, 295, 5223, 307, 30621, 295, 26516, 293, 286, 519, 321, 833, 2858, 300, 321, 411, 281, 670, 2858, 41451, 293, 833, 2858, 41451, 51218]}, {"id": 146, "avg_logprob": -0.2963368892669678, "compression_ratio": 1.7418301105499268, "end": 642.3200073242188, "no_speech_prob": 0.1460690051317215, "seek": 62092, "start": 638.1199951171875, "temperature": 0.0, "text": " For a sort of say well underplay nature for societal reasons, but nature is a big deal", "tokens": [51224, 1171, 257, 1333, 295, 584, 731, 833, 2858, 3687, 337, 33472, 4112, 11, 457, 3687, 307, 257, 955, 2028, 51434]}, {"id": 147, "avg_logprob": -0.2963368892669678, "compression_ratio": 1.7418301105499268, "end": 647.6400146484375, "no_speech_prob": 0.1460690051317215, "seek": 62092, "start": 642.9600219726562, "temperature": 0.0, "text": " The temperament of the person you marry is probably going to be reflected in your child by default people can change", "tokens": [51466, 440, 3393, 2466, 295, 264, 954, 291, 9747, 307, 1391, 516, 281, 312, 15502, 294, 428, 1440, 538, 7576, 561, 393, 1319, 51700]}, {"id": 148, "avg_logprob": -0.3236851692199707, "compression_ratio": 1.791277289390564, "end": 649.6400146484375, "no_speech_prob": 0.044012073427438736, "seek": 64764, "start": 647.6400146484375, "temperature": 0.0, "text": " Securely-attached kid pick a securely-attached partner", "tokens": [50364, 3306, 540, 356, 12, 1591, 15095, 1636, 1888, 257, 38348, 12, 1591, 15095, 4975, 50464]}, {"id": 149, "avg_logprob": -0.3236851692199707, "compression_ratio": 1.791277289390564, "end": 655.47998046875, "no_speech_prob": 0.044012073427438736, "seek": 64764, "start": 650.3599853515625, "temperature": 0.0, "text": " Well, the secret to a happy relationship is two happy people, right? So I would say if you want to be happy then", "tokens": [50500, 1042, 11, 264, 4054, 281, 257, 2055, 2480, 307, 732, 2055, 561, 11, 558, 30, 407, 286, 576, 584, 498, 291, 528, 281, 312, 2055, 550, 50756]}, {"id": 150, "avg_logprob": -0.3236851692199707, "compression_ratio": 1.791277289390564, "end": 657.3599853515625, "no_speech_prob": 0.044012073427438736, "seek": 64764, "start": 656.1599731445312, "temperature": 0.0, "text": " Be with a happy person", "tokens": [50790, 879, 365, 257, 2055, 954, 50850]}, {"id": 151, "avg_logprob": -0.3236851692199707, "compression_ratio": 1.791277289390564, "end": 661.5, "no_speech_prob": 0.044012073427438736, "seek": 64764, "start": 657.3599853515625, "temperature": 0.0, "text": " Don't think you're gonna be with someone who's unhappy and then make them happy down the road", "tokens": [50850, 1468, 380, 519, 291, 434, 799, 312, 365, 1580, 567, 311, 22172, 293, 550, 652, 552, 2055, 760, 264, 3060, 51057]}, {"id": 152, "avg_logprob": -0.3236851692199707, "compression_ratio": 1.791277289390564, "end": 665.7999877929688, "no_speech_prob": 0.044012073427438736, "seek": 64764, "start": 661.6400146484375, "temperature": 0.0, "text": " How are if you're okay with them being unhappy, but there are other things you like about them", "tokens": [51064, 1012, 366, 498, 291, 434, 1392, 365, 552, 885, 22172, 11, 457, 456, 366, 661, 721, 291, 411, 466, 552, 51272]}, {"id": 153, "avg_logprob": -0.3236851692199707, "compression_ratio": 1.791277289390564, "end": 669.280029296875, "no_speech_prob": 0.044012073427438736, "seek": 64764, "start": 665.7999877929688, "temperature": 0.0, "text": " That's fine. But this goes back to their own happiness with other things", "tokens": [51272, 663, 311, 2489, 13, 583, 341, 1709, 646, 281, 641, 1065, 8324, 365, 661, 721, 51446]}, {"id": 154, "avg_logprob": -0.3236851692199707, "compression_ratio": 1.791277289390564, "end": 677.2000122070312, "no_speech_prob": 0.044012073427438736, "seek": 64764, "start": 669.280029296875, "temperature": 0.0, "text": " Yes, and actually we talked a little bit about how people do connect successfully, you know on spirit and and those things", "tokens": [51446, 1079, 11, 293, 767, 321, 2825, 257, 707, 857, 466, 577, 561, 360, 1745, 10727, 11, 291, 458, 322, 3797, 293, 293, 729, 721, 51842]}, {"id": 155, "avg_logprob": -0.268358051776886, "compression_ratio": 1.7254902124404907, "end": 681.8400268554688, "no_speech_prob": 0.002757300389930606, "seek": 67720, "start": 677.2000122070312, "temperature": 0.0, "text": " but that's maybe a little too abstract if you want to get a little more practical it could be based on values and", "tokens": [50364, 457, 300, 311, 1310, 257, 707, 886, 12649, 498, 291, 528, 281, 483, 257, 707, 544, 8496, 309, 727, 312, 2361, 322, 4190, 293, 50596]}, {"id": 156, "avg_logprob": -0.268358051776886, "compression_ratio": 1.7254902124404907, "end": 687.3200073242188, "no_speech_prob": 0.002757300389930606, "seek": 67720, "start": 682.239990234375, "temperature": 0.0, "text": " Values a set of things you won't compromise on values are the tough decisions of oh my parent got sick", "tokens": [50616, 7188, 1247, 257, 992, 295, 721, 291, 1582, 380, 18577, 322, 4190, 366, 264, 4930, 5327, 295, 1954, 452, 2596, 658, 4998, 50870]}, {"id": 157, "avg_logprob": -0.268358051776886, "compression_ratio": 1.7254902124404907, "end": 690.47998046875, "no_speech_prob": 0.002757300389930606, "seek": 67720, "start": 687.3599853515625, "temperature": 0.0, "text": " Do they move in with us or do we put them in a nursing home?", "tokens": [50872, 1144, 436, 1286, 294, 365, 505, 420, 360, 321, 829, 552, 294, 257, 15423, 1280, 30, 51028]}, {"id": 158, "avg_logprob": -0.268358051776886, "compression_ratio": 1.7254902124404907, "end": 696.2000122070312, "no_speech_prob": 0.002757300389930606, "seek": 67720, "start": 691.280029296875, "temperature": 0.0, "text": " You know that do we give the children money or do we not? You know, do we?", "tokens": [51068, 509, 458, 300, 360, 321, 976, 264, 2227, 1460, 420, 360, 321, 406, 30, 509, 458, 11, 360, 321, 30, 51314]}, {"id": 159, "avg_logprob": -0.268358051776886, "compression_ratio": 1.7254902124404907, "end": 701.719970703125, "no_speech_prob": 0.002757300389930606, "seek": 67720, "start": 697.719970703125, "temperature": 0.0, "text": " Do we move across the country to be closer to our family or do we stay put where we are", "tokens": [51390, 1144, 321, 1286, 2108, 264, 1941, 281, 312, 4966, 281, 527, 1605, 420, 360, 321, 1754, 829, 689, 321, 366, 51590]}, {"id": 160, "avg_logprob": -0.2874279320240021, "compression_ratio": 1.6595745086669922, "end": 710.3800048828125, "no_speech_prob": 0.0032223910093307495, "seek": 70172, "start": 702.5999755859375, "temperature": 0.0, "text": " You know, do we argue about politics do we care or do we not right the values are way more important than checklist items", "tokens": [50408, 509, 458, 11, 360, 321, 9695, 466, 7341, 360, 321, 1127, 420, 360, 321, 406, 558, 264, 4190, 366, 636, 544, 1021, 813, 30357, 4754, 50797]}, {"id": 161, "avg_logprob": -0.2874279320240021, "compression_ratio": 1.6595745086669922, "end": 718.0800170898438, "no_speech_prob": 0.0032223910093307495, "seek": 70172, "start": 711.0399780273438, "temperature": 0.0, "text": " And I think if people were to align much more on their values, they would have much more successful relationships. Hmm the", "tokens": [50830, 400, 286, 519, 498, 561, 645, 281, 7975, 709, 544, 322, 641, 4190, 11, 436, 576, 362, 709, 544, 4406, 6159, 13, 8239, 264, 51182]}, {"id": 162, "avg_logprob": -0.2874279320240021, "compression_ratio": 1.6595745086669922, "end": 720.9199829101562, "no_speech_prob": 0.0032223910093307495, "seek": 70172, "start": 719.1599731445312, "temperature": 0.0, "text": " emotional pain of", "tokens": [51236, 6863, 1822, 295, 51324]}, {"id": 163, "avg_logprob": -0.2874279320240021, "compression_ratio": 1.6595745086669922, "end": 727.1599731445312, "no_speech_prob": 0.0032223910093307495, "seek": 70172, "start": 720.9199829101562, "temperature": 0.0, "text": " Fearing change. I have this thing the job the location to partner. I'm going to enter or not enter this thing for the most part", "tokens": [51324, 3697, 1921, 1319, 13, 286, 362, 341, 551, 264, 1691, 264, 4914, 281, 4975, 13, 286, 478, 516, 281, 3242, 420, 406, 3242, 341, 551, 337, 264, 881, 644, 51636]}, {"id": 164, "avg_logprob": -0.31355372071266174, "compression_ratio": 1.840000033378601, "end": 731.9199829101562, "no_speech_prob": 0.03732096031308174, "seek": 72716, "start": 727.1599731445312, "temperature": 0.0, "text": " It's leaving. I think we have this sort of loss aversion that we really evolve loss aversion", "tokens": [50364, 467, 311, 5012, 13, 286, 519, 321, 362, 341, 1333, 295, 4470, 257, 29153, 300, 321, 534, 16693, 4470, 257, 29153, 50602]}, {"id": 165, "avg_logprob": -0.31355372071266174, "compression_ratio": 1.840000033378601, "end": 737.0800170898438, "no_speech_prob": 0.03732096031308174, "seek": 72716, "start": 731.9199829101562, "temperature": 0.0, "text": " It's just painful separating yourself in front of your friends. It's embarrassing and how you how would you advise people to?", "tokens": [50602, 467, 311, 445, 11697, 29279, 1803, 294, 1868, 295, 428, 1855, 13, 467, 311, 17299, 293, 577, 291, 577, 576, 291, 18312, 561, 281, 30, 50860]}, {"id": 166, "avg_logprob": -0.31355372071266174, "compression_ratio": 1.840000033378601, "end": 742.9600219726562, "no_speech_prob": 0.03732096031308174, "seek": 72716, "start": 737.8800048828125, "temperature": 0.0, "text": " Get past themselves with that loss aversion that fear of change. Oh my god. I'm gonna yes the hardest thing in the world", "tokens": [50900, 3240, 1791, 2969, 365, 300, 4470, 257, 29153, 300, 4240, 295, 1319, 13, 876, 452, 3044, 13, 286, 478, 799, 2086, 264, 13158, 551, 294, 264, 1002, 51154]}, {"id": 167, "avg_logprob": -0.31355372071266174, "compression_ratio": 1.840000033378601, "end": 748.8400268554688, "no_speech_prob": 0.03732096031308174, "seek": 72716, "start": 744.2000122070312, "temperature": 0.0, "text": " Starting over it's back to the zero to one thing. It's a it's the mountain climbing thing", "tokens": [51216, 16217, 670, 309, 311, 646, 281, 264, 4018, 281, 472, 551, 13, 467, 311, 257, 309, 311, 264, 6937, 14780, 551, 51448]}, {"id": 168, "avg_logprob": -0.31355372071266174, "compression_ratio": 1.840000033378601, "end": 751.52001953125, "no_speech_prob": 0.03732096031308174, "seek": 72716, "start": 748.8400268554688, "temperature": 0.0, "text": " You're not gonna find your path to the top of the mountain in the first go-around", "tokens": [51448, 509, 434, 406, 799, 915, 428, 3100, 281, 264, 1192, 295, 264, 6937, 294, 264, 700, 352, 12, 25762, 51582]}, {"id": 169, "avg_logprob": -0.31355372071266174, "compression_ratio": 1.840000033378601, "end": 756.760009765625, "no_speech_prob": 0.03732096031308174, "seek": 72716, "start": 751.6400146484375, "temperature": 0.0, "text": " Sometimes you go up there you get stuck and you come back down and the difference between all the successful people and the ones who", "tokens": [51588, 4803, 291, 352, 493, 456, 291, 483, 5541, 293, 291, 808, 646, 760, 293, 264, 2649, 1296, 439, 264, 4406, 561, 293, 264, 2306, 567, 51844]}, {"id": 170, "avg_logprob": -0.2988884449005127, "compression_ratio": 1.684887409210205, "end": 759.6799926757812, "no_speech_prob": 0.00023778414470143616, "seek": 75676, "start": 756.760009765625, "temperature": 0.0, "text": " Are not is the ones who are successful one is so badly", "tokens": [50364, 2014, 406, 307, 264, 2306, 567, 366, 4406, 472, 307, 370, 13425, 50510]}, {"id": 171, "avg_logprob": -0.2988884449005127, "compression_ratio": 1.684887409210205, "end": 764.8800048828125, "no_speech_prob": 0.00023778414470143616, "seek": 75676, "start": 759.6799926757812, "temperature": 0.0, "text": " They're willing to go back and start over again and again whether in their career or in their relationships or in anything else", "tokens": [50510, 814, 434, 4950, 281, 352, 646, 293, 722, 670, 797, 293, 797, 1968, 294, 641, 3988, 420, 294, 641, 6159, 420, 294, 1340, 1646, 50770]}, {"id": 172, "avg_logprob": -0.2988884449005127, "compression_ratio": 1.684887409210205, "end": 771.6799926757812, "no_speech_prob": 0.00023778414470143616, "seek": 75676, "start": 765.2000122070312, "temperature": 0.0, "text": " In other news this episode is brought to you by function staying on top of your health requires more than just an annual", "tokens": [50786, 682, 661, 2583, 341, 3500, 307, 3038, 281, 291, 538, 2445, 7939, 322, 1192, 295, 428, 1585, 7029, 544, 813, 445, 364, 9784, 51110]}, {"id": 173, "avg_logprob": -0.2988884449005127, "compression_ratio": 1.684887409210205, "end": 774.280029296875, "no_speech_prob": 0.00023778414470143616, "seek": 75676, "start": 771.8800048828125, "temperature": 0.0, "text": " Physical which is why I partnered with function", "tokens": [51120, 31918, 597, 307, 983, 286, 29865, 365, 2445, 51240]}, {"id": 174, "avg_logprob": -0.2988884449005127, "compression_ratio": 1.684887409210205, "end": 780.239990234375, "no_speech_prob": 0.00023778414470143616, "seek": 75676, "start": 774.280029296875, "temperature": 0.0, "text": " They run lab tests twice a year to track over a hundred biomarkers and monitor for early signs of thousands of diseases", "tokens": [51240, 814, 1190, 2715, 6921, 6091, 257, 1064, 281, 2837, 670, 257, 3262, 27450, 809, 433, 293, 6002, 337, 2440, 7880, 295, 5383, 295, 11044, 51538]}, {"id": 175, "avg_logprob": -0.2988884449005127, "compression_ratio": 1.684887409210205, "end": 783.4400024414062, "no_speech_prob": 0.00023778414470143616, "seek": 75676, "start": 780.239990234375, "temperature": 0.0, "text": " They even screen for 50 types of cancer at stage one", "tokens": [51538, 814, 754, 2568, 337, 2625, 3467, 295, 5592, 412, 3233, 472, 51698]}, {"id": 176, "avg_logprob": -0.32054391503334045, "compression_ratio": 1.7353845834732056, "end": 790.1599731445312, "no_speech_prob": 0.37373313307762146, "seek": 78344, "start": 783.4400024414062, "temperature": 0.0, "text": " Which is five times more data than you get from an annual physical you receive insights from a team of expert physicians who provide a detail", "tokens": [50364, 3013, 307, 1732, 1413, 544, 1412, 813, 291, 483, 490, 364, 9784, 4001, 291, 4774, 14310, 490, 257, 1469, 295, 5844, 21966, 567, 2893, 257, 2607, 50700]}, {"id": 177, "avg_logprob": -0.32054391503334045, "compression_ratio": 1.7353845834732056, "end": 793.02001953125, "no_speech_prob": 0.37373313307762146, "seek": 78344, "start": 790.1599731445312, "temperature": 0.0, "text": " Written clinician summary for their observations and then phone", "tokens": [50700, 10159, 2987, 45962, 12691, 337, 641, 18163, 293, 550, 2593, 50843]}, {"id": 178, "avg_logprob": -0.32054391503334045, "compression_ratio": 1.7353845834732056, "end": 798.1599731445312, "no_speech_prob": 0.37373313307762146, "seek": 78344, "start": 793.3200073242188, "temperature": 0.0, "text": " consultations for any critical findings getting these lab tests done would usually cost thousands, but with function it is only", "tokens": [50858, 7189, 763, 337, 604, 4924, 16483, 1242, 613, 2715, 6921, 1096, 576, 2673, 2063, 5383, 11, 457, 365, 2445, 309, 307, 787, 51100]}, {"id": 179, "avg_logprob": -0.32054391503334045, "compression_ratio": 1.7353845834732056, "end": 803.3200073242188, "no_speech_prob": 0.37373313307762146, "seek": 78344, "start": 799.4400024414062, "temperature": 0.0, "text": " $499 and right now you can get the exact same blood panels that I get and", "tokens": [51164, 1848, 19, 8494, 293, 558, 586, 291, 393, 483, 264, 1900, 912, 3390, 13419, 300, 286, 483, 293, 51358]}, {"id": 180, "avg_logprob": -0.32054391503334045, "compression_ratio": 1.7353845834732056, "end": 809.8800048828125, "no_speech_prob": 0.37373313307762146, "seek": 78344, "start": 803.52001953125, "temperature": 0.0, "text": " Bypass their waitlist by going to the link in the description below or heading to function health comm slash modern wisdom", "tokens": [51368, 3146, 9216, 641, 1699, 8264, 538, 516, 281, 264, 2113, 294, 264, 3855, 2507, 420, 9864, 281, 2445, 1585, 800, 17330, 4363, 10712, 51686]}, {"id": 181, "avg_logprob": -0.32054391503334045, "compression_ratio": 1.7353845834732056, "end": 811.8800048828125, "no_speech_prob": 0.37373313307762146, "seek": 78344, "start": 809.8800048828125, "temperature": 0.0, "text": " That's function health comm slash", "tokens": [51686, 663, 311, 2445, 1585, 800, 17330, 51786]}, {"id": 182, "avg_logprob": -0.39555326104164124, "compression_ratio": 1.2575757503509521, "end": 815.0, "no_speech_prob": 0.030619632452726364, "seek": 81188, "start": 812.280029296875, "temperature": 0.0, "text": " Modern wisdom. Thank you very much for tuning in", "tokens": [50384, 19814, 10712, 13, 1044, 291, 588, 709, 337, 15164, 294, 50520]}, {"id": 183, "avg_logprob": -0.39555326104164124, "compression_ratio": 1.2575757503509521, "end": 821.239990234375, "no_speech_prob": 0.030619632452726364, "seek": 81188, "start": 815.0, "temperature": 0.0, "text": " If you enjoyed that clip with <PERSON><PERSON> just a mere taster the full-length episode is available right here", "tokens": [50520, 759, 291, 4626, 300, 7353, 365, 1734, 3337, 445, 257, 8401, 256, 1727, 264, 1577, 12, 45390, 3500, 307, 2435, 558, 510, 50832]}, {"id": 184, "avg_logprob": -0.39555326104164124, "compression_ratio": 1.2575757503509521, "end": 823.7999877929688, "no_speech_prob": 0.030619632452726364, "seek": 81188, "start": 822.3200073242188, "temperature": 0.0, "text": " Go on", "tokens": [50886, 1037, 322, 50960]}, {"id": 185, "avg_logprob": -0.39555326104164124, "compression_ratio": 1.2575757503509521, "end": 825.7999877929688, "no_speech_prob": 0.030619632452726364, "seek": 81188, "start": 823.7999877929688, "temperature": 0.0, "text": " Watch it", "tokens": [50960, 7277, 309, 51060]}], "words": null, "task": "transcribe"}