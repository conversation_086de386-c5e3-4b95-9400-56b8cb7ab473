{"clips": [{"clip_id": "clip_000", "file_path": "clip_000.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.85, "text": "Sample clip 000 for face tracking testing"}, {"clip_id": "clip_001", "file_path": "clip_001.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.82, "text": "Sample clip 001 for face tracking testing"}, {"clip_id": "clip_002", "file_path": "clip_002.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.78, "text": "Sample clip 002 for face tracking testing"}, {"clip_id": "clip_003", "file_path": "clip_003.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.88, "text": "Sample clip 003 for face tracking testing"}, {"clip_id": "clip_004", "file_path": "clip_004.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.75, "text": "Sample clip 004 for face tracking testing"}, {"clip_id": "clip_005", "file_path": "clip_005.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.8, "text": "Sample clip 005 for face tracking testing"}, {"clip_id": "clip_006", "file_path": "clip_006.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.83, "text": "Sample clip 006 for face tracking testing"}, {"clip_id": "clip_007", "file_path": "clip_007.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.77, "text": "Sample clip 007 for face tracking testing"}, {"clip_id": "clip_008", "file_path": "clip_008.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.86, "text": "Sample clip 008 for face tracking testing"}, {"clip_id": "clip_009", "file_path": "clip_009.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.79, "text": "Sample clip 009 for face tracking testing"}, {"clip_id": "clip_010", "file_path": "clip_010.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.84, "text": "Sample clip 010 for face tracking testing"}, {"clip_id": "clip_011", "file_path": "clip_011.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.81, "text": "Sample clip 011 for face tracking testing"}, {"clip_id": "clip_012", "file_path": "clip_012.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.87, "text": "Sample clip 012 for face tracking testing"}, {"clip_id": "clip_013", "file_path": "clip_013.mp4", "start_time": 0.0, "end_time": 30.0, "duration": 30.0, "score": 0.87, "text": "Sample clip 013 for face tracking testing"}]}