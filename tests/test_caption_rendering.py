#!/usr/bin/env python3
"""
Test script for caption rendering improvements

This script tests the enhanced caption styling and placement features:
1. Dynamic caption area calculation based on font size
2. Maximum 2-line restriction for optimal readability
3. Face-aware positioning using temporal tracking data
4. Smart word wrapping algorithms
5. Application after highlight video generation
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pipeline.tasks.caption_composer import CaptionComposer
from config.settings import (
    CAPTION_FONT_SIZE, CAPTION_MAX_LINES, CAPTION_DYNAMIC_POSITIONING,
    VIDEO_WIDTH, VIDEO_HEIGHT
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_caption_improvements():
    """Test the caption improvements with the existing output"""
    print("🧪 Testing Caption Styling and Placement Improvements...")
    print("=" * 60)
    
    # Use the existing output directory
    job_id = "fac587e6-2c5b-4f97-bfe6-89fd76dc6954"
    output_dir = f"output/{job_id}"
    
    if not os.path.exists(output_dir):
        print(f"❌ Output directory not found: {output_dir}")
        print("Please run the pipeline first to generate test data")
        return False
    
    print(f"📁 Using existing output: {job_id}")
    
    # Check for required files
    transcript_vtt_path = os.path.join(output_dir, "transcript", "transcript.vtt")
    temporal_tracking_path = os.path.join(output_dir, "temporal_tracking", "clip_000_tracking.json")
    reframed_video_path = os.path.join(output_dir, "reframed", "clip_000_temporal_reframed.mp4")
    
    print(f"🔍 Checking required files...")
    print(f"   📝 VTT transcript: {'✅' if os.path.exists(transcript_vtt_path) else '❌'}")
    print(f"   🎯 Temporal tracking: {'✅' if os.path.exists(temporal_tracking_path) else '❌'}")
    print(f"   🎬 Reframed video: {'✅' if os.path.exists(reframed_video_path) else '❌'}")
    
    if not all(os.path.exists(p) for p in [transcript_vtt_path, temporal_tracking_path, reframed_video_path]):
        print("❌ Missing required files for caption testing")
        return False
    
    # Test caption composer
    print("\n🎨 Testing Caption Composer...")
    composer = CaptionComposer()
    
    # Test VTT loading
    print("📝 Testing VTT segment loading...")
    vtt_segments = composer._load_vtt_segments(transcript_vtt_path)
    print(f"   Loaded {len(vtt_segments)} VTT segments")
    
    if vtt_segments:
        sample_segment = vtt_segments[0]
        print(f"   Sample segment: {sample_segment['start_time']:.1f}s - {sample_segment['end_time']:.1f}s")
        print(f"   Text: {sample_segment['text'][:50]}...")
    
    # Test face region loading
    print("\n🎭 Testing face region loading...")
    face_regions = composer._load_face_regions(job_id, "clip_000")
    print(f"   Loaded {len(face_regions)} face regions")
    
    if face_regions:
        sample_face = face_regions[0]
        print(f"   Sample face: {sample_face.x}, {sample_face.y}, {sample_face.width}x{sample_face.height} at {sample_face.timestamp:.1f}s")
    
    # Test word wrapping algorithms
    print("\n📏 Testing word wrapping algorithms...")
    test_texts = [
        "This is a short text that should fit on one line",
        "This is a longer text that should be wrapped into two lines for optimal readability on mobile devices",
        "This is an extremely long text that would normally exceed the maximum line limit but our smart algorithm should handle it gracefully by truncating or wrapping appropriately"
    ]
    
    for i, text in enumerate(test_texts):
        print(f"\n   Test {i+1}: {text[:30]}...")
        lines = composer._smart_word_wrap(text, {})
        print(f"   Result: {len(lines)} lines (max: {CAPTION_MAX_LINES})")
        for j, line in enumerate(lines):
            print(f"     Line {j+1}: {line}")
    
    # Test caption positioning
    print("\n📍 Testing caption positioning...")
    test_timestamps = [0.0, 5.0, 10.0]
    
    for timestamp in test_timestamps:
        position_y = composer._calculate_optimal_position(timestamp, face_regions, {})
        print(f"   Timestamp {timestamp:.1f}s: Y position = {position_y}")
        
        # Check if position avoids faces
        if face_regions:
            nearby_faces = [f for f in face_regions if abs(f.timestamp - timestamp) <= 1.0]
            if nearby_faces:
                print(f"     Nearby faces: {len(nearby_faces)}")
                for face in nearby_faces[:2]:  # Show first 2
                    print(f"       Face at Y: {face.y}-{face.y + face.height}")
    
    # Test caption dimensions calculation
    print("\n📐 Testing caption dimensions calculation...")
    test_line_sets = [
        ["Single line"],
        ["First line", "Second line"],
        ["A longer first line with more text", "Shorter second"]
    ]
    
    for i, lines in enumerate(test_line_sets):
        width, height = composer._calculate_caption_dimensions(lines, {})
        print(f"   Test {i+1}: {len(lines)} lines -> {width}x{height} pixels")
        for line in lines:
            print(f"     '{line}'")
    
    # Test configuration display
    print("\n⚙️  Caption Configuration:")
    print(f"   Font size: {CAPTION_FONT_SIZE}px")
    print(f"   Max lines: {CAPTION_MAX_LINES}")
    print(f"   Video dimensions: {VIDEO_WIDTH}x{VIDEO_HEIGHT}")
    print(f"   Dynamic positioning: {'✅' if CAPTION_DYNAMIC_POSITIONING else '❌'}")
    
    print("\n✅ Caption improvement tests completed!")
    print("\n📋 Summary of Improvements:")
    print("   🎯 Dynamic caption area calculation based on font size")
    print("   📏 Maximum 2-line restriction for optimal readability")
    print("   🎭 Face-aware positioning using temporal tracking data")
    print("   🧠 Smart word wrapping algorithms (simple, smart, balanced)")
    print("   ⏰ Applied after highlight video generation")
    print("   🎨 Enhanced styling with stroke, shadow, and positioning")
    
    return True


def test_caption_integration():
    """Test caption integration with the pipeline"""
    print("\n🔗 Testing Caption Integration...")
    
    # Mock reframer result
    mock_reframer_result = {
        "metadata": {
            "clips": [
                {
                    "clip_id": "clip_000",
                    "reframed_path": "output/fac587e6-2c5b-4f97-bfe6-89fd76dc6954/reframed/clip_000_temporal_reframed.mp4",
                    "start_time": 0.0,
                    "end_time": 11.96
                }
            ]
        }
    }
    
    # Mock transcription result
    mock_transcription_result = {
        "transcript_vtt_path": "output/fac587e6-2c5b-4f97-bfe6-89fd76dc6954/transcript/transcript.vtt"
    }
    
    # Test parameters
    test_params = {
        "skip_captions": False,
        "caption_style": "youtube_shorts",
        "enable_smart_positioning": True
    }
    
    # Create caption composer and test
    composer = CaptionComposer()
    
    print("🎬 Running caption composition test...")
    result = composer.run(
        "fac587e6-2c5b-4f97-bfe6-89fd76dc6954",
        mock_reframer_result,
        mock_transcription_result,
        test_params
    )
    
    print(f"   Status: {result.get('status', 'unknown')}")
    if result.get('status') == 'completed':
        captioned_clips = result.get('captioned_clips', [])
        print(f"   Captioned clips: {len(captioned_clips)}")
        
        if captioned_clips:
            clip = captioned_clips[0]
            captioned_path = clip.get('captioned_path')
            if captioned_path:
                print(f"   Captioned video: {captioned_path}")
                print(f"   File exists: {'✅' if os.path.exists(captioned_path) else '❌'}")
    else:
        print(f"   Error: {result.get('error', 'Unknown error')}")
    
    return result.get('status') == 'completed'


if __name__ == "__main__":
    print("🚀 Starting Caption Rendering Tests...")
    
    # Test caption improvements
    improvements_success = test_caption_improvements()
    
    # Test integration
    integration_success = test_caption_integration()
    
    print("\n" + "=" * 60)
    print("🏁 Test Results:")
    print(f"   Caption Improvements: {'✅ PASSED' if improvements_success else '❌ FAILED'}")
    print(f"   Pipeline Integration: {'✅ PASSED' if integration_success else '❌ FAILED'}")
    
    if improvements_success and integration_success:
        print("\n🎉 All caption tests passed! The improvements are working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
