#!/usr/bin/env python3
"""
Unit tests for highlight duration validation

Tests the 10-second minimum duration requirement for highlight videos
across all highlight processing functions and validation utilities.
"""

import unittest
import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.validation_utils import HighlightValidationUtils, ValidationError
from config.settings import MIN_HIGHLIGHT_DURATION_SECONDS


class TestHighlightDurationValidation(unittest.TestCase):
    """Test cases for highlight duration validation"""

    def setUp(self):
        """Set up test fixtures"""
        self.min_duration = MIN_HIGHLIGHT_DURATION_SECONDS
        
        # Test highlights with various durations
        self.valid_highlights = [
            {'duration': 10.0, 'start_time': 0, 'end_time': 10},
            {'duration': 15.5, 'start_time': 20, 'end_time': 35.5},
            {'duration': 30.0, 'start_time': 40, 'end_time': 70},
        ]
        
        self.invalid_highlights = [
            {'duration': 5.0, 'start_time': 0, 'end_time': 5},
            {'duration': 9.9, 'start_time': 10, 'end_time': 19.9},
            {'duration': 0.5, 'start_time': 30, 'end_time': 30.5},
        ]
        
        self.mixed_highlights = self.valid_highlights + self.invalid_highlights

    def test_validate_highlight_duration_valid(self):
        """Test validation of valid highlight durations"""
        for duration in [10.0, 15.5, 30.0, 60.0]:
            with self.subTest(duration=duration):
                result = HighlightValidationUtils.validate_highlight_duration(duration)
                self.assertTrue(result['valid'])
                self.assertTrue(result['meets_minimum'])
                self.assertEqual(result['duration'], duration)
                self.assertEqual(result['min_required'], self.min_duration)
                self.assertEqual(len(result['errors']), 0)

    def test_validate_highlight_duration_invalid(self):
        """Test validation of invalid highlight durations"""
        for duration in [0.0, 5.0, 9.9]:
            with self.subTest(duration=duration):
                result = HighlightValidationUtils.validate_highlight_duration(duration)
                self.assertFalse(result['valid'])
                self.assertFalse(result['meets_minimum'])
                self.assertEqual(result['duration'], duration)
                self.assertEqual(result['min_required'], self.min_duration)
                self.assertGreater(len(result['errors']), 0)
                
                # Check error message content
                error_msg = result['errors'][0]
                self.assertIn(f"{duration:.1f}s", error_msg)
                self.assertIn(f"{self.min_duration:.1f}s", error_msg)

    def test_validate_highlight_duration_edge_cases(self):
        """Test validation edge cases"""
        # Exactly minimum duration
        result = HighlightValidationUtils.validate_highlight_duration(10.0)
        self.assertTrue(result['valid'])
        
        # Just below minimum
        result = HighlightValidationUtils.validate_highlight_duration(9.999)
        self.assertFalse(result['valid'])
        
        # Just above minimum
        result = HighlightValidationUtils.validate_highlight_duration(10.001)
        self.assertTrue(result['valid'])

    def test_validate_highlight_duration_invalid_types(self):
        """Test validation with invalid duration types"""
        for invalid_duration in ["10", None, [], {}]:
            with self.subTest(duration=invalid_duration):
                result = HighlightValidationUtils.validate_highlight_duration(invalid_duration)
                self.assertFalse(result['valid'])
                self.assertGreater(len(result['errors']), 0)

    def test_validate_highlight_duration_negative(self):
        """Test validation with negative durations"""
        for duration in [-1.0, -10.0]:
            with self.subTest(duration=duration):
                result = HighlightValidationUtils.validate_highlight_duration(duration)
                self.assertFalse(result['valid'])
                self.assertGreater(len(result['errors']), 0)

    def test_validate_highlight_duration_custom_minimum(self):
        """Test validation with custom minimum duration"""
        custom_min = 15.0
        
        # Valid with custom minimum
        result = HighlightValidationUtils.validate_highlight_duration(20.0, custom_min)
        self.assertTrue(result['valid'])
        self.assertEqual(result['min_required'], custom_min)
        
        # Invalid with custom minimum (but would be valid with default)
        result = HighlightValidationUtils.validate_highlight_duration(12.0, custom_min)
        self.assertFalse(result['valid'])
        self.assertEqual(result['min_required'], custom_min)

    def test_validate_highlight_list_all_valid(self):
        """Test validation of list with all valid highlights"""
        result = HighlightValidationUtils.validate_highlight_list(self.valid_highlights)
        
        self.assertTrue(result['valid'])
        self.assertEqual(result['total_highlights'], 3)
        self.assertEqual(result['valid_highlights'], 3)
        self.assertEqual(result['invalid_highlights'], 0)
        self.assertEqual(len(result['filtered_highlights']), 3)
        self.assertEqual(len(result['invalid_items']), 0)

    def test_validate_highlight_list_all_invalid(self):
        """Test validation of list with all invalid highlights"""
        result = HighlightValidationUtils.validate_highlight_list(self.invalid_highlights)
        
        self.assertFalse(result['valid'])
        self.assertEqual(result['total_highlights'], 3)
        self.assertEqual(result['valid_highlights'], 0)
        self.assertEqual(result['invalid_highlights'], 3)
        self.assertEqual(len(result['filtered_highlights']), 0)
        self.assertEqual(len(result['invalid_items']), 3)

    def test_validate_highlight_list_mixed(self):
        """Test validation of list with mixed valid/invalid highlights"""
        result = HighlightValidationUtils.validate_highlight_list(self.mixed_highlights)
        
        self.assertFalse(result['valid'])  # Overall invalid due to some invalid highlights
        self.assertEqual(result['total_highlights'], 6)
        self.assertEqual(result['valid_highlights'], 3)
        self.assertEqual(result['invalid_highlights'], 3)
        self.assertEqual(len(result['filtered_highlights']), 3)
        self.assertEqual(len(result['invalid_items']), 3)

    def test_validate_highlight_list_empty(self):
        """Test validation of empty highlight list"""
        result = HighlightValidationUtils.validate_highlight_list([])
        
        self.assertFalse(result['valid'])
        self.assertEqual(result['total_highlights'], 0)
        self.assertGreater(len(result['errors']), 0)

    def test_validate_highlight_list_alternative_duration_fields(self):
        """Test validation with highlights using start_time/end_time instead of duration"""
        highlights_with_times = [
            {'start_time': 0, 'end_time': 15},  # 15s - valid
            {'start_time': 20, 'end_time': 25}, # 5s - invalid
            {'start': 30, 'end': 45},           # 15s - valid (alternative field names)
        ]
        
        result = HighlightValidationUtils.validate_highlight_list(highlights_with_times)
        
        self.assertEqual(result['total_highlights'], 3)
        self.assertEqual(result['valid_highlights'], 2)
        self.assertEqual(result['invalid_highlights'], 1)

    def test_enforce_minimum_duration(self):
        """Test enforcement of minimum duration requirement"""
        filtered = HighlightValidationUtils.enforce_minimum_duration(
            self.mixed_highlights, 
            log_filtered=False
        )
        
        self.assertEqual(len(filtered), 3)  # Only valid highlights should remain
        
        # Verify all filtered highlights meet minimum duration
        for highlight in filtered:
            duration = highlight.get('duration', 0)
            self.assertGreaterEqual(duration, self.min_duration)

    def test_enforce_minimum_duration_custom_minimum(self):
        """Test enforcement with custom minimum duration"""
        custom_min = 20.0
        filtered = HighlightValidationUtils.enforce_minimum_duration(
            self.mixed_highlights, 
            min_duration=custom_min,
            log_filtered=False
        )
        
        # Only highlights >= 20s should remain
        expected_count = sum(1 for h in self.mixed_highlights if h.get('duration', 0) >= custom_min)
        self.assertEqual(len(filtered), expected_count)
        
        for highlight in filtered:
            duration = highlight.get('duration', 0)
            self.assertGreaterEqual(duration, custom_min)

    def test_enforce_minimum_duration_all_valid(self):
        """Test enforcement when all highlights are already valid"""
        filtered = HighlightValidationUtils.enforce_minimum_duration(
            self.valid_highlights,
            log_filtered=False
        )
        
        self.assertEqual(len(filtered), len(self.valid_highlights))

    def test_enforce_minimum_duration_all_invalid(self):
        """Test enforcement when all highlights are invalid"""
        filtered = HighlightValidationUtils.enforce_minimum_duration(
            self.invalid_highlights,
            log_filtered=False
        )
        
        self.assertEqual(len(filtered), 0)


if __name__ == '__main__':
    unittest.main()
