#!/usr/bin/env python3
"""
Test script for temporal face tracking functionality using sample clips

This script demonstrates how to:
1. Track faces every second throughout sample video clips
2. Generate smooth crop sequences from tracking data
3. Create videos with face-following camera movements
4. Generate preview frames showing tracking results
"""

import sys
import os
import json
import argparse
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from reframing.face_detection.engine import FaceDetectionEngine
from reframing.tracking.temporal_face_tracker import TemporalFaceTracker
from reframing.video.temporal_video_generator import TemporalVideoGenerator


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def load_clip_manifest(clips_dir: Path) -> dict:
    """Load clip manifest from clips directory"""
    manifest_path = clips_dir / "clip_manifest.json"
    if not manifest_path.exists():
        raise FileNotFoundError(f"Clip manifest not found: {manifest_path}")

    with open(manifest_path, 'r') as f:
        return json.load(f)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Test temporal face tracking on sample clips')
    parser.add_argument('--clips-dir', default='tests/sample/clips',
                       help='Directory containing sample clips (default: tests/sample/clips)')
    parser.add_argument('--output-dir', default='output/temporal_clips_test',
                       help='Output directory for results')
    parser.add_argument('--detection-interval', type=float, default=1.0,
                       help='Face detection interval in seconds (default: 1.0)')
    parser.add_argument('--target-width', type=int, default=720,
                       help='Target crop width (default: 720)')
    parser.add_argument('--target-height', type=int, default=1280,
                       help='Target crop height (default: 1280)')
    parser.add_argument('--smoothing-factor', type=float, default=0.3,
                       help='Temporal smoothing factor 0.0-1.0 (default: 0.3)')
    parser.add_argument('--preview-only', action='store_true',
                       help='Generate preview frames only, no video')
    parser.add_argument('--clip-id', type=str, default=None,
                       help='Process specific clip ID only (e.g., clip_000)')

    args = parser.parse_args()

    setup_logging()
    logger = logging.getLogger(__name__)

    # Validate clips directory
    clips_dir = Path(args.clips_dir)
    if not clips_dir.exists():
        logger.error(f"Clips directory not found: {clips_dir}")
        return 1

    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info("🎬 Starting Temporal Face Tracking Test on Sample Clips")
    logger.info(f"📁 Clips directory: {clips_dir}")
    logger.info(f"📁 Output directory: {output_dir}")
    logger.info(f"⏱️ Detection interval: {args.detection_interval}s")
    logger.info(f"📐 Target size: {args.target_width}x{args.target_height}")
    logger.info(f"🔄 Smoothing factor: {args.smoothing_factor}")

    try:
        # Load clip manifest
        manifest = load_clip_manifest(clips_dir)
        clips = manifest.get('clips', [])

        if not clips:
            logger.error("No clips found in manifest")
            return 1

        # Filter clips if specific clip ID requested
        if args.clip_id:
            clips = [clip for clip in clips if clip.get('clip_id') == args.clip_id]
            if not clips:
                logger.error(f"Clip ID '{args.clip_id}' not found in manifest")
                return 1

        logger.info(f"📹 Processing {len(clips)} clips")

        # Initialize face detection engine with MediaPipe for good results
        face_engine = FaceDetectionEngine(backend="mediapipe", confidence_threshold=0.4)

        # Create temporal face tracker
        tracker = TemporalFaceTracker(face_engine, args.detection_interval)

        # Create video generator
        video_generator = TemporalVideoGenerator()

        # Process each clip
        for i, clip in enumerate(clips):
            clip_id = clip.get('clip_id', f'clip_{i}')
            clip_file_path = clip.get('file_path')

            # Check if clip file exists in clips directory
            clip_path = clips_dir / f"{clip_id}.mp4"
            if not clip_path.exists():
                # Try the path from manifest (might be absolute)
                if clip_file_path and os.path.exists(clip_file_path):
                    clip_path = Path(clip_file_path)
                else:
                    logger.warning(f"Clip file not found: {clip_path}")
                    continue

            logger.info(f"🎯 Processing clip {i+1}/{len(clips)}: {clip_id}")
            logger.info(f"📹 Clip path: {clip_path}")

            # Create clip-specific output directory
            clip_output_dir = output_dir / clip_id
            clip_output_dir.mkdir(exist_ok=True)

            # Step 1: Track faces in the clip
            logger.info(f"🔍 Tracking faces in {clip_id}...")
            tracking_sequence = tracker.track_faces_in_video(str(clip_path))

            # Save tracking data
            tracking_json_path = clip_output_dir / "tracking_data.json"
            tracker.save_tracking_data(tracking_sequence, str(tracking_json_path))
            logger.info(f"💾 Saved tracking data: {tracking_json_path}")

            # Step 2: Generate preview frames
            logger.info(f"🖼️ Generating preview frames for {clip_id}...")
            preview_dir = clip_output_dir / "preview_frames"
            preview_frames = video_generator.generate_preview_frames(
                str(clip_path), tracking_sequence, str(preview_dir),
                args.target_width, args.target_height, frame_interval=2.0
            )
            logger.info(f"✅ Generated {len(preview_frames)} preview frames")

            if args.preview_only:
                logger.info(f"🎯 Preview-only mode - skipping video generation for {clip_id}")
                continue

            # Step 3: Generate video with face tracking
            logger.info(f"🎬 Generating tracked video for {clip_id}...")
            output_video_path = clip_output_dir / f"{clip_id}_tracked.mp4"

            success = video_generator.generate_video_from_tracking(
                str(clip_path), tracking_sequence, str(output_video_path),
                args.target_width, args.target_height, args.smoothing_factor
            )

            if success:
                logger.info(f"✅ Successfully generated tracked video: {output_video_path}")

                # Generate statistics
                stats_path = clip_output_dir / "tracking_stats.txt"
                generate_tracking_stats(tracking_sequence, stats_path, clip)
                logger.info(f"📊 Generated tracking statistics: {stats_path}")

            else:
                logger.error(f"❌ Failed to generate tracked video for {clip_id}")

        logger.info("🎉 Temporal face tracking test completed successfully!")

        # Generate summary report
        summary_path = output_dir / "summary_report.txt"
        generate_summary_report(clips, output_dir, summary_path)
        logger.info(f"📋 Generated summary report: {summary_path}")

        return 0

    except Exception as e:
        logger.error(f"❌ Error during temporal face tracking: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


def generate_tracking_stats(tracking_sequence, output_path, clip_info):
    """Generate tracking statistics report for a clip"""

    total_detections = len(tracking_sequence.tracking_data)
    face_detections = sum(1 for data in tracking_sequence.tracking_data if data.faces)
    multi_face_detections = sum(1 for data in tracking_sequence.tracking_data if len(data.faces) >= 2)

    avg_confidence = 0.0
    if face_detections > 0:
        avg_confidence = sum(data.confidence for data in tracking_sequence.tracking_data if data.faces) / face_detections

    max_faces = max(len(data.faces) for data in tracking_sequence.tracking_data) if tracking_sequence.tracking_data else 0

    with open(output_path, 'w') as f:
        f.write("Temporal Face Tracking Statistics\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Clip ID: {clip_info.get('clip_id', 'unknown')}\n")
        f.write(f"Video: {tracking_sequence.video_path}\n")
        f.write(f"Duration: {tracking_sequence.duration:.2f} seconds\n")
        f.write(f"Frame size: {tracking_sequence.frame_width}x{tracking_sequence.frame_height}\n")
        f.write(f"FPS: {tracking_sequence.fps:.2f}\n")
        f.write(f"Detection interval: {tracking_sequence.detection_interval}s\n\n")

        f.write("Clip Information:\n")
        f.write(f"- Start time: {clip_info.get('start_time', 'unknown')}s\n")
        f.write(f"- End time: {clip_info.get('end_time', 'unknown')}s\n")
        f.write(f"- Score: {clip_info.get('score', 'unknown')}\n")
        f.write(f"- Text: {clip_info.get('text', 'No text')[:100]}...\n\n")

        f.write("Detection Results:\n")
        f.write(f"- Total detection points: {total_detections}\n")
        f.write(f"- Points with faces: {face_detections} ({face_detections/total_detections*100:.1f}%)\n")
        f.write(f"- Multi-face detections: {multi_face_detections} ({multi_face_detections/total_detections*100:.1f}%)\n")
        f.write(f"- Average confidence: {avg_confidence:.3f}\n")
        f.write(f"- Maximum faces in frame: {max_faces}\n\n")

        f.write("Timeline:\n")
        for i, data in enumerate(tracking_sequence.tracking_data):
            f.write(f"{data.timestamp:6.1f}s: {len(data.faces)} faces, conf={data.confidence:.3f}")
            if data.group_bounds:
                f.write(f", group={data.group_bounds.face_count} faces")
            f.write("\n")


def generate_summary_report(clips, output_dir, summary_path):
    """Generate summary report for all processed clips"""

    with open(summary_path, 'w') as f:
        f.write("Temporal Face Tracking Summary Report\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Total clips processed: {len(clips)}\n")
        f.write(f"Output directory: {output_dir}\n\n")

        f.write("Processed Clips:\n")
        f.write("-" * 20 + "\n")

        for clip in clips:
            clip_id = clip.get('clip_id', 'unknown')
            duration = clip.get('duration', 0)
            f.write(f"- {clip_id}: {duration:.1f}s\n")

            # Check if tracking data exists
            clip_output_dir = output_dir / clip_id
            tracking_json = clip_output_dir / "tracking_data.json"
            tracked_video = clip_output_dir / f"{clip_id}_tracked.mp4"

            if tracking_json.exists():
                f.write(f"  ✅ Tracking data: {tracking_json}\n")
            else:
                f.write(f"  ❌ Tracking data: Missing\n")

            if tracked_video.exists():
                f.write(f"  ✅ Tracked video: {tracked_video}\n")
            else:
                f.write(f"  ❌ Tracked video: Missing\n")

            f.write("\n")


if __name__ == "__main__":
    sys.exit(main())
