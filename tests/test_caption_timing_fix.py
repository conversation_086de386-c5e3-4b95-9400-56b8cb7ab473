#!/usr/bin/env python3
"""
Test script to verify caption timing synchronization fix

This script tests the critical fix for caption timing synchronization where:
1. VTT segments are correctly converted from absolute to clip-relative timestamps
2. Face regions are also converted to clip-relative timestamps
3. Caption positioning uses consistent time references
4. Captions appear exactly when corresponding words are spoken

The fix addresses the issue where captions appeared at wrong times due to
timestamp reference mismatches between VTT segments and face tracking data.
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from pipeline.tasks.caption_composer import CaptionComposer
from config.settings import (
    CAPTION_FONT_SIZE, CAPTION_MAX_LINES, CAPTION_DYNAMIC_POSITIONING,
    VIDEO_WIDTH, VIDEO_HEIGHT
)

# Set up logging to see timing debug messages
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_vtt_segments():
    """Create test VTT segments with known timestamps for testing"""
    return [
        {
            'cue_number': 1,
            'start_time': 10.0,  # Absolute time in original video
            'end_time': 12.5,
            'text': 'Hello, this is the first caption segment.'
        },
        {
            'cue_number': 2,
            'start_time': 12.5,
            'end_time': 15.0,
            'text': 'This is the second caption that should appear right after.'
        },
        {
            'cue_number': 3,
            'start_time': 15.0,
            'end_time': 18.0,
            'text': 'And this is the third caption for our test.'
        }
    ]

def create_test_face_regions():
    """Create test face regions with absolute timestamps"""
    return [
        {
            'timestamp': 10.5,  # Absolute time in original video
            'primary_face': {
                'x': 100,
                'y': 200,
                'width': 150,
                'height': 200
            }
        },
        {
            'timestamp': 13.0,
            'primary_face': {
                'x': 120,
                'y': 180,
                'width': 160,
                'height': 210
            }
        },
        {
            'timestamp': 16.5,
            'primary_face': {
                'x': 110,
                'y': 190,
                'width': 155,
                'height': 205
            }
        }
    ]

def test_vtt_segment_timing_conversion():
    """Test that VTT segments are correctly converted to clip-relative time"""
    print("\n🧪 Testing VTT Segment Timing Conversion...")

    composer = CaptionComposer()
    test_segments = create_test_vtt_segments()

    # Test clip from 11.0s to 16.0s (5 second clip)
    clip_start = 11.0
    clip_end = 16.0

    print(f"📊 Test clip: {clip_start}s - {clip_end}s (duration: {clip_end - clip_start}s)")
    print(f"📝 Original VTT segments:")
    for seg in test_segments:
        print(f"   {seg['start_time']:.1f}s - {seg['end_time']:.1f}s: '{seg['text'][:30]}...'")

    # Convert segments
    relevant_segments = composer._find_relevant_segments(test_segments, clip_start, clip_end)

    print(f"\n✅ Converted segments (clip-relative):")
    for seg in relevant_segments:
        print(f"   {seg['start_time']:.3f}s - {seg['end_time']:.3f}s: '{seg['text'][:30]}...'")
        print(f"      (Original: {seg['original_start']:.1f}s - {seg['original_end']:.1f}s)")

    # Verify timing conversion is correct
    # The actual conversions should be:
    # 1. 10.0-12.5 overlaps 11.0-16.0 -> max(0, 10.0-11.0) to min(16.0-11.0, 12.5-11.0) = 0.0 to 1.5
    # 2. 12.5-15.0 overlaps 11.0-16.0 -> max(0, 12.5-11.0) to min(16.0-11.0, 15.0-11.0) = 1.5 to 4.0
    # 3. 15.0-18.0 overlaps 11.0-16.0 -> max(0, 15.0-11.0) to min(16.0-11.0, 18.0-11.0) = 4.0 to 5.0
    expected_conversions = [
        (10.0, 12.5, 0.0, 1.5),  # First segment (clipped): 10.0-12.5 -> 0.0-1.5
        (12.5, 15.0, 1.5, 4.0),  # Second segment: 12.5-15.0 -> 1.5-4.0
        (15.0, 18.0, 4.0, 5.0),  # Third segment (clipped): 15.0-18.0 -> 4.0-5.0
    ]

    success = True
    for i, (orig_start, orig_end, expected_start, expected_end) in enumerate(expected_conversions):
        if i < len(relevant_segments):
            seg = relevant_segments[i]
            if abs(seg['start_time'] - expected_start) > 0.001 or abs(seg['end_time'] - expected_end) > 0.001:
                print(f"❌ Timing conversion error for segment {i+1}")
                print(f"   Expected: {expected_start:.3f}s - {expected_end:.3f}s")
                print(f"   Got: {seg['start_time']:.3f}s - {seg['end_time']:.3f}s")
                success = False

    if success:
        print("✅ VTT segment timing conversion is working correctly!")
    else:
        print("❌ VTT segment timing conversion has issues!")

    return success

def test_face_region_timing_conversion():
    """Test that face regions are correctly converted to clip-relative time"""
    print("\n🧪 Testing Face Region Timing Conversion...")

    composer = CaptionComposer()

    # Create temporary tracking file
    test_job_id = "test_job"
    test_clip_id = "test_clip"
    tracking_dir = os.path.join("output", test_job_id, "temporal_tracking")
    os.makedirs(tracking_dir, exist_ok=True)

    tracking_file = os.path.join(tracking_dir, f"{test_clip_id}_tracking.json")

    # Create test tracking data
    tracking_data = {
        'tracking_data': create_test_face_regions()
    }

    with open(tracking_file, 'w') as f:
        json.dump(tracking_data, f)

    print(f"📊 Test clip start time: 11.0s")
    print(f"🎭 Original face regions (absolute time):")
    for i, region in enumerate(tracking_data['tracking_data']):
        print(f"   Face {i+1}: {region['timestamp']:.1f}s at ({region['primary_face']['x']}, {region['primary_face']['y']})")

    # Load face regions with clip start time
    clip_start_time = 11.0
    face_regions = composer._load_face_regions(test_job_id, test_clip_id, clip_start_time)

    print(f"\n✅ Converted face regions (clip-relative):")
    for i, region in enumerate(face_regions):
        print(f"   Face {i+1}: {region.timestamp:.3f}s at ({region.x}, {region.y})")

    # Verify conversion
    expected_conversions = [
        (10.5, -0.5),  # 10.5 - 11.0 = -0.5 (should be filtered out)
        (13.0, 2.0),   # 13.0 - 11.0 = 2.0
        (16.5, 5.5),   # 16.5 - 11.0 = 5.5
    ]

    # Should only have 2 face regions (the one at -0.5s should be filtered out)
    expected_count = 2
    success = len(face_regions) == expected_count

    if success:
        # Check timing conversion for remaining faces
        for i, region in enumerate(face_regions):
            expected_time = expected_conversions[i+1][1]  # Skip the filtered one
            if abs(region.timestamp - expected_time) > 0.001:
                success = False
                print(f"❌ Face region {i+1} timing conversion error")
                print(f"   Expected: {expected_time:.3f}s, Got: {region.timestamp:.3f}s")

    # Cleanup
    os.remove(tracking_file)

    if success:
        print("✅ Face region timing conversion is working correctly!")
    else:
        print("❌ Face region timing conversion has issues!")

    return success

def test_caption_face_synchronization():
    """Test that captions and faces use the same time reference for positioning"""
    print("\n🧪 Testing Caption-Face Synchronization...")

    composer = CaptionComposer()

    # Create test face regions (already in clip-relative time)
    from pipeline.tasks.caption_composer import FaceRegion
    face_regions = [
        FaceRegion(x=100, y=200, width=150, height=200, timestamp=1.5),
        FaceRegion(x=120, y=180, width=160, height=210, timestamp=2.0),
        FaceRegion(x=110, y=190, width=155, height=205, timestamp=4.5),
    ]

    # Test caption positioning at various timestamps
    test_timestamps = [1.5, 2.0, 3.0, 4.5, 6.0]

    print(f"🎭 Face regions at: {[f.timestamp for f in face_regions]}")
    print(f"📝 Testing caption positioning at timestamps: {test_timestamps}")

    success = True
    for timestamp in test_timestamps:
        position = composer._calculate_optimal_position(timestamp, face_regions, {})

        # Find nearby faces (within 1 second)
        nearby_faces = [f for f in face_regions if abs(f.timestamp - timestamp) <= 1.0]

        print(f"   Caption at {timestamp:.1f}s: position Y={position}, nearby faces: {len(nearby_faces)}")

        # Verify that positioning logic is working
        if nearby_faces and CAPTION_DYNAMIC_POSITIONING:
            # Should use face-aware positioning
            if position == 50:  # Default margin from edge
                print(f"     ⚠️  Using default position despite nearby faces")
        else:
            # Should use default positioning
            pass

    print("✅ Caption-face synchronization test completed!")
    return success

def run_all_tests():
    """Run all caption timing synchronization tests"""
    print("🚀 Running Caption Timing Synchronization Tests")
    print("=" * 60)

    tests = [
        ("VTT Segment Timing Conversion", test_vtt_segment_timing_conversion),
        ("Face Region Timing Conversion", test_face_region_timing_conversion),
        ("Caption-Face Synchronization", test_caption_face_synchronization),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {str(e)}")
            results.append((test_name, False))

    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")

    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"   {status}: {test_name}")
        if not passed:
            all_passed = False

    if all_passed:
        print("\n🎉 All caption timing synchronization tests PASSED!")
        print("🎯 Caption timing fix is working correctly!")
    else:
        print("\n⚠️  Some tests FAILED - caption timing may still have issues!")

    return all_passed

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
