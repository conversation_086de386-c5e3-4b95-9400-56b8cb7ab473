#!/usr/bin/env python3
"""
List all Smart Video Highlight Generator Pipeline jobs and their status
"""

import os
import sys
import json
import argparse
import datetime
from typing import Dict, Any, List

from pipeline.controller import controller
from config.settings import PIPELINE_OUTPUT_DIR

def get_job_info(job_id: str) -> Dict[str, Any]:
    """
    Get information about a job

    Args:
        job_id: Unique identifier for the job

    Returns:
        Dictionary containing job information
    """
    # Get job directory
    job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
    if not os.path.exists(job_dir):
        return {
            'job_id': job_id,
            'status': 'not_found',
            'created_at': None,
            'last_modified': None
        }

    # Get job parameters
    params_path = os.path.join(job_dir, "params.json")
    params = None
    if os.path.exists(params_path):
        try:
            with open(params_path, 'r') as f:
                params = json.load(f)
        except Exception:
            pass

    # Get job progress
    progress = controller.get_job_progress(job_id)

    # Get creation and modification times
    created_at = datetime.datetime.fromtimestamp(os.path.getctime(job_dir))
    last_modified = datetime.datetime.fromtimestamp(os.path.getmtime(job_dir))

    # Get completed stages
    state_dir = os.path.join(job_dir, "state")
    completed_stages = []
    if os.path.exists(state_dir):
        for state_file in os.listdir(state_dir):
            if state_file.endswith('.json'):
                stage_name = os.path.splitext(state_file)[0]
                state_path = os.path.join(state_dir, state_file)
                try:
                    with open(state_path, 'r') as f:
                        state = json.load(f)
                    if state.get('status') == 'completed':
                        completed_stages.append(stage_name)
                except Exception:
                    pass

    return {
        'job_id': job_id,
        'status': progress.get('status', 'unknown'),
        'created_at': created_at,
        'last_modified': last_modified,
        'video_path': params.get('video_path') if params else None,
        'keywords': params.get('keywords') if params else None,
        'completed_stages': completed_stages,
        'progress': progress.get('progress', {})
    }

def list_jobs(detailed: bool = False) -> List[Dict[str, Any]]:
    """
    List all jobs

    Args:
        detailed: Whether to include detailed information

    Returns:
        List of job information dictionaries
    """
    # Get all job directories
    job_dirs = []
    if os.path.exists(PIPELINE_OUTPUT_DIR):
        job_dirs = [d for d in os.listdir(PIPELINE_OUTPUT_DIR) if os.path.isdir(os.path.join(PIPELINE_OUTPUT_DIR, d))]

    # Get information for each job
    jobs = []
    for job_id in job_dirs:
        job_info = get_job_info(job_id)
        jobs.append(job_info)

    # Sort by last modified time (newest first)
    jobs.sort(key=lambda j: j['last_modified'] if j['last_modified'] else datetime.datetime.min, reverse=True)

    return jobs

def main():
    """Main entry point for the script"""
    parser = argparse.ArgumentParser(description="List all pipeline jobs")
    parser.add_argument("--detailed", help="Show detailed job information", action="store_true")
    parser.add_argument("--json", help="Output in JSON format", action="store_true")

    args = parser.parse_args()

    # List jobs
    jobs = list_jobs(args.detailed)

    # Output in JSON format
    if args.json:
        # Convert datetime objects to strings
        for job in jobs:
            if job['created_at']:
                job['created_at'] = job['created_at'].isoformat()
            if job['last_modified']:
                job['last_modified'] = job['last_modified'].isoformat()

        print(json.dumps(jobs, indent=2))
        return 0

    # Output in human-readable format
    if not jobs:
        print("No jobs found")
        return 0

    print(f"Found {len(jobs)} jobs:")
    print()

    for i, job in enumerate(jobs):
        print(f"{i+1}. Job ID: {job['job_id']}")
        print(f"   Status: {job['status']}")

        if job['created_at']:
            print(f"   Created: {job['created_at'].strftime('%Y-%m-%d %H:%M:%S')}")

        if job['last_modified']:
            print(f"   Last modified: {job['last_modified'].strftime('%Y-%m-%d %H:%M:%S')}")

        if job['video_path']:
            print(f"   Video: {job['video_path']}")

        if job['keywords']:
            print(f"   Keywords: {', '.join(job['keywords'])}")

        if job['completed_stages']:
            print(f"   Completed stages: {', '.join(job['completed_stages'])}")

        if args.detailed and job['progress']:
            print("   Progress:")
            for stage, info in job['progress'].items():
                print(f"     - {stage}: {info.get('status', 'unknown')}")

        print()

    return 0

if __name__ == "__main__":
    sys.exit(main())
