"""
Language Detection Utility for Caption Rendering

This module provides language detection capabilities to automatically
select appropriate fonts for different scripts and languages.
"""

import re
import unicodedata
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class LanguageDetector:
    """Detects language and script from text to enable proper font selection"""
    
    # Unicode ranges for different scripts
    SCRIPT_RANGES = {
        'devanagari': [(0x0900, 0x097F)],  # Hindi, Sanskrit, Marathi, Nepali
        'arabic': [(0x0600, 0x06FF), (0x0750, 0x077F), (0x08A0, 0x08FF)],
        'chinese': [(0x4E00, 0x9FFF), (0x3400, 0x4DBF)],  # CJK Unified Ideographs
        'japanese': [(0x3040, 0x309F), (0x30A0, 0x30FF)],  # Hiragana, Katakana
        'korean': [(0xAC00, 0xD7AF)],  # Hangul
        'cyrillic': [(0x0400, 0x04FF)],  # Russian, Bulgarian, etc.
        'greek': [(0x0370, 0x03FF)],
        'thai': [(0x0E00, 0x0E7F)],
        'latin': [(0x0020, 0x007F), (0x00A0, 0x00FF), (0x0100, 0x017F)]  # Basic Latin + extensions
    }
    
    # Language-specific patterns
    LANGUAGE_PATTERNS = {
        'hindi': {
            'scripts': ['devanagari'],
            'common_words': ['है', 'में', 'को', 'का', 'की', 'के', 'से', 'पर', 'और', 'या'],
            'font_preference': 'hindi'
        },
        'arabic': {
            'scripts': ['arabic'],
            'common_words': ['في', 'من', 'إلى', 'على', 'هذا', 'التي', 'أن', 'كان'],
            'font_preference': 'arabic'
        },
        'chinese': {
            'scripts': ['chinese'],
            'common_words': ['的', '是', '在', '有', '我', '他', '这', '个', '们', '中'],
            'font_preference': 'chinese'
        },
        'english': {
            'scripts': ['latin'],
            'common_words': ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with'],
            'font_preference': 'default'
        }
    }
    
    def __init__(self):
        """Initialize the language detector"""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def detect_script(self, text: str) -> Dict[str, float]:
        """
        Detect the script(s) used in the text
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dictionary mapping script names to their percentage in the text
        """
        if not text:
            return {'latin': 1.0}
        
        # Count characters by script
        script_counts = {script: 0 for script in self.SCRIPT_RANGES.keys()}
        total_chars = 0
        
        for char in text:
            if char.isspace() or char in '.,!?;:()[]{}"\'-':
                continue
                
            char_code = ord(char)
            total_chars += 1
            
            # Check which script this character belongs to
            for script, ranges in self.SCRIPT_RANGES.items():
                for start, end in ranges:
                    if start <= char_code <= end:
                        script_counts[script] += 1
                        break
        
        if total_chars == 0:
            return {'latin': 1.0}
        
        # Calculate percentages
        script_percentages = {
            script: count / total_chars 
            for script, count in script_counts.items() 
            if count > 0
        }
        
        # If no script detected, assume Latin
        if not script_percentages:
            script_percentages['latin'] = 1.0
        
        return script_percentages
    
    def detect_language(self, text: str) -> Tuple[str, float]:
        """
        Detect the most likely language of the text
        
        Args:
            text: Input text to analyze
            
        Returns:
            Tuple of (language_code, confidence_score)
        """
        if not text:
            return 'english', 1.0
        
        # First detect scripts
        script_percentages = self.detect_script(text)
        
        # Score languages based on script usage and common words
        language_scores = {}
        
        for lang, config in self.LANGUAGE_PATTERNS.items():
            score = 0.0
            
            # Script-based scoring
            for script in config['scripts']:
                if script in script_percentages:
                    score += script_percentages[script] * 0.7
            
            # Common words scoring
            text_lower = text.lower()
            word_matches = sum(1 for word in config['common_words'] if word in text_lower)
            if config['common_words']:
                word_score = word_matches / len(config['common_words'])
                score += word_score * 0.3
            
            if score > 0:
                language_scores[lang] = score
        
        if not language_scores:
            return 'english', 0.5
        
        # Return language with highest score
        best_language = max(language_scores.items(), key=lambda x: x[1])
        return best_language[0], min(best_language[1], 1.0)
    
    def get_font_preference(self, text: str) -> str:
        """
        Get the recommended font preference for the given text
        
        Args:
            text: Input text to analyze
            
        Returns:
            Font preference key (e.g., 'hindi', 'arabic', 'default')
        """
        language, confidence = self.detect_language(text)
        
        if confidence < 0.3:
            # Low confidence, check script directly
            scripts = self.detect_script(text)
            if 'devanagari' in scripts and scripts['devanagari'] > 0.5:
                return 'hindi'
            elif 'arabic' in scripts and scripts['arabic'] > 0.5:
                return 'arabic'
            elif 'chinese' in scripts and scripts['chinese'] > 0.5:
                return 'chinese'
        
        # Use language-based preference
        if language in self.LANGUAGE_PATTERNS:
            return self.LANGUAGE_PATTERNS[language]['font_preference']
        
        return 'default'
    
    def is_rtl_script(self, text: str) -> bool:
        """
        Check if the text contains right-to-left script
        
        Args:
            text: Input text to analyze
            
        Returns:
            True if text contains RTL script
        """
        scripts = self.detect_script(text)
        rtl_scripts = ['arabic']
        
        return any(script in scripts and scripts[script] > 0.1 for script in rtl_scripts)
    
    def analyze_text(self, text: str) -> Dict:
        """
        Comprehensive text analysis for caption rendering
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dictionary with analysis results
        """
        language, confidence = self.detect_language(text)
        scripts = self.detect_script(text)
        font_preference = self.get_font_preference(text)
        is_rtl = self.is_rtl_script(text)
        
        return {
            'language': language,
            'confidence': confidence,
            'scripts': scripts,
            'font_preference': font_preference,
            'is_rtl': is_rtl,
            'primary_script': max(scripts.items(), key=lambda x: x[1])[0] if scripts else 'latin'
        }


# Global instance for easy access
language_detector = LanguageDetector()
