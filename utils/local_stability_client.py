import os
import torch
from diffusers import (
    StableDiffusionPipeline,
    DPMSolverMultistepScheduler,
    AutoPipelineForText2Image
)
from PIL import Image
from config.settings import (
    LOCAL_SD_MODEL_PATH,
    LOCAL_SD_DEVICE,
    LOCAL_SD_PRECISION,
    LOCAL_SD_ENABLE_XFORMERS
)
from huggingface_hub import login
import re

class LocalStabilityClient:
    """Client for running Stable Diffusion locally"""

    def __init__(self):
        self.model = None
        # Map 'gpu' to 'cuda' for PyTorch compatibility
        if LOCAL_SD_DEVICE.lower() == 'gpu':
            print("Note: 'gpu' device name mapped to 'cuda' for PyTorch compatibility")
            self.device = 'cuda'
        else:
            self.device = LOCAL_SD_DEVICE
        self.precision = LOCAL_SD_PRECISION
        self.model_path = LOCAL_SD_MODEL_PATH
        self.enable_xformers = LOCAL_SD_ENABLE_XFORMERS

        # Detect model types
        self.is_sd3 = "stable-diffusion-3" in self.model_path.lower()
        self.is_sd21 = "stable-diffusion-2-1" in self.model_path.lower()

        # Check if CUDA is available when device is set to 'cuda'
        if self.device == 'cuda' and not torch.cuda.is_available():
            print("Warning: CUDA requested but not available. Falling back to CPU.")
            self.device = 'cpu'

        print(f"Local Stability Client initialized with device: {self.device}")
        print(f"Using model: {self.model_path}")

        # Check which model type we're using
        if self.is_sd3:
            print("Detected Stable Diffusion 3.x model")
        elif self.is_sd21:
            print("Detected Stable Diffusion 2.1 model")
        elif "sdxl" in self.model_path.lower() or "stable-diffusion-xl" in self.model_path.lower():
            print("Detected Stable Diffusion XL model")
        else:
            print("Using standard Stable Diffusion model")

    def load_model(self):
        """Load the Stable Diffusion model"""
        if self.model is not None:
            return True

        print(f"Loading Stable Diffusion model from {self.model_path}...")

        # Set the appropriate torch dtype based on precision setting
        if self.precision == 'float16' and self.device != 'cpu':
            torch_dtype = torch.float16
        else:
            torch_dtype = torch.float32

        # Check VRAM requirements
        if self.device == 'cuda':
            total_vram = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"Available GPU VRAM: {total_vram:.2f} GB")

            # Warn about VRAM requirements
            if self.is_sd3 and total_vram < 16:
                print(f"WARNING: SD3 models typically require 16+ GB VRAM, but only {total_vram:.2f} GB available")
                print("This may cause out-of-memory errors. Consider using a smaller model.")
            elif ("sdxl" in self.model_path.lower() or "stable-diffusion-xl" in self.model_path.lower()) and total_vram < 8:
                print(f"WARNING: SDXL models typically require 8+ GB VRAM, but only {total_vram:.2f} GB available")
                print("This may cause out-of-memory errors. Consider using a smaller model.")

        # Load the model
        try:
            # Check if we need to authenticate with Hugging Face
            if self.is_sd3 and "stabilityai/stable-diffusion-3" in self.model_path:
                print("This is a gated model that requires Hugging Face authentication.")
                print("Attempting to use cached authentication or environment variables...")

                # Try to use token from environment variables
                hf_token = os.environ.get("HF_TOKEN") or os.environ.get("HUGGING_FACE_HUB_TOKEN")
                if hf_token:
                    print(f"Using token from environment variables: {hf_token[:5]}...")
                    # Set both environment variables
                    os.environ["HF_TOKEN"] = hf_token
                    os.environ["HUGGING_FACE_HUB_TOKEN"] = hf_token
                    login(token=hf_token)
                else:
                    print("ERROR: No Hugging Face token found in environment variables")
                    print("SD3 models require authentication. Please set HF_TOKEN in your .env file")
                    return False

            # Choose the appropriate pipeline based on model type
            try:
                if self.is_sd3:
                    print("Loading SD3 model with AutoPipelineForText2Image...")
                    print("This may take several minutes to download and load...")
                    self.model = AutoPipelineForText2Image.from_pretrained(
                        self.model_path,
                        torch_dtype=torch_dtype,
                        use_safetensors=True,
                        variant="fp16" if torch_dtype == torch.float16 else None,
                        low_cpu_mem_usage=True
                    )
                else:
                    print("Loading standard Stable Diffusion model...")
                    self.model = StableDiffusionPipeline.from_pretrained(
                        self.model_path,
                        torch_dtype=torch_dtype,
                        safety_checker=None,
                        local_files_only=True,
                        use_safetensors=True,
                        low_cpu_mem_usage=True
                    )
            except Exception as e:
                print(f"Error during model loading: {e}")
                import traceback
                traceback.print_exc()
                return False

            # Move model to device
            try:
                print(f"Moving model to {self.device}...")
                if hasattr(self.model, "config"):
                    # disable aesthetic scoring head so time-embedding dims match
                    self.model.config.requires_aesthetics_score = False

                self.model = self.model.to(self.device)
                self.model.enable_attention_slicing()
            except Exception as e:
                print(f"Error moving model to device: {e}")
                import traceback
                traceback.print_exc()
                return False

            # Enable memory optimizations if requested and supported
            if self.enable_xformers and self.device == 'cuda':
                try:
                    self.model.enable_xformers_memory_efficient_attention()
                    print("Enabled xformers memory efficient attention")
                except Exception as e:
                    print(f"Could not enable xformers: {e}")

            # Use DPMSolver for faster inference if not SD3 (SD3 has its own scheduler)
            if not self.is_sd3:
                try:
                    self.model.scheduler = DPMSolverMultistepScheduler.from_config(
                        self.model.scheduler.config,
                        algorithm_type="dpmsolver++",
                        use_karras_sigmas=True
                    )
                except Exception as e:
                    print(f"Could not set scheduler: {e}")

            print("Model loaded successfully")

            return True
        except Exception as e:
            print(f"Error loading model: {e}")
            import traceback
            traceback.print_exc()
            return False

    def generate_image(self, prompt, size, output_file, negative_prompt=None, cfg_scale=7, steps=30, seed=None):
        """
        Generate image using local Stable Diffusion model

        Args:
            prompt (str): The prompt to generate an image from
            size (str): Size in format "width x height" (e.g., "768x1280")
            output_file (str): Path to save the generated image
            negative_prompt (str, optional): Things to avoid in the image
            cfg_scale (float, optional): How strictly to follow the prompt (1-35)
            steps (int, optional): Number of diffusion steps (10-50)
            seed (int, optional): Random seed for reproducibility

        Returns:
            str: Path to the generated image file, or None if generation failed
        """
        try:
            # Load model if not already loaded
            if self.model is None:
                if not self.load_model():
                    return None

            # Parse size string (e.g., "768x1280" to width and height)
            width, height = map(int, size.split("x"))

            # Ensure dimensions are multiples of 8 (required by some SD models)
            width = (width // 8) * 8
            height = (height // 8) * 8

            # Set random seed if provided
            generator = None
            if seed is not None:
                generator = torch.Generator(device=self.device).manual_seed(seed)

            # Generate the image
            print(f"Generating image with prompt: {prompt}")
            print(f"Size: {width}x{height}, Steps: {steps}, CFG Scale: {cfg_scale}")

            # Standard generation
            with torch.no_grad():
                # Different handling based on model type
                if self.is_sd3:
                    # SD3 models use a different parameter set
                    image = self.model(
                        prompt=prompt,
                        negative_prompt=negative_prompt,
                        width=width,
                        height=height,
                        num_inference_steps=steps,
                        guidance_scale=cfg_scale,
                        generator=generator,
                    ).images[0]
                elif "sdxl" in self.model_path.lower() or "stable-diffusion-xl" in self.model_path.lower():
                    # SDXL models might need additional parameters
                    image = self.model(
                        prompt=prompt,
                        negative_prompt=negative_prompt,
                        width=width,
                        height=height,
                        num_inference_steps=steps,
                        guidance_scale=cfg_scale,
                        generator=generator,
                    ).images[0]
                else:
                    # Standard SD models
                    image = self.model(
                        prompt=prompt,
                        negative_prompt=negative_prompt,
                        width=width,
                        height=height,
                        num_inference_steps=steps,
                        guidance_scale=cfg_scale,
                        generator=generator,
                    ).images[0]

            # Save the image
            image.save(output_file)
            print(f"Image saved to {output_file}")
            return output_file

        except Exception as e:
            print(f"Error generating image with local Stable Diffusion: {e}")
            return None

    def unload_model(self):
        """Unload the model to free up GPU memory"""
        # Unload model if it exists
        if self.model is not None:
            del self.model
            self.model = None
            print("Model unloaded from memory")

        # Force CUDA to release memory
        if self.device == 'cuda':
            torch.cuda.empty_cache()
            print("CUDA cache cleared")
