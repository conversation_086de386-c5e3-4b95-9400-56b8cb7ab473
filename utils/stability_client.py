import os
import json
import base64
import requests
from config.settings import STAB<PERSON>ITY_API_KEY, STABILITY_API_HOST

class StabilityClient:
    """Wrapper for Stability AI API client"""

    def __init__(self):
        self.api_key = STABILITY_API_KEY
        self.api_host = STABILITY_API_HOST or "https://api.stability.ai"

        if not self.api_key:
            raise ValueError("STABILITY_API_KEY environment variable is not set. Please create a .env file with your API key.")

    def generate_image(self, prompt, model, size, output_file, negative_prompt=None, cfg_scale=7, steps=30, style_preset=None):
        """
        Generate image using Stability AI's Stable Diffusion API

        Args:
            prompt (str): The prompt to generate an image from
            model (str): The model to use (e.g., "sd3")
            size (str): Size in format "width x height" (e.g., "768x1280")
            output_file (str): Path to save the generated image
            negative_prompt (str, optional): Things to avoid in the image
            cfg_scale (float, optional): How strictly to follow the prompt (1-35)
            steps (int, optional): Number of diffusion steps (10-50)
            style_preset (str, optional): Style preset to use

        Returns:
            str: Path to the generated image file, or None if generation failed
        """
        try:
            # Parse size string (e.g., "768x1280" to width and height)
            width, height = map(int, size.split("x"))

            # For SD3, use the v2beta API with multipart/form-data
            if model == "sd3" or model == "sd3-medium":
                # Set up the API endpoint for SD3
                api_endpoint = f"{self.api_host}/v2beta/stable-image/generate/sd3"

                # Set up headers
                headers = {
                    "Accept": "image/*",
                    "Authorization": f"Bearer {self.api_key}"
                }

                # Prepare form data
                form_data = {
                    "prompt": prompt,
                    "output_format": "png",
                    "width": str(width),
                    "height": str(height)
                }

                # Add negative prompt if provided
                if negative_prompt:
                    form_data["negative_prompt"] = negative_prompt

                # Make the API request
                response = requests.post(
                    api_endpoint,
                    headers=headers,
                    files={k: (None, v) for k, v in form_data.items()}
                )

                # Check if the request was successful
                if response.status_code != 200:
                    print(f"Error generating image: {response.status_code} {response.text}")
                    return None

                # Save the image directly from the response content
                with open(output_file, "wb") as f:
                    f.write(response.content)
                return output_file

            else:
                # Legacy API for older models
                # Prepare the request payload
                payload = {
                    "text_prompts": [
                        {
                            "text": prompt,
                            "weight": 1.0
                        }
                    ],
                    "cfg_scale": cfg_scale,
                    "steps": steps,
                    "width": width,
                    "height": height
                }

                # Add negative prompt if provided
                if negative_prompt:
                    payload["text_prompts"].append({
                        "text": negative_prompt,
                        "weight": -1.0
                    })

                # Add style preset if provided
                if style_preset:
                    payload["style_preset"] = style_preset

                # Set up the API endpoint
                engine_id = model
                api_endpoint = f"{self.api_host}/v1/generation/{engine_id}/text-to-image"

                # Set up headers for legacy API
                headers = {
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                    "Authorization": f"Bearer {self.api_key}"
                }

                # Make the API request for legacy API
                response = requests.post(
                    api_endpoint,
                    headers=headers,
                    json=payload
                )

                # Check if the request was successful
                if response.status_code != 200:
                    print(f"Error generating image: {response.status_code} {response.text}")
                    return None

                # Parse the response
                data = response.json()

                # Save the image
                if "artifacts" in data and len(data["artifacts"]) > 0:
                    for i, image in enumerate(data["artifacts"]):
                        # For this implementation, we only save the first image
                        if i == 0:
                            image_data = base64.b64decode(image["base64"])
                            with open(output_file, "wb") as f:
                                f.write(image_data)
                            return output_file

                print("No image artifacts found in the response")
                return None

        except Exception as e:
            print(f"Error generating image with Stability AI: {e}")
            return None
