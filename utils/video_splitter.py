#!/usr/bin/env python3
"""
Video Splitter Utility for Long Video Processing

This module provides functionality to split videos longer than 90 minutes
into multiple parts for processing within the pipeline constraints.
"""

import os
import json
import logging
import subprocess
import tempfile
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import ffmpeg

logger = logging.getLogger(__name__)


class VideoSplitter:
    """
    Utility class for splitting long videos into manageable parts
    """

    def __init__(self, max_duration: int = 14400):  # 240 minutes (4 hours) default
        self.max_duration = max_duration
        self.logger = logging.getLogger(self.__class__.__name__)

    def needs_splitting(self, video_path: str) -> bool:
        """
        Check if a video needs to be split based on duration

        Args:
            video_path: Path to the video file

        Returns:
            True if video duration exceeds max_duration
        """
        try:
            probe = ffmpeg.probe(video_path)
            duration = float(probe['format'].get('duration', 0))
            return duration > self.max_duration
        except Exception as e:
            self.logger.error(f"Error checking video duration: {e}")
            return False

    def get_video_duration(self, video_path: str) -> float:
        """
        Get video duration in seconds

        Args:
            video_path: Path to the video file

        Returns:
            Duration in seconds
        """
        try:
            probe = ffmpeg.probe(video_path)
            return float(probe['format'].get('duration', 0))
        except Exception as e:
            self.logger.error(f"Error getting video duration: {e}")
            return 0.0

    def calculate_split_points(self, total_duration: float) -> List[Tuple[float, float]]:
        """
        Calculate optimal split points for a video

        Args:
            total_duration: Total video duration in seconds

        Returns:
            List of (start_time, duration) tuples for each part
        """
        parts = []
        current_start = 0.0

        while current_start < total_duration:
            # Calculate remaining duration
            remaining = total_duration - current_start

            # Use max_duration or remaining time, whichever is smaller
            part_duration = min(self.max_duration, remaining)

            parts.append((current_start, part_duration))
            current_start += part_duration

        self.logger.info(f"Calculated {len(parts)} parts for {total_duration:.2f}s video")
        return parts

    def split_video(self, video_path: str, output_dir: str, base_name: str = None) -> List[Dict[str, Any]]:
        """
        Split a video into multiple parts

        Args:
            video_path: Path to the input video file
            output_dir: Directory to save the split parts
            base_name: Base name for output files (defaults to original filename)

        Returns:
            List of dictionaries containing information about each part
        """
        if not self.needs_splitting(video_path):
            self.logger.info("Video does not need splitting")
            return []

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Get video duration and calculate split points
        total_duration = self.get_video_duration(video_path)
        split_points = self.calculate_split_points(total_duration)

        # Determine base name
        if base_name is None:
            base_name = Path(video_path).stem

        parts_info = []

        for i, (start_time, duration) in enumerate(split_points, 1):
            # Generate output filename
            part_filename = f"{base_name}_part{i}.mp4"
            output_path = os.path.join(output_dir, part_filename)

            # Split the video using FFmpeg
            success = self._extract_video_part(video_path, output_path, start_time, duration)

            if success:
                part_info = {
                    'part_number': i,
                    'filename': part_filename,
                    'path': output_path,
                    'start_time': start_time,
                    'duration': duration,
                    'end_time': start_time + duration,
                    'original_video': video_path
                }
                parts_info.append(part_info)
                self.logger.info(f"Created part {i}: {part_filename} ({start_time:.1f}s - {start_time + duration:.1f}s)")
            else:
                self.logger.error(f"Failed to create part {i}")
                # Clean up any partial files
                if os.path.exists(output_path):
                    os.remove(output_path)

        self.logger.info(f"Successfully split video into {len(parts_info)} parts")
        return parts_info

    def _extract_video_part(self, input_path: str, output_path: str,
                           start_time: float, duration: float) -> bool:
        """
        Extract a video part using FFmpeg

        Args:
            input_path: Path to the input video
            output_path: Path for the output part
            start_time: Start time in seconds
            duration: Duration in seconds

        Returns:
            True if successful, False otherwise
        """
        try:
            # Use FFmpeg to extract the part with stream copying for speed
            cmd = [
                'ffmpeg',
                '-i', input_path,
                '-ss', str(start_time),
                '-t', str(duration),
                '-c', 'copy',  # Copy streams without re-encoding for speed
                '-avoid_negative_ts', 'make_zero',
                '-y',  # Overwrite output file
                output_path
            ]

            self.logger.debug(f"Running FFmpeg command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                # Verify the output file was created and has reasonable size
                if os.path.exists(output_path) and os.path.getsize(output_path) > 1024:  # At least 1KB
                    return True
                else:
                    self.logger.error(f"Output file not created or too small: {output_path}")
                    return False
            else:
                self.logger.error(f"FFmpeg failed: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            self.logger.error(f"FFmpeg timeout while extracting part")
            return False
        except Exception as e:
            self.logger.error(f"Error extracting video part: {e}")
            return False

    def get_split_info_summary(self, parts_info: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a summary of the split operation

        Args:
            parts_info: List of part information dictionaries

        Returns:
            Summary dictionary
        """
        if not parts_info:
            return {}

        total_duration = sum(part['duration'] for part in parts_info)

        return {
            'total_parts': len(parts_info),
            'total_duration': total_duration,
            'original_video': parts_info[0]['original_video'],
            'parts': [
                {
                    'part_number': part['part_number'],
                    'filename': part['filename'],
                    'duration': part['duration'],
                    'time_range': f"{part['start_time']:.1f}s - {part['end_time']:.1f}s"
                }
                for part in parts_info
            ]
        }


# Create a default instance
video_splitter = VideoSplitter()


def split_long_video(video_path: str, output_dir: str, base_name: str = None,
                    max_duration: int = 14400) -> List[Dict[str, Any]]:
    """
    Split a long video into parts if needed

    Args:
        video_path: Path to the input video file
        output_dir: Directory to save the split parts
        base_name: Base name for output files
        max_duration: Maximum duration per part in seconds

    Returns:
        List of part information dictionaries
    """
    splitter = VideoSplitter(max_duration=max_duration)
    return splitter.split_video(video_path, output_dir, base_name)


def check_video_needs_splitting(video_path: str, max_duration: int = 14400) -> bool:
    """
    Check if a video needs splitting

    Args:
        video_path: Path to the video file
        max_duration: Maximum allowed duration in seconds

    Returns:
        True if video needs splitting
    """
    splitter = VideoSplitter(max_duration=max_duration)
    return splitter.needs_splitting(video_path)
