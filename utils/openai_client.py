import os
import json
import re
import requests
from openai import OpenAI
from config.settings import OPENAI_API_KEY

class OpenAIClient:
    """Wrapper for OpenAI API client"""

    def __init__(self):
        self.client = OpenAI(api_key=OPENAI_API_KEY)

    def generate_text(self, prompt, model, temperature=0.7, max_tokens=500):
        """Generate text using OpenAI's GPT models"""
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"Error generating text: {e}")
            return None

    def generate_speech(self, text, model, voice, output_file):
        """Generate speech using OpenAI's text-to-speech API"""
        try:
            response = self.client.audio.speech.create(
                model=model,
                voice=voice,
                input=text
            )
            response.stream_to_file(output_file)
            return output_file
        except Exception as e:
            print(f"Error generating speech: {e}")
            return None

    def generate_json(self, prompt, model, schema=None, temperature=0.7, max_tokens=1000, max_retries=3):
        """
        Generate a JSON response using OpenAI's GPT models with robust parsing

        Args:
            prompt (str): The prompt to send to the API
            model (str): The model to use
            schema (dict, optional): JSON schema to validate against
            temperature (float): Temperature parameter for generation
            max_tokens (int): Maximum tokens to generate
            max_retries (int): Maximum number of retries for JSON parsing

        Returns:
            dict or list: Parsed JSON object or None if failed
        """
        # Add explicit instructions for JSON formatting
        json_instruction = """
        IMPORTANT: Your response must be valid JSON. Do not include any explanations, markdown formatting, or anything else.
        Only return a valid JSON object or array.
        """

        # If schema is provided, add it to the prompt
        schema_instruction = ""
        if schema:
            schema_instruction = f"""
            Your response must conform to this JSON schema:
            {json.dumps(schema, indent=2)}
            """

        # Combine the prompt with JSON instructions
        full_prompt = f"{prompt}\n\n{json_instruction}\n{schema_instruction}"

        for attempt in range(max_retries):
            try:
                # Generate response
                response_format = {"type": "json_object"}

                # Check if the model supports response_format parameter
                # Some models like GPT-4 might not support it in all API versions
                try:
                    response = self.client.chat.completions.create(
                        model=model,
                        messages=[{"role": "user", "content": full_prompt}],
                        temperature=temperature,
                        max_tokens=max_tokens,
                        response_format=response_format
                    )
                except Exception as format_error:
                    print(f"Warning: Could not use response_format parameter: {format_error}")
                    # Fallback to standard request without response_format
                    response = self.client.chat.completions.create(
                        model=model,
                        messages=[{"role": "user", "content": full_prompt}],
                        temperature=temperature,
                        max_tokens=max_tokens
                    )

                content = response.choices[0].message.content
                print(f"Raw JSON response: {content[:200]}...")  # Print first 200 chars for debugging

                # Try to parse the JSON
                try:
                    # Clean the response - remove any markdown code blocks or extra text
                    content = self._clean_json_response(content)
                    parsed_json = json.loads(content)
                    return parsed_json
                except json.JSONDecodeError as e:
                    print(f"JSON decode error: {e}")
                    print(f"Content causing error: {content[:100]}...")  # Print first 100 chars

                    if attempt < max_retries - 1:
                        # If parsing fails, try again with more explicit instructions
                        full_prompt = f"""
                        The previous response could not be parsed as valid JSON. Error: {str(e)}

                        {full_prompt}

                        CRITICAL: Ensure your response is ONLY valid JSON with no other text.
                        Format your response as a JSON array of objects with these fields:
                        - start_time: number (seconds)
                        - end_time: number (seconds)
                        - keyword: string
                        - description: string
                        - relevance_score: number (1-10)
                        """
                    else:
                        print(f"Failed to parse JSON after {max_retries} attempts: {e}")
                        # Try to salvage what we can - return a minimal valid structure
                        return [{"start_time": 0, "end_time": 30, "keyword": "highlight",
                                "description": "Automatically generated highlight", "relevance_score": 5}]
            except Exception as e:
                print(f"Error generating JSON (attempt {attempt+1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    # Return a minimal valid structure as fallback
                    return [{"start_time": 0, "end_time": 30, "keyword": "highlight",
                            "description": "Automatically generated highlight", "relevance_score": 5}]

        # Return a minimal valid structure as fallback
        return [{"start_time": 0, "end_time": 30, "keyword": "highlight",
                "description": "Automatically generated highlight", "relevance_score": 5}]

    def _clean_json_response(self, response):
        """
        Clean a response to extract only the JSON part

        Args:
            response (str): The response from the API

        Returns:
            str: Cleaned JSON string
        """
        # Remove markdown code blocks if present
        json_pattern = r"```(?:json)?(.*?)```"
        matches = re.findall(json_pattern, response, re.DOTALL)

        if matches:
            # Use the first code block that contains valid JSON
            for match in matches:
                cleaned = match.strip()
                try:
                    # Test if it's valid JSON
                    json.loads(cleaned)
                    return cleaned
                except json.JSONDecodeError:
                    continue

        # If no code blocks or none contain valid JSON, try to find JSON-like content
        # Look for content between { and } or [ and ]
        if response.strip().startswith("{") and "}" in response:
            # Find the last closing brace
            end_index = response.rstrip().rfind("}")
            if end_index > 0:
                return response[:end_index+1].strip()
        elif response.strip().startswith("[") and "]" in response:
            # Find the last closing bracket
            end_index = response.rstrip().rfind("]")
            if end_index > 0:
                return response[:end_index+1].strip()

        # If all else fails, return the original response
        return response.strip()

    def generate_image(self, prompt, model, size, quality, style, output_file):
        """Generate image using OpenAI's DALL-E API"""
        try:
            response = self.client.images.generate(
                model=model,
                prompt=prompt,
                size=size,
                quality=quality,
                style=style,
                n=1
            )

            # Download the image
            image_url = response.data[0].url
            image_data = requests.get(image_url).content

            with open(output_file, 'wb') as f:
                f.write(image_data)

            return output_file
        except Exception as e:
            print(f"Error generating image: {e}")
            return None

    def transcribe_audio(self, audio_file, model="whisper-1", response_format="verbose_json", temperature=0.0, language=None):
        """
        Transcribe audio using OpenAI's Whisper API

        Args:
            audio_file (str): Path to the audio file
            model (str): The model to use for transcription
            response_format (str): The format of the response (json, text, srt, verbose_json, or vtt)
            temperature (float): Temperature parameter for generation
            language (str, optional): Language code (e.g., 'en') to use for transcription

        Returns:
            dict or str: Transcription data or None if failed
        """
        try:
            # Open the audio file
            with open(audio_file, "rb") as f:
                # Create transcription request
                params = {
                    "model": model,
                    "response_format": response_format,
                    "temperature": temperature
                }

                # Add language if specified
                if language:
                    params["language"] = language

                # Make the API call
                response = self.client.audio.transcriptions.create(
                    file=f,
                    **params
                )

                # Process the response based on format
                if response_format == "verbose_json" or response_format == "json":
                    # For JSON formats, return the parsed data
                    if hasattr(response, "model_dump"):
                        # Handle pydantic model response
                        return response.model_dump()
                    else:
                        # Handle direct dictionary response
                        return response
                else:
                    # For text formats, return the text content
                    return response.text if hasattr(response, "text") else str(response)

        except Exception as e:
            print(f"Error transcribing audio: {e}")
            return None
