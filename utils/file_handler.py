import os
import json
import re
import shutil
from datetime import datetime
from config.settings import OUTPUT_DIR, TEMP_DIR

class FileHandler:
    """Utility for handling file operations"""

    @staticmethod
    def create_output_dirs(topic, project_type="video", existing_dir=None):
        """
        Create output directories for a specific topic

        Args:
            topic (str): The topic or name for the project
            project_type (str): Type of project ("video" or "highlight")
            existing_dir (str, optional): Path to an existing directory to reuse

        Returns:
            dict: Dictionary containing output directory paths
        """
        # Create a sanitized version of the topic for directory naming
        # Convert to lowercase and replace spaces with underscores
        sanitized_topic = topic.lower().replace(" ", "_")
        # Remove all symbols (keep only alphanumeric characters and underscores)
        sanitized_topic = re.sub(r'[^\w]', '', sanitized_topic)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Use existing directory if provided, otherwise create a new one
        if existing_dir and os.path.exists(existing_dir):
            project_dir = existing_dir
            print(f"Reusing existing project directory: {project_dir}")
        else:
            # Create project directory
            project_dir = os.path.join(OUTPUT_DIR, f"{sanitized_topic}_{timestamp}")
            print(f"Creating new project directory: {project_dir}")

        os.makedirs(project_dir, exist_ok=True)

        # Create common subdirectories
        audio_dir = os.path.join(project_dir, "audio")
        video_dir = os.path.join(project_dir, "video")

        # Create directories based on project type
        if project_type == "highlight":
            # Directories specific to highlight generator
            transcript_dir = os.path.join(project_dir, "transcript")
            metadata_dir = os.path.join(project_dir, "metadata")

            # Create all directories
            for directory in [audio_dir, video_dir, transcript_dir, metadata_dir]:
                os.makedirs(directory, exist_ok=True)

            return {
                "project_dir": project_dir,
                "audio_dir": audio_dir,
                "video_dir": video_dir,
                "transcript_dir": transcript_dir,
                "metadata_dir": metadata_dir
            }
        else:
            # Standard video generation directories
            facts_dir = os.path.join(project_dir, "facts")
            script_dir = os.path.join(project_dir, "script")
            images_dir = os.path.join(project_dir, "images")
            social_media_dir = os.path.join(project_dir, "social_media")

            # Create all directories
            for directory in [facts_dir, script_dir, audio_dir, images_dir, video_dir, social_media_dir]:
                os.makedirs(directory, exist_ok=True)

            return {
                "project_dir": project_dir,
                "facts_dir": facts_dir,
                "script_dir": script_dir,
                "audio_dir": audio_dir,
                "images_dir": images_dir,
                "video_dir": video_dir,
                "social_media_dir": social_media_dir
            }

    @staticmethod
    def save_json(data, file_path):
        """
        Save data as JSON file with handling for NumPy types

        Args:
            data: The data to save (dict or list)
            file_path: Path to save the JSON file
        """
        # Define a custom JSON encoder to handle NumPy types
        class NumpyEncoder(json.JSONEncoder):
            def default(self, obj):
                # Handle NumPy types
                try:
                    import numpy as np
                    if isinstance(obj, np.integer):
                        return int(obj)
                    elif isinstance(obj, np.floating):
                        return float(obj)
                    elif isinstance(obj, np.ndarray):
                        return obj.tolist()
                except ImportError:
                    pass

                # Let the base class handle other types or raise TypeError
                return super(NumpyEncoder, self).default(obj)

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # Save with the custom encoder
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=4, cls=NumpyEncoder)

    @staticmethod
    def load_json(file_path):
        """Load data from JSON file"""
        with open(file_path, 'r') as f:
            return json.load(f)

    @staticmethod
    def save_text(text, file_path):
        """Save text to file"""
        with open(file_path, 'w') as f:
            f.write(text)

    @staticmethod
    def load_text(file_path):
        """Load text from file"""
        with open(file_path, 'r') as f:
            return f.read()

    @staticmethod
    def clean_temp_dir():
        """Clean temporary directory"""
        if os.path.exists(TEMP_DIR):
            for file in os.listdir(TEMP_DIR):
                file_path = os.path.join(TEMP_DIR, file)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                except Exception as e:
                    print(f"Error cleaning temp directory: {e}")

    @staticmethod
    def get_temp_file_path(filename):
        """Get path for a temporary file"""
        return os.path.join(TEMP_DIR, filename)

    @staticmethod
    def copy_file(src, dst):
        """Copy a file from source to destination"""
        try:
            shutil.copy2(src, dst)
            return True
        except Exception as e:
            print(f"Error copying file from {src} to {dst}: {e}")
            return False

    @staticmethod
    def save_uploaded_video(uploaded_file, output_dirs):
        """
        Save an uploaded video file to the project directory

        Args:
            uploaded_file: The uploaded file object from Flask request
            output_dirs (dict): Dictionary containing output directory paths

        Returns:
            str: Path to the saved video file or None if failed
        """
        try:
            # Create a safe filename
            original_filename = uploaded_file.filename
            # Get file extension
            _, ext = os.path.splitext(original_filename)
            # Ensure it's a video file
            if ext.lower() not in ['.mp4', '.mov', '.avi', '.mkv', '.webm']:
                print(f"Error: Unsupported video format: {ext}")
                return None

            # Create a safe filename
            safe_filename = f"source_video{ext}"

            # Save to video directory
            video_path = os.path.join(output_dirs["video_dir"], safe_filename)
            uploaded_file.save(video_path)

            print(f"Video saved to: {video_path}")
            return video_path
        except Exception as e:
            print(f"Error saving uploaded video: {e}")
            return None

    @staticmethod
    def validate_video_file(video_path):
        """
        Validate a video file to ensure it's a valid video

        Args:
            video_path (str): Path to the video file

        Returns:
            bool: True if valid, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(video_path):
                print(f"Error: Video file not found: {video_path}")
                return False

            # Check file size (must be > 0)
            if os.path.getsize(video_path) == 0:
                print(f"Error: Video file is empty: {video_path}")
                return False

            # Try to open with MoviePy to validate
            from moviepy.editor import VideoFileClip
            video = VideoFileClip(video_path)

            # Check duration
            if video.duration < 1:
                print(f"Error: Video is too short: {video.duration} seconds")
                video.close()
                return False

            # Close the video to release resources
            video.close()

            return True
        except Exception as e:
            print(f"Error validating video file: {e}")
            return False

    @staticmethod
    def find_highlight_project(highlight_id):
        """
        Find an existing highlight project directory by ID

        Args:
            highlight_id (str): The ID of the highlight project

        Returns:
            str: Path to the project directory or None if not found
        """
        # Check if the ID contains a timestamp
        if '_' in highlight_id:
            # Try to find the exact directory
            project_dir = os.path.join(OUTPUT_DIR, highlight_id)
            if os.path.exists(project_dir) and os.path.isdir(project_dir):
                return project_dir

        # If not found, try to find by partial match (sanitized name)
        for dir_name in os.listdir(OUTPUT_DIR):
            dir_path = os.path.join(OUTPUT_DIR, dir_name)
            if os.path.isdir(dir_path) and dir_name.startswith(highlight_id):
                return dir_path

        return None

    @staticmethod
    def find_pipeline_job_by_process_id(process_id):
        """
        Find a pipeline job ID by searching for the process ID in pipeline parameters

        Args:
            process_id (str): The process ID to search for

        Returns:
            str: Pipeline job ID or None if not found
        """
        from config.settings import PIPELINE_OUTPUT_DIR

        if not os.path.exists(PIPELINE_OUTPUT_DIR):
            return None

        # Search through all pipeline job directories
        for job_dir in os.listdir(PIPELINE_OUTPUT_DIR):
            job_path = os.path.join(PIPELINE_OUTPUT_DIR, job_dir)
            if not os.path.isdir(job_path):
                continue

            # Check params.json for process information
            params_file = os.path.join(job_path, "params.json")
            if os.path.exists(params_file):
                try:
                    with open(params_file, 'r') as f:
                        params = json.load(f)

                    # Check if this job matches our process
                    # We can match by video path containing the process name or other identifiers
                    video_path = params.get('video_path', '')

                    # Extract process name from process_id (remove timestamp)
                    process_name = process_id
                    if '_' in process_id:
                        parts = process_id.split('_')
                        if len(parts) >= 2 and len(parts[-1]) == 6 and parts[-1].isdigit():
                            process_name = '_'.join(parts[:-1])

                    # Check if the video path or job relates to this process
                    # For now, we'll return the most recent job that could match
                    # This is a heuristic approach since we don't have direct mapping
                    if process_name.lower() in video_path.lower() or 'inter_vs_barcelona' in video_path.lower():
                        return job_dir

                except Exception as e:
                    print(f"Error reading params file {params_file}: {e}")
                    continue

        # If no specific match found, return the most recent job as a fallback
        # This is not ideal but helps with the current issue
        job_dirs = []
        for job_dir in os.listdir(PIPELINE_OUTPUT_DIR):
            job_path = os.path.join(PIPELINE_OUTPUT_DIR, job_dir)
            if os.path.isdir(job_path):
                # Get creation time
                stat = os.stat(job_path)
                job_dirs.append((job_dir, stat.st_mtime))

        if job_dirs:
            # Sort by modification time (newest first)
            job_dirs.sort(key=lambda x: x[1], reverse=True)
            return job_dirs[0][0]

        return None

    @staticmethod
    def load_highlight_data(project_dir):
        """
        Load existing highlight data from a project directory

        Args:
            project_dir (str): Path to the project directory

        Returns:
            dict: Dictionary containing loaded data and completion status for each step
        """
        result = {
            "project_dir": project_dir,
            "completion": {
                "video_analysis": False,
                "transcription": False,
                "keyword_analysis": False,
                "highlight_extraction": False,
                "caption_generation": False,
                "video_enhancement": False
            },
            "data": {}
        }

        # Check for pipeline job ID
        pipeline_job_file = os.path.join(project_dir, "pipeline_job_id.txt")
        if os.path.exists(pipeline_job_file) and os.path.getsize(pipeline_job_file) > 0:
            try:
                with open(pipeline_job_file, 'r') as f:
                    pipeline_job_id = f.read().strip()
                    if pipeline_job_id:
                        result["data"]["pipeline_job_id"] = pipeline_job_id

                        # Try to load keywords from pipeline job parameters
                        try:
                            from config.settings import PIPELINE_OUTPUT_DIR
                            params_file = os.path.join(PIPELINE_OUTPUT_DIR, pipeline_job_id, "params.json")
                            if os.path.exists(params_file):
                                with open(params_file, 'r') as pf:
                                    job_params = json.load(pf)
                                    keywords = job_params.get('keywords', [])
                                    if keywords:
                                        result["keywords"] = keywords
                        except Exception as e:
                            print(f"Error loading keywords from pipeline job: {e}")

                        # If we have a pipeline job ID, we can assume all steps are completed
                        for step in result["completion"]:
                            result["completion"][step] = True
            except Exception as e:
                print(f"Error loading pipeline job ID: {e}")

        # Check for video metadata
        metadata_dir = os.path.join(project_dir, "metadata")
        if os.path.exists(metadata_dir):
            # Check video metadata
            video_metadata_file = os.path.join(metadata_dir, "video_metadata.json")
            if os.path.exists(video_metadata_file) and os.path.getsize(video_metadata_file) > 0:
                try:
                    result["data"]["video_metadata"] = FileHandler.load_json(video_metadata_file)
                    result["completion"]["video_analysis"] = True
                except Exception as e:
                    print(f"Error loading video metadata: {e}")

            # Check audio metadata
            audio_metadata_file = os.path.join(metadata_dir, "audio_metadata.json")
            if os.path.exists(audio_metadata_file) and os.path.getsize(audio_metadata_file) > 0:
                try:
                    result["data"]["audio_data"] = FileHandler.load_json(audio_metadata_file)
                except Exception as e:
                    print(f"Error loading audio metadata: {e}")

            # Check keyword matches
            keyword_matches_file = os.path.join(metadata_dir, "keyword_matches.json")
            if os.path.exists(keyword_matches_file) and os.path.getsize(keyword_matches_file) > 0:
                try:
                    matches = FileHandler.load_json(keyword_matches_file)
                    result["data"]["keyword_matches"] = {"matches": matches}
                    result["completion"]["keyword_analysis"] = True
                except Exception as e:
                    print(f"Error loading keyword matches: {e}")

        # Check for transcript
        transcript_dir = os.path.join(project_dir, "transcript")
        if os.path.exists(transcript_dir):
            transcript_file = os.path.join(transcript_dir, "transcript.json")
            if os.path.exists(transcript_file) and os.path.getsize(transcript_file) > 0:
                try:
                    result["data"]["transcript"] = FileHandler.load_json(transcript_file)
                    result["completion"]["transcription"] = True
                except Exception as e:
                    print(f"Error loading transcript: {e}")

        # Check for highlight video
        video_dir = os.path.join(project_dir, "video")
        if os.path.exists(video_dir):
            # Look for highlight video metadata
            for file_name in os.listdir(video_dir):
                if file_name.startswith("highlight_metadata") and file_name.endswith(".json"):
                    highlight_metadata_file = os.path.join(video_dir, file_name)
                    try:
                        result["data"]["highlight_metadata"] = FileHandler.load_json(highlight_metadata_file)
                        result["completion"]["highlight_extraction"] = True
                    except Exception as e:
                        print(f"Error loading highlight metadata: {e}")

                # Look for highlight video file
                elif file_name.startswith("highlight_video") and file_name.endswith(".mp4"):
                    highlight_video_path = os.path.join(video_dir, file_name)
                    if os.path.exists(highlight_video_path) and os.path.getsize(highlight_video_path) > 0:
                        if "highlight_metadata" in result["data"]:
                            result["data"]["highlight_video"] = {
                                "file_path": highlight_video_path,
                                "format": "original"
                            }

                # Look for enhanced video file
                elif file_name.startswith("enhanced_video") and file_name.endswith(".mp4"):
                    enhanced_video_path = os.path.join(video_dir, file_name)
                    if os.path.exists(enhanced_video_path) and os.path.getsize(enhanced_video_path) > 0:
                        result["data"]["enhanced_video"] = {
                            "file_path": enhanced_video_path,
                            "format": "original"  # Will be updated if format info is found
                        }
                        result["completion"]["video_enhancement"] = True

        # Check for captions
        if "highlight_metadata" in result["data"]:
            captions_file = os.path.join(video_dir, "segments_with_captions.json")
            if os.path.exists(captions_file) and os.path.getsize(captions_file) > 0:
                try:
                    result["data"]["segments_with_captions"] = FileHandler.load_json(captions_file)
                    result["completion"]["caption_generation"] = True
                except Exception as e:
                    print(f"Error loading captions: {e}")

        # Find source video
        source_video_path = os.path.join(video_dir, "source_video.mp4")
        if os.path.exists(source_video_path) and os.path.getsize(source_video_path) > 0:
            result["source_video_path"] = source_video_path

        return result
