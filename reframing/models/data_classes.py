#!/usr/bin/env python3
"""
Data classes for the Smart Video Reframing Package

This module contains all the data structures used throughout the reframing pipeline.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class FaceDetection:
    """Data class for face detection results with ultra-high precision center calculation"""
    x: int
    y: int
    width: int
    height: int
    confidence: float
    center_x: float  # Ultra-high precision center calculation
    center_y: float  # Ultra-high precision center calculation
    # Advanced precision fields
    precise_center_x: float = 0.0  # Double precision center for ultra-accurate tracking
    precise_center_y: float = 0.0  # Double precision center for ultra-accurate tracking
    eye_center_x: float = 0.0  # Eye-based center for facial landmark accuracy
    eye_center_y: float = 0.0  # Eye-based center for facial landmark accuracy

    def __post_init__(self):
        # Ultra-high precision center calculation for world-class face tracking
        self.center_x = self.x + self.width / 2.0  # Standard precision
        self.center_y = self.y + self.height / 2.0  # Standard precision

        # Double precision calculation for ultra-accurate tracking
        self.precise_center_x = float(self.x) + float(self.width) / 2.0
        self.precise_center_y = float(self.y) + float(self.height) / 2.0

        # Initialize eye-based centers (will be updated by advanced detection)
        if self.eye_center_x == 0.0:
            self.eye_center_x = self.precise_center_x
        if self.eye_center_y == 0.0:
            self.eye_center_y = self.precise_center_y

    def get_ultra_precise_center_x(self) -> float:
        """Get the most accurate center_x available"""
        # Prioritize eye-based center if available, otherwise use precise center
        return self.eye_center_x if self.eye_center_x != self.precise_center_x else self.precise_center_x

    def get_ultra_precise_center_y(self) -> float:
        """Get the most accurate center_y available"""
        # Prioritize eye-based center if available, otherwise use precise center
        return self.eye_center_y if self.eye_center_y != self.precise_center_y else self.precise_center_y


@dataclass
class SpeakerSegment:
    """Data class for speaker segments from transcription"""
    start_time: float
    end_time: float
    text: str
    speaker_id: Optional[int] = None


@dataclass
class CropWindow:
    """Data class for crop window with smoothing"""
    x: int
    y: int
    width: int
    height: int
    confidence: float
    timestamp: float


@dataclass
class GroupFaceBounds:
    """Data class for group face bounding box with sub-pixel precision center calculation"""
    x: int
    y: int
    width: int
    height: int
    center_x: float  # Changed to float for sub-pixel precision
    center_y: float  # Changed to float for sub-pixel precision
    face_count: int
    confidence: float

    def __post_init__(self):
        # Sub-pixel precision center calculation for improved group face tracking
        self.center_x = self.x + self.width / 2.0  # Use float division for precision
        self.center_y = self.y + self.height / 2.0  # Use float division for precision
