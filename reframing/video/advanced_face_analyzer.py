#!/usr/bin/env python3
"""
Advanced Face Analysis for World-Class Video Centering

This module provides sophisticated face analysis capabilities including:
- Face orientation detection
- Gaze direction estimation
- Face quality assessment
- Composition optimization
"""

import cv2
import numpy as np
import logging
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass

from ..models.data_classes import FaceDetection


@dataclass
class AdvancedFaceMetrics:
    """Advanced metrics for face analysis"""
    face: FaceDetection
    orientation_angle: float  # Head rotation angle
    gaze_direction: Tuple[float, float]  # (horizontal, vertical) gaze
    face_quality_score: float  # Overall face quality (0-1)
    eye_level_y: int  # Estimated eye level Y coordinate
    composition_weight: float  # Importance weight for composition (0-1)
    shot_type: str  # "extreme_closeup", "closeup", "medium", "wide"


class AdvancedFaceAnalyzer:
    """World-class face analysis for optimal video composition"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Composition constants based on cinematography principles
        self.GOLDEN_RATIO = 0.618
        self.RULE_OF_THIRDS = 0.333
        
        # Shot type thresholds (based on face area ratio)
        self.SHOT_THRESHOLDS = {
            'extreme_closeup': 0.20,  # >20% of frame
            'closeup': 0.12,          # 12-20% of frame
            'medium': 0.06,           # 6-12% of frame
            'medium_wide': 0.03,      # 3-6% of frame
            'wide': 0.0               # <3% of frame
        }
    
    def analyze_face(self, face: FaceDetection, frame_width: int, frame_height: int) -> AdvancedFaceMetrics:
        """Perform comprehensive face analysis for optimal composition"""
        
        # Calculate face area ratio
        face_area = face.width * face.height
        frame_area = frame_width * frame_height
        face_area_ratio = face_area / frame_area if frame_area > 0 else 0
        
        # Determine shot type
        shot_type = self._classify_shot_type(face_area_ratio)
        
        # Estimate eye level (eyes are typically 30% down from face top)
        eye_level_y = face.y + int(face.height * 0.3)
        
        # Calculate face orientation (simplified - could be enhanced with ML)
        orientation_angle = self._estimate_face_orientation(face)
        
        # Estimate gaze direction (simplified)
        gaze_direction = self._estimate_gaze_direction(face)
        
        # Calculate face quality score
        quality_score = self._calculate_face_quality(face, face_area_ratio)
        
        # Calculate composition weight
        composition_weight = self._calculate_composition_weight(face, shot_type, quality_score)
        
        return AdvancedFaceMetrics(
            face=face,
            orientation_angle=orientation_angle,
            gaze_direction=gaze_direction,
            face_quality_score=quality_score,
            eye_level_y=eye_level_y,
            composition_weight=composition_weight,
            shot_type=shot_type
        )
    
    def _classify_shot_type(self, face_area_ratio: float) -> str:
        """Classify shot type based on face size"""
        if face_area_ratio > self.SHOT_THRESHOLDS['extreme_closeup']:
            return 'extreme_closeup'
        elif face_area_ratio > self.SHOT_THRESHOLDS['closeup']:
            return 'closeup'
        elif face_area_ratio > self.SHOT_THRESHOLDS['medium']:
            return 'medium'
        elif face_area_ratio > self.SHOT_THRESHOLDS['medium_wide']:
            return 'medium_wide'
        else:
            return 'wide'
    
    def _estimate_face_orientation(self, face: FaceDetection) -> float:
        """Estimate face orientation angle (simplified implementation)"""
        # This is a simplified implementation
        # In a production system, you'd use facial landmark detection
        aspect_ratio = face.width / face.height if face.height > 0 else 1.0
        
        # Estimate based on face aspect ratio
        if aspect_ratio > 1.2:
            return 15.0  # Slightly turned
        elif aspect_ratio < 0.8:
            return -15.0  # Slightly turned other way
        else:
            return 0.0  # Frontal
    
    def _estimate_gaze_direction(self, face: FaceDetection) -> Tuple[float, float]:
        """Estimate gaze direction (simplified implementation)"""
        # This is a simplified implementation
        # In a production system, you'd use eye tracking algorithms
        return (0.0, 0.0)  # Assume looking forward
    
    def _calculate_face_quality(self, face: FaceDetection, face_area_ratio: float) -> float:
        """Calculate overall face quality score"""
        quality = face.confidence  # Start with detection confidence
        
        # Boost quality for optimal face sizes
        if 0.08 <= face_area_ratio <= 0.25:  # Optimal size range
            quality *= 1.2
        elif face_area_ratio < 0.03:  # Too small
            quality *= 0.7
        elif face_area_ratio > 0.4:  # Too large
            quality *= 0.8
        
        # Ensure quality stays in valid range
        return min(1.0, max(0.0, quality))
    
    def _calculate_composition_weight(self, face: FaceDetection, shot_type: str, quality_score: float) -> float:
        """Calculate importance weight for composition decisions"""
        base_weight = quality_score
        
        # Adjust weight based on shot type
        shot_weights = {
            'extreme_closeup': 1.0,
            'closeup': 0.9,
            'medium': 0.8,
            'medium_wide': 0.7,
            'wide': 0.6
        }
        
        return base_weight * shot_weights.get(shot_type, 0.5)
    
    def get_optimal_eye_position(self, metrics: AdvancedFaceMetrics, target_height: int) -> int:
        """Calculate optimal eye position based on advanced analysis"""
        
        # Base positioning rules by shot type
        position_rules = {
            'extreme_closeup': target_height * self.GOLDEN_RATIO * 0.5,  # ~31% from top
            'closeup': target_height * self.RULE_OF_THIRDS,              # 33% from top
            'medium': target_height * 0.28,                             # 28% from top
            'medium_wide': target_height * 0.25,                        # 25% from top
            'wide': target_height * 0.22                                # 22% from top
        }
        
        base_position = position_rules.get(metrics.shot_type, target_height * 0.3)
        
        # Apply micro-adjustments based on face orientation
        orientation_adjustment = 0
        if abs(metrics.orientation_angle) > 10:  # Turned face
            orientation_adjustment = target_height * 0.02  # Slightly higher
        
        # Apply gaze direction adjustments (future enhancement)
        gaze_adjustment = 0
        
        final_position = int(base_position + orientation_adjustment + gaze_adjustment)
        
        self.logger.debug(f"Optimal eye position: shot_type={metrics.shot_type}, "
                         f"base={base_position:.1f}, final={final_position}")
        
        return final_position
    
    def analyze_group_dynamics(self, faces: List[FaceDetection], frame_width: int, frame_height: int) -> Dict:
        """Analyze group dynamics for optimal multi-face composition"""
        if not faces:
            return {}
        
        # Analyze each face
        face_metrics = [self.analyze_face(face, frame_width, frame_height) for face in faces]
        
        # Find dominant face (highest composition weight)
        dominant_face = max(face_metrics, key=lambda m: m.composition_weight)
        
        # Calculate group characteristics
        total_weight = sum(m.composition_weight for m in face_metrics)
        avg_eye_level = sum(m.eye_level_y * m.composition_weight for m in face_metrics) / total_weight if total_weight > 0 else 0
        
        # Determine group shot type (based on combined area)
        total_face_area = sum(m.face.width * m.face.height for m in face_metrics)
        group_area_ratio = total_face_area / (frame_width * frame_height)
        group_shot_type = self._classify_shot_type(group_area_ratio)
        
        return {
            'face_metrics': face_metrics,
            'dominant_face': dominant_face,
            'weighted_eye_level': avg_eye_level,
            'group_shot_type': group_shot_type,
            'total_composition_weight': total_weight,
            'face_count': len(faces)
        }
