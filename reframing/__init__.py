#!/usr/bin/env python3
"""
Smart Video Reframing Package

A comprehensive package for intelligent video reframing with face detection,
speaker tracking, and optimized crop window calculation for vertical video formats.

This package provides:
- Multi-backend face detection (OpenCV, MediaPipe, RetinaFace, MTCNN)
- Intelligent speaker tracking using transcription data
- Temporal smoothing for stable crop windows
- Support for vertical (9:16) and square (1:1) video formats
- GPU acceleration support
"""

from .models.data_classes import (
    FaceDetection,
    SpeakerSegment,
    CropWindow,
    GroupFaceBounds
)

__version__ = "1.0.0"
__author__ = "Smart Video Highlight Generator"

__all__ = [
    "FaceDetection",
    "SpeakerSegment",
    "CropWindow",
    "GroupFaceBounds"
]
