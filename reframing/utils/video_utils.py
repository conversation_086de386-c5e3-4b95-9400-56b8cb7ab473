#!/usr/bin/env python3
"""
Video utility functions for the reframing package
"""

import cv2
import ffmpeg
from typing import Tuple, Optional, Dict, Any


class VideoUtils:
    """Utility class for video operations"""
    
    @staticmethod
    def get_video_info(video_path: str) -> Optional[Dict[str, Any]]:
        """
        Get video information using ffprobe
        
        Args:
            video_path: Path to the video file
            
        Returns:
            Dictionary containing video information or None if failed
        """
        try:
            probe = ffmpeg.probe(video_path)
            video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
            
            if not video_stream:
                return None
                
            return {
                'width': int(video_stream['width']),
                'height': int(video_stream['height']),
                'fps': eval(video_stream.get('r_frame_rate', '30/1')),
                'duration': float(video_stream.get('duration', 0)),
                'codec': video_stream.get('codec_name', 'unknown'),
                'pixel_format': video_stream.get('pix_fmt', 'unknown')
            }
        except Exception:
            return None
    
    @staticmethod
    def get_video_dimensions(video_path: str) -> Optional[Tuple[int, int]]:
        """
        Get video dimensions (width, height)
        
        Args:
            video_path: Path to the video file
            
        Returns:
            Tuple of (width, height) or None if failed
        """
        info = VideoUtils.get_video_info(video_path)
        if info:
            return info['width'], info['height']
        return None
    
    @staticmethod
    def calculate_aspect_ratio(width: int, height: int) -> float:
        """
        Calculate aspect ratio
        
        Args:
            width: Video width
            height: Video height
            
        Returns:
            Aspect ratio as width/height
        """
        return width / height if height > 0 else 0
    
    @staticmethod
    def is_vertical_video(width: int, height: int) -> bool:
        """
        Check if video is in vertical format
        
        Args:
            width: Video width
            height: Video height
            
        Returns:
            True if video is vertical (height > width)
        """
        return height > width
    
    @staticmethod
    def calculate_crop_for_aspect_ratio(original_width: int, original_height: int, 
                                      target_aspect: float) -> Tuple[int, int, int, int]:
        """
        Calculate crop parameters to achieve target aspect ratio
        
        Args:
            original_width: Original video width
            original_height: Original video height
            target_aspect: Target aspect ratio (width/height)
            
        Returns:
            Tuple of (x_offset, y_offset, crop_width, crop_height)
        """
        original_aspect = original_width / original_height
        
        if original_aspect > target_aspect:
            # Original is wider, crop width
            crop_height = original_height
            crop_width = int(original_height * target_aspect)
            x_offset = (original_width - crop_width) // 2
            y_offset = 0
        else:
            # Original is taller, crop height
            crop_width = original_width
            crop_height = int(original_width / target_aspect)
            x_offset = 0
            y_offset = (original_height - crop_height) // 2
            
        return x_offset, y_offset, crop_width, crop_height
    
    @staticmethod
    def extract_frame_at_position(video_path: str, frame_position: int) -> Optional[Any]:
        """
        Extract a frame at a specific position
        
        Args:
            video_path: Path to the video file
            frame_position: Frame number to extract
            
        Returns:
            Frame as numpy array or None if failed
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
                
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_position)
            ret, frame = cap.read()
            cap.release()
            
            return frame if ret else None
        except Exception:
            return None
    
    @staticmethod
    def extract_frame_at_timestamp(video_path: str, timestamp: float) -> Optional[Any]:
        """
        Extract a frame at a specific timestamp
        
        Args:
            video_path: Path to the video file
            timestamp: Timestamp in seconds
            
        Returns:
            Frame as numpy array or None if failed
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
                
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_position = int(timestamp * fps)
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_position)
            ret, frame = cap.read()
            cap.release()
            
            return frame if ret else None
        except Exception:
            return None
