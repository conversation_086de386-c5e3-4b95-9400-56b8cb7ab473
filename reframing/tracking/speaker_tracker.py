#!/usr/bin/env python3
"""
Speaker tracking module for intelligent face selection

This module tracks speakers using face detection and transcription timing
to identify the primary speaker for optimal video cropping.
"""

import logging
from typing import Dict, Any, List, Optional

from ..models.data_classes import FaceDetection, SpeakerSegment


class SpeakerTracker:
    """
    Tracks speakers using face detection and transcription timing
    """

    def __init__(self, transcription_segments: List[Dict[str, Any]]):
        self.transcription_segments = transcription_segments
        self.speaker_segments = self._parse_transcription_segments()
        # Add logger for debugging
        self.logger = logging.getLogger(__name__)

    def _parse_transcription_segments(self) -> List[SpeakerSegment]:
        """Parse transcription segments into speaker segments"""
        segments = []
        for segment in self.transcription_segments:
            segments.append(SpeakerSegment(
                start_time=segment.get('start', 0),
                end_time=segment.get('end', 0),
                text=segment.get('text', '').strip()
            ))
        return segments

    def get_active_speaker_at_time(self, timestamp: float) -> Optional[SpeakerSegment]:
        """Get the active speaker segment at a given timestamp"""
        for segment in self.speaker_segments:
            if segment.start_time <= timestamp <= segment.end_time:
                return segment
        return None

    def identify_primary_speaker(self, faces: List[FaceDetection], timestamp: float,
                               frame_width: int, frame_height: int) -> Optional[FaceDetection]:
        """
        Identify the primary speaker based on face size, position, speech timing, and consistency

        Args:
            faces: List of detected faces
            timestamp: Current timestamp in seconds
            frame_width: Frame width
            frame_height: Frame height

        Returns:
            Primary speaker face or None
        """
        if not faces:
            return None

        # Get active speaker segment
        active_segment = self.get_active_speaker_at_time(timestamp)

        # Enhanced multi-face handling
        if len(faces) >= 2:
            self.logger.debug(f"Multiple faces detected ({len(faces)}), using enhanced selection logic")
            return self._select_primary_from_multiple_faces(faces, active_segment, frame_width, frame_height, timestamp)

        # Single face - use standard scoring
        scored_faces = []
        for face in faces:
            score = self._calculate_face_score(face, active_segment, frame_width, frame_height)
            scored_faces.append((face, score))

        # Return face with highest score
        if scored_faces:
            scored_faces.sort(key=lambda x: x[1], reverse=True)
            return scored_faces[0][0]

        return None

    def _select_primary_from_multiple_faces(self, faces: List[FaceDetection], active_segment: Optional[SpeakerSegment],
                                          frame_width: int, frame_height: int, timestamp: float) -> Optional[FaceDetection]:
        """
        Enhanced logic for selecting primary face when multiple faces are detected

        Args:
            faces: List of detected faces
            active_segment: Current active speaker segment
            frame_width: Frame width
            frame_height: Frame height
            timestamp: Current timestamp

        Returns:
            Primary face or None
        """
        if not faces:
            return None

        # Score all faces with enhanced multi-face criteria
        scored_faces = []
        for face in faces:
            score = self._calculate_enhanced_face_score(face, active_segment, frame_width, frame_height, faces)
            scored_faces.append((face, score))

        # Sort by score and log the decision process
        scored_faces.sort(key=lambda x: x[1], reverse=True)

        # Log face selection details for debugging
        for i, (face, score) in enumerate(scored_faces):
            self.logger.debug(f"  Face {i}: center=({face.center_x}, {face.center_y}), "
                            f"size={face.width}x{face.height}, confidence={face.confidence:.2f}, score={score:.1f}")

        # Return the highest scoring face
        best_face = scored_faces[0][0]
        self.logger.debug(f"Selected primary face: center=({best_face.center_x}, {best_face.center_y}), "
                         f"score={scored_faces[0][1]:.1f}")

        return best_face

    def _calculate_enhanced_face_score(self, face: FaceDetection, active_segment: Optional[SpeakerSegment],
                                     frame_width: int, frame_height: int, all_faces: List[FaceDetection]) -> float:
        """
        Calculate enhanced face score for multi-face scenarios

        Args:
            face: Face to score
            active_segment: Current active speaker segment
            frame_width: Frame width
            frame_height: Frame height
            all_faces: All detected faces for relative comparison

        Returns:
            Face score
        """
        score = 0.0

        # Base score using standard criteria
        base_score = self._calculate_face_score(face, active_segment, frame_width, frame_height)
        score += base_score

        # Multi-face specific bonuses
        if len(all_faces) >= 2:
            # Relative size bonus - prefer larger faces but not exclusively
            face_areas = [f.width * f.height for f in all_faces]
            max_area = max(face_areas)
            face_area = face.width * face.height
            relative_size_score = (face_area / max_area) * 30  # Max 30 points
            score += relative_size_score

            # Horizontal position preference for vertical video
            # Prefer faces closer to center horizontally
            center_distance = abs(face.center_x - frame_width / 2)
            max_distance = frame_width / 2
            horizontal_center_score = (1 - center_distance / max_distance) * 20  # Max 20 points
            score += horizontal_center_score

            # Stability bonus - prefer faces that are well-positioned for vertical crop
            # Faces in the upper-middle area get bonus points
            vertical_position_ratio = face.center_y / frame_height
            if 0.2 <= vertical_position_ratio <= 0.6:  # Good position for vertical video
                score += 25  # Bonus for good vertical positioning

            # Confidence reliability bonus
            if face.confidence > 0.5:
                score += 15  # Bonus for high confidence faces

        return score

    def _calculate_face_score(self, face: FaceDetection, active_segment: Optional[SpeakerSegment],
                            frame_width: int, frame_height: int) -> float:
        """Calculate a score for a face based on multiple criteria"""
        score = 0.0

        # Size score (larger faces get higher scores)
        face_area = face.width * face.height
        frame_area = frame_width * frame_height
        size_ratio = face_area / frame_area
        score += size_ratio * 100  # Scale up for better scoring

        # Position score (centered faces get higher scores)
        center_x_ratio = abs(face.center_x - frame_width / 2) / (frame_width / 2)
        center_y_ratio = abs(face.center_y - frame_height / 2) / (frame_height / 2)
        position_score = (2 - center_x_ratio - center_y_ratio) * 25  # Max 50 points
        score += position_score

        # Confidence score
        score += face.confidence * 25  # Max 25 points

        # Speech timing bonus (if there's active speech)
        if active_segment and active_segment.text.strip():
            score += 50  # Bonus for speaking during active segment

        return score
