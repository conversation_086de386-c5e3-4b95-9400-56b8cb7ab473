#!/usr/bin/env python3
"""
Backend availability checks for face detection libraries
"""

import logging
logger = logging.getLogger(__name__)

# Check InsightFace SCRFD availability (best accuracy)
INSIGHTFACE_AVAILABLE = False
try:
    import insightface
    INSIGHTFACE_AVAILABLE = True
    logger.info("InsightFace SCRFD backend available")
except ImportError:
    logger.warning("InsightFace not available - install with: pip install insightface")

# Check YOLOv8 Face availability
YOLOV8_FACE_AVAILABLE = False
try:
    import ultralytics
    YOLOV8_FACE_AVAILABLE = True
    logger.info("YOLOv8 Face backend available")
except ImportError:
    logger.warning("YOLOv8 not available - install with: pip install ultralytics")

# Check MediaPipe availability
MEDIAPIPE_AVAILABLE = False
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
    logger.info("MediaPipe backend available")
except ImportError:
    logger.warning("MediaPipe not available - install with: pip install mediapipe")

# Check RetinaFace availability
RETINAFACE_AVAILABLE = False
try:
    from retinaface import RetinaFace
    RETINAFACE_AVAILABLE = True
    logger.info("RetinaFace backend available")
except ImportError:
    logger.warning("RetinaFace not available - install with: pip install retina-face")

# Check MTCNN availability
MTCNN_AVAILABLE = False
try:
    from mtcnn import MTCNN
    MTCNN_AVAILABLE = True
    logger.info("MTCNN backend available")
except ImportError:
    logger.warning("MTCNN not available - install with: pip install mtcnn")

# Check OpenCV availability
OPENCV_FACE_DETECTION_AVAILABLE = False
try:
    import cv2
    OPENCV_FACE_DETECTION_AVAILABLE = True
    logger.info("OpenCV backend available")
except ImportError:
    logger.warning("OpenCV not available - install with: pip install opencv-python")
