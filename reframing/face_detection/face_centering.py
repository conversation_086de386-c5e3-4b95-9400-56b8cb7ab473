#!/usr/bin/env python3
"""
Face Centering Algorithm

This module implements a professional-grade face centering system that provides
sub-pixel precision positioning for optimal video composition.

Features:
- Golden ratio and rule of thirds positioning
- Eye-level detection and positioning
- Face size-aware composition
- Multi-face optimization
- Social media format optimization
- Temporal smoothing with centering preservation
"""

import logging
from typing import Tuple
from dataclasses import dataclass

from ..models.data_classes import FaceDetection, GroupFaceBounds


@dataclass
class CenteringMetrics:
    """Metrics for face centering quality assessment"""
    horizontal_deviation: float  # Deviation from perfect center (0.0 = perfect)
    vertical_positioning_score: float  # Quality of vertical positioning (1.0 = perfect)
    composition_score: float  # Overall composition quality (1.0 = perfect)
    face_visibility_score: float  # How well the face is visible (1.0 = perfect)


class FaceCenteringAlgorithm:
    """
    Professional face centering algorithm for optimal video composition

    This class implements advanced cinematography principles for optimal face positioning
    in vertical video formats, with special optimization for social media platforms.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Golden ratio constant for professional composition
        self.GOLDEN_RATIO = 1.618

        # Composition constants
        self.RULE_OF_THIRDS = 1/3
        self.EXTREME_CLOSEUP_THRESHOLD = 0.20  # 20% of frame area
        self.CLOSEUP_THRESHOLD = 0.12  # 12% of frame area
        self.MEDIUM_THRESHOLD = 0.06  # 6% of frame area
        self.MEDIUM_WIDE_THRESHOLD = 0.03  # 3% of frame area

        # Minimum margins (as fraction of frame)
        self.MIN_HEADROOM = 0.08  # 8% minimum space above face
        self.MIN_BOTTOM_SPACE = 0.15  # 15% minimum space below face
        self.MIN_SIDE_MARGIN = 0.05  # 5% minimum space on sides

    def calculate_optimal_crop_position(self, face: FaceDetection, frame_width: int, frame_height: int,
                                      target_width: int, target_height: int) -> Tuple[int, int]:
        """
        Calculate optimal crop position for professional face centering

        Args:
            face: Primary face detection
            frame_width: Original frame width
            frame_height: Original frame height
            target_width: Target crop width
            target_height: Target crop height

        Returns:
            Tuple of (crop_x, crop_y) for optimal positioning
        """
        # Calculate face metrics
        face_area = face.width * face.height
        frame_area = frame_width * frame_height
        face_area_ratio = face_area / frame_area

        # Determine shot type for optimal positioning
        shot_type = self._classify_shot_type(face_area_ratio)

        # Calculate eye level (eyes are typically 30% down from face top)
        eye_level_y = face.y + int(face.height * 0.3)

        # === ULTRA-PRECISE HORIZONTAL CENTERING ===
        # World-class horizontal centering with maximum precision
        face_center_x = face.get_ultra_precise_center_x()  # Use the most accurate center available
        crop_x = int(round(face_center_x - target_width / 2.0))  # Round to nearest pixel with ultra-precision

        # Micro-adjustment for face asymmetry (faces are naturally asymmetric)
        asymmetry_adjustment = self._calculate_face_asymmetry_adjustment(face)
        crop_x += asymmetry_adjustment

        # === PROFESSIONAL VERTICAL POSITIONING ===
        optimal_eye_position = self._get_optimal_eye_position(shot_type, target_height)
        crop_y = eye_level_y - optimal_eye_position

        # Apply composition refinements
        crop_x, crop_y = self._apply_composition_refinements(
            crop_x, crop_y, face, frame_width, frame_height, target_width, target_height
        )

        # CRITICAL: Ensure complete face visibility - this fixes the half-face issue
        crop_x, crop_y = self._ensure_complete_face_visibility(
            crop_x, crop_y, face, frame_width, frame_height, target_width, target_height
        )

        # Final boundary check
        crop_x = max(0, min(frame_width - target_width, crop_x))
        crop_y = max(0, min(frame_height - target_height, crop_y))

        return crop_x, crop_y

    def calculate_multi_face_crop_position(self, group_bounds: GroupFaceBounds, frame_width: int, frame_height: int,
                                         target_width: int, target_height: int) -> Tuple[int, int]:
        """
        Calculate optimal crop position for multiple faces with professional composition

        Args:
            group_bounds: Group face bounds
            frame_width: Original frame width
            frame_height: Original frame height
            target_width: Target crop width
            target_height: Target crop height

        Returns:
            Tuple of (crop_x, crop_y) for optimal group positioning
        """
        is_vertical = target_width < target_height

        if is_vertical and group_bounds.face_count == 2:
            return self._calculate_two_face_vertical_crop(
                group_bounds, frame_width, frame_height, target_width, target_height
            )
        else:
            # Standard group positioning with enhanced precision centering
            crop_x = int(round(group_bounds.center_x - target_width / 2.0))
            crop_y = int(round(group_bounds.center_y - target_height / 2.0))

            # Apply group composition refinements
            crop_x, crop_y = self._apply_group_composition_refinements(
                crop_x, crop_y, group_bounds, frame_width, frame_height, target_width, target_height
            )

            # CRITICAL: Ensure complete group visibility
            crop_x, crop_y = self._ensure_complete_group_visibility(
                crop_x, crop_y, group_bounds, frame_width, frame_height, target_width, target_height
            )

            # Final boundary check
            crop_x = max(0, min(frame_width - target_width, crop_x))
            crop_y = max(0, min(frame_height - target_height, crop_y))

            return crop_x, crop_y

    def _classify_shot_type(self, face_area_ratio: float) -> str:
        """Classify shot type based on face area ratio"""
        if face_area_ratio >= self.EXTREME_CLOSEUP_THRESHOLD:
            return "extreme_closeup"
        elif face_area_ratio >= self.CLOSEUP_THRESHOLD:
            return "closeup"
        elif face_area_ratio >= self.MEDIUM_THRESHOLD:
            return "medium"
        elif face_area_ratio >= self.MEDIUM_WIDE_THRESHOLD:
            return "medium_wide"
        else:
            return "wide"

    def _get_optimal_eye_position(self, shot_type: str, target_height: int) -> int:
        """Get optimal eye position based on shot type and cinematography principles"""
        if shot_type == "extreme_closeup":
            # Golden ratio positioning for dramatic emphasis
            return int(target_height * 0.618 * 0.5)  # ~31% from top
        elif shot_type == "closeup":
            # Rule of thirds for standard portraits
            return int(target_height * self.RULE_OF_THIRDS)  # 33% from top
        elif shot_type == "medium":
            # Slightly lower for head & shoulders
            return int(target_height * 0.28)  # 28% from top
        elif shot_type == "medium_wide":
            # Lower positioning for context
            return int(target_height * 0.25)  # 25% from top
        else:  # wide
            # Maximum visibility positioning
            return int(target_height * 0.22)  # 22% from top

    def _calculate_face_asymmetry_adjustment(self, face: FaceDetection) -> int:
        """Calculate micro-adjustment for natural face asymmetry"""
        # Most faces have slight asymmetry - adjust by 1-2 pixels for better centering
        # This is a simplified heuristic; could be enhanced with ML-based face analysis
        face_width = face.width

        if face_width > 100:
            # For larger faces, apply small adjustment
            return 1 if (face.x + face.width // 2) % 2 == 0 else -1
        else:
            # For smaller faces, no adjustment needed
            return 0

    def _apply_composition_refinements(self, crop_x: int, crop_y: int, face: FaceDetection,
                                     frame_width: int, frame_height: int,
                                     target_width: int, target_height: int) -> Tuple[int, int]:
        """Apply advanced composition refinements"""

        # Ensure adequate headroom (minimum 8% of frame height above face)
        min_headroom_pixels = int(target_height * self.MIN_HEADROOM)
        face_top_in_crop = face.y - crop_y
        if face_top_in_crop < min_headroom_pixels:
            crop_y = max(0, face.y - min_headroom_pixels)

        # Ensure adequate bottom space (minimum 15% below face)
        min_bottom_pixels = int(target_height * self.MIN_BOTTOM_SPACE)
        face_bottom_in_crop = (face.y + face.height) - crop_y
        if (target_height - face_bottom_in_crop) < min_bottom_pixels:
            crop_y = min(frame_height - target_height,
                        (face.y + face.height) - (target_height - min_bottom_pixels))

        # Ensure side margins
        min_side_pixels = int(target_width * self.MIN_SIDE_MARGIN)
        face_left_in_crop = face.x - crop_x
        face_right_in_crop = (face.x + face.width) - crop_x

        if face_left_in_crop < min_side_pixels:
            crop_x = max(0, face.x - min_side_pixels)
        elif (target_width - face_right_in_crop) < min_side_pixels:
            crop_x = min(frame_width - target_width,
                        (face.x + face.width) - (target_width - min_side_pixels))

        return crop_x, crop_y

    def _calculate_two_face_vertical_crop(self, group_bounds: GroupFaceBounds, frame_width: int, frame_height: int,
                                        target_width: int, target_height: int) -> Tuple[int, int]:
        """Advanced two-face positioning for vertical video with optimal social media composition"""

        # Calculate group width relative to target
        group_width = group_bounds.width
        group_to_target_ratio = group_width / target_width

        # Safety margin for edge cases
        min_safety_margin = 20

        if group_to_target_ratio > 0.90:  # Group is very wide (>90% of target width)
            # Critical case: faces are very far apart
            crop_x = max(min_safety_margin,
                        min(frame_width - target_width - min_safety_margin,
                            int(round(group_bounds.center_x - target_width / 2.0))))
            self.logger.debug(f"🎭 Very wide face separation (ratio: {group_to_target_ratio:.2f}), "
                            f"using safety positioning: x={crop_x}")

        elif group_to_target_ratio > 0.70:  # Group is moderately wide (70-90% of target width)
            # Standard case: faces are well separated but manageable
            crop_x = int(round(group_bounds.center_x - target_width / 2.0))
            self.logger.debug(f"🎭 Moderate face separation (ratio: {group_to_target_ratio:.2f}), "
                            f"using balanced positioning: x={crop_x}")

        else:  # Group fits comfortably (<70% of target width)
            # Easy case: faces are close enough to center normally
            crop_x = int(round(group_bounds.center_x - target_width / 2.0))
            self.logger.debug(f"🎭 Comfortable face positioning (ratio: {group_to_target_ratio:.2f}), "
                            f"using optimized center positioning: x={crop_x}")

        # Vertical positioning for group with enhanced precision
        crop_y = int(round(group_bounds.center_y - target_height * 0.35))  # Position group in upper portion

        # Ensure bounds
        crop_x = max(0, min(frame_width - target_width, crop_x))
        crop_y = max(0, min(frame_height - target_height, crop_y))

        return crop_x, crop_y

    def _ensure_complete_face_visibility(self, crop_x: int, crop_y: int, face: FaceDetection,
                                       frame_width: int, frame_height: int,
                                       target_width: int, target_height: int) -> Tuple[int, int]:
        """
        CRITICAL METHOD: Ensure the complete face is visible within the crop window
        This fixes the half-face issue by adjusting crop position when face extends beyond boundaries
        """

        # Calculate face boundaries within the crop
        face_left = face.x - crop_x
        face_right = face.x + face.width - crop_x
        face_top = face.y - crop_y
        face_bottom = face.y + face.height - crop_y

        # Add safety margin around face (10% of face size)
        safety_margin_x = int(face.width * 0.1)
        safety_margin_y = int(face.height * 0.1)

        # Check and fix horizontal boundaries
        if face_left < safety_margin_x:
            # Face is too close to left edge - shift crop left
            adjustment = safety_margin_x - face_left
            new_crop_x = crop_x - adjustment
            # Ensure we don't go beyond frame boundaries
            if new_crop_x >= 0:
                crop_x = new_crop_x
                self.logger.debug(f"🔧 Adjusted crop left by {adjustment}px to show complete face")
            else:
                # Can't shift left enough, shift as much as possible
                crop_x = 0
                self.logger.debug(f"🔧 Shifted crop to leftmost position to maximize face visibility")

        elif face_right > (target_width - safety_margin_x):
            # Face is too close to right edge - shift crop right
            adjustment = face_right - (target_width - safety_margin_x)
            new_crop_x = crop_x + adjustment
            # Ensure we don't go beyond frame boundaries
            if new_crop_x <= (frame_width - target_width):
                crop_x = new_crop_x
                self.logger.debug(f"🔧 Adjusted crop right by {adjustment}px to show complete face")
            else:
                # Can't shift right enough, shift as much as possible
                crop_x = frame_width - target_width
                self.logger.debug(f"🔧 Shifted crop to rightmost position to maximize face visibility")

        # Check and fix vertical boundaries
        if face_top < safety_margin_y:
            # Face is too close to top edge - shift crop up
            adjustment = safety_margin_y - face_top
            new_crop_y = crop_y - adjustment
            # Ensure we don't go beyond frame boundaries
            if new_crop_y >= 0:
                crop_y = new_crop_y
                self.logger.debug(f"🔧 Adjusted crop up by {adjustment}px to show complete face")
            else:
                # Can't shift up enough, shift as much as possible
                crop_y = 0
                self.logger.debug(f"🔧 Shifted crop to topmost position to maximize face visibility")

        elif face_bottom > (target_height - safety_margin_y):
            # Face is too close to bottom edge - shift crop down
            adjustment = face_bottom - (target_height - safety_margin_y)
            new_crop_y = crop_y + adjustment
            # Ensure we don't go beyond frame boundaries
            if new_crop_y <= (frame_height - target_height):
                crop_y = new_crop_y
                self.logger.debug(f"🔧 Adjusted crop down by {adjustment}px to show complete face")
            else:
                # Can't shift down enough, shift as much as possible
                crop_y = frame_height - target_height
                self.logger.debug(f"🔧 Shifted crop to bottommost position to maximize face visibility")

        # Final validation: ensure face is still reasonably centered after adjustments
        face_center_x_in_crop = face.get_ultra_precise_center_x() - crop_x
        horizontal_deviation = abs(face_center_x_in_crop - target_width / 2.0) / target_width

        if horizontal_deviation > 0.4:  # If face is more than 40% off-center
            self.logger.warning(f"⚠️ Face significantly off-center after visibility adjustment: {horizontal_deviation:.2f}")

        return crop_x, crop_y

    def _ensure_complete_group_visibility(self, crop_x: int, crop_y: int, group_bounds: GroupFaceBounds,
                                        frame_width: int, frame_height: int,
                                        target_width: int, target_height: int) -> Tuple[int, int]:
        """
        CRITICAL METHOD: Ensure the complete group of faces is visible within the crop window
        This fixes the half-face issue for multi-face scenarios
        """

        # Calculate group boundaries within the crop
        group_left = group_bounds.x - crop_x
        group_right = group_bounds.x + group_bounds.width - crop_x
        group_top = group_bounds.y - crop_y
        group_bottom = group_bounds.y + group_bounds.height - crop_y

        # Add safety margin around group (5% of group size, minimum 10px)
        safety_margin_x = max(10, int(group_bounds.width * 0.05))
        safety_margin_y = max(10, int(group_bounds.height * 0.05))

        # Check and fix horizontal boundaries
        if group_left < safety_margin_x:
            # Group is too close to left edge - shift crop left
            adjustment = safety_margin_x - group_left
            new_crop_x = crop_x - adjustment
            if new_crop_x >= 0:
                crop_x = new_crop_x
                self.logger.debug(f"🔧 Adjusted group crop left by {adjustment}px for complete visibility")
            else:
                crop_x = 0
                self.logger.debug(f"🔧 Shifted group crop to leftmost position")

        elif group_right > (target_width - safety_margin_x):
            # Group is too close to right edge - shift crop right
            adjustment = group_right - (target_width - safety_margin_x)
            new_crop_x = crop_x + adjustment
            if new_crop_x <= (frame_width - target_width):
                crop_x = new_crop_x
                self.logger.debug(f"🔧 Adjusted group crop right by {adjustment}px for complete visibility")
            else:
                crop_x = frame_width - target_width
                self.logger.debug(f"🔧 Shifted group crop to rightmost position")

        # Check and fix vertical boundaries
        if group_top < safety_margin_y:
            # Group is too close to top edge - shift crop up
            adjustment = safety_margin_y - group_top
            new_crop_y = crop_y - adjustment
            if new_crop_y >= 0:
                crop_y = new_crop_y
                self.logger.debug(f"🔧 Adjusted group crop up by {adjustment}px for complete visibility")
            else:
                crop_y = 0
                self.logger.debug(f"🔧 Shifted group crop to topmost position")

        elif group_bottom > (target_height - safety_margin_y):
            # Group is too close to bottom edge - shift crop down
            adjustment = group_bottom - (target_height - safety_margin_y)
            new_crop_y = crop_y + adjustment
            if new_crop_y <= (frame_height - target_height):
                crop_y = new_crop_y
                self.logger.debug(f"🔧 Adjusted group crop down by {adjustment}px for complete visibility")
            else:
                crop_y = frame_height - target_height
                self.logger.debug(f"🔧 Shifted group crop to bottommost position")

        return crop_x, crop_y

    def _apply_group_composition_refinements(self, crop_x: int, crop_y: int, group_bounds: GroupFaceBounds,
                                           frame_width: int, frame_height: int,
                                           target_width: int, target_height: int) -> Tuple[int, int]:
        """Apply composition refinements for group face positioning"""

        # Ensure group has adequate breathing room
        min_group_margin = int(target_width * 0.1)  # 10% margin around group

        group_left_in_crop = group_bounds.x - crop_x
        group_right_in_crop = (group_bounds.x + group_bounds.width) - crop_x

        if group_left_in_crop < min_group_margin:
            crop_x = max(0, group_bounds.x - min_group_margin)
        elif (target_width - group_right_in_crop) < min_group_margin:
            crop_x = min(frame_width - target_width,
                        (group_bounds.x + group_bounds.width) - (target_width - min_group_margin))

        return crop_x, crop_y

    def assess_centering_quality(self, face: FaceDetection, crop_x: int, crop_y: int,
                               target_width: int, target_height: int) -> CenteringMetrics:
        """Assess the quality of face centering"""

        # Calculate face position in crop with ultra-high precision
        face_center_x_in_crop = face.get_ultra_precise_center_x() - crop_x

        # Horizontal deviation from perfect center
        target_center_x = target_width // 2
        horizontal_deviation = abs(face_center_x_in_crop - target_center_x) / target_width

        # Vertical positioning score (based on composition rules)
        face_area_ratio = (face.width * face.height) / (target_width * target_height)
        shot_type = self._classify_shot_type(face_area_ratio)
        optimal_y = self._get_optimal_eye_position(shot_type, target_height)

        eye_level_y_in_crop = (face.y - crop_y) + int(face.height * 0.3)
        vertical_deviation = abs(eye_level_y_in_crop - optimal_y) / target_height
        vertical_positioning_score = max(0.0, 1.0 - vertical_deviation * 2)

        # Overall composition score
        composition_score = max(0.0, 1.0 - horizontal_deviation * 2 - vertical_deviation)

        # Face visibility score (ensure face is not cut off)
        face_left = face.x - crop_x
        face_right = face.x + face.width - crop_x
        face_top = face.y - crop_y
        face_bottom = face.y + face.height - crop_y

        visibility_penalty = 0.0
        if face_left < 0 or face_right > target_width or face_top < 0 or face_bottom > target_height:
            visibility_penalty = 0.5

        face_visibility_score = max(0.0, 1.0 - visibility_penalty)

        return CenteringMetrics(
            horizontal_deviation=horizontal_deviation,
            vertical_positioning_score=vertical_positioning_score,
            composition_score=composition_score,
            face_visibility_score=face_visibility_score
        )