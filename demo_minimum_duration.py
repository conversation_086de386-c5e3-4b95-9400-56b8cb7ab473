#!/usr/bin/env python3
"""
Demonstration of 10-second minimum duration enforcement for highlight videos

This script demonstrates how the minimum duration requirement is enforced
across all highlight processing functions in the codebase.
"""

import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Simple validation implementation for demo
MIN_HIGHLIGHT_DURATION_SECONDS = 10.0

class DemoHighlightValidator:
    """Demo validator for highlight duration"""
    
    @staticmethod
    def validate_highlight_duration(duration, min_duration=None):
        """Validate highlight duration"""
        min_required = min_duration if min_duration is not None else MIN_HIGHLIGHT_DURATION_SECONDS
        
        result = {
            'valid': duration >= min_required if isinstance(duration, (int, float)) and duration > 0 else False,
            'duration': duration,
            'min_required': min_required,
            'meets_minimum': duration >= min_required if isinstance(duration, (int, float)) else False,
        }
        
        if not result['valid']:
            if not isinstance(duration, (int, float)):
                result['error'] = f"Invalid duration type: {type(duration).__name__}"
            elif duration <= 0:
                result['error'] = f"Duration must be positive, got: {duration}"
            else:
                result['error'] = f"Duration {duration:.1f}s < minimum {min_required:.1f}s"
        
        return result
    
    @staticmethod
    def enforce_minimum_duration(highlights, min_duration=None):
        """Filter highlights to enforce minimum duration"""
        min_required = min_duration if min_duration is not None else MIN_HIGHLIGHT_DURATION_SECONDS
        filtered = []
        
        for highlight in highlights:
            duration = highlight.get('duration')
            if duration is None:
                start_time = highlight.get('start_time', highlight.get('start', 0))
                end_time = highlight.get('end_time', highlight.get('end', 0))
                if start_time is not None and end_time is not None:
                    duration = end_time - start_time
            
            if duration is not None and duration >= min_required:
                filtered.append(highlight)
        
        return filtered


def demo_duration_validation():
    """Demonstrate duration validation"""
    print("=" * 60)
    print("HIGHLIGHT DURATION VALIDATION DEMO")
    print("=" * 60)
    print(f"Minimum required duration: {MIN_HIGHLIGHT_DURATION_SECONDS} seconds")
    print()
    
    validator = DemoHighlightValidator()
    
    # Test various durations
    test_durations = [5.0, 9.9, 10.0, 10.1, 15.5, 30.0]
    
    print("Testing individual durations:")
    print("-" * 40)
    for duration in test_durations:
        result = validator.validate_highlight_duration(duration)
        status = "✅ VALID" if result['valid'] else "❌ INVALID"
        print(f"Duration: {duration:5.1f}s → {status}")
        if not result['valid']:
            print(f"         Reason: {result.get('error', 'Unknown error')}")
    print()


def demo_highlight_filtering():
    """Demonstrate highlight filtering"""
    print("HIGHLIGHT FILTERING DEMO")
    print("-" * 40)
    
    # Sample highlights with various durations
    sample_highlights = [
        {'id': 1, 'duration': 5.0, 'text': 'Short highlight (5s)'},
        {'id': 2, 'duration': 9.9, 'text': 'Almost valid highlight (9.9s)'},
        {'id': 3, 'duration': 10.0, 'text': 'Exactly minimum highlight (10s)'},
        {'id': 4, 'duration': 15.5, 'text': 'Good highlight (15.5s)'},
        {'id': 5, 'duration': 30.0, 'text': 'Long highlight (30s)'},
        {'id': 6, 'start_time': 0, 'end_time': 8, 'text': 'Highlight with timing (8s)'},
        {'id': 7, 'start_time': 10, 'end_time': 25, 'text': 'Highlight with timing (15s)'},
    ]
    
    validator = DemoHighlightValidator()
    
    print(f"Original highlights: {len(sample_highlights)}")
    for highlight in sample_highlights:
        duration = highlight.get('duration')
        if duration is None:
            start = highlight.get('start_time', 0)
            end = highlight.get('end_time', 0)
            duration = end - start
        
        status = "✅" if duration >= MIN_HIGHLIGHT_DURATION_SECONDS else "❌"
        print(f"  {status} ID {highlight['id']}: {duration:.1f}s - {highlight['text']}")
    
    print()
    
    # Filter highlights
    filtered_highlights = validator.enforce_minimum_duration(sample_highlights)
    
    print(f"Filtered highlights: {len(filtered_highlights)} (removed {len(sample_highlights) - len(filtered_highlights)})")
    for highlight in filtered_highlights:
        duration = highlight.get('duration')
        if duration is None:
            start = highlight.get('start_time', 0)
            end = highlight.get('end_time', 0)
            duration = end - start
        
        print(f"  ✅ ID {highlight['id']}: {duration:.1f}s - {highlight['text']}")
    print()


def demo_custom_minimum():
    """Demonstrate custom minimum duration"""
    print("CUSTOM MINIMUM DURATION DEMO")
    print("-" * 40)
    
    validator = DemoHighlightValidator()
    
    highlights = [
        {'duration': 8.0, 'text': '8 second highlight'},
        {'duration': 12.0, 'text': '12 second highlight'},
        {'duration': 18.0, 'text': '18 second highlight'},
        {'duration': 25.0, 'text': '25 second highlight'},
    ]
    
    custom_minimums = [10.0, 15.0, 20.0]
    
    for min_duration in custom_minimums:
        print(f"Using minimum duration: {min_duration}s")
        filtered = validator.enforce_minimum_duration(highlights, min_duration)
        print(f"  Valid highlights: {len(filtered)}/{len(highlights)}")
        for highlight in filtered:
            print(f"    ✅ {highlight['duration']:.1f}s - {highlight['text']}")
        print()


def demo_real_world_scenario():
    """Demonstrate real-world scenario"""
    print("REAL-WORLD SCENARIO DEMO")
    print("-" * 40)
    print("Simulating highlight extraction from a video transcript...")
    print()
    
    # Simulate transcript segments
    transcript_segments = [
        {'start': 0.0, 'end': 3.0, 'text': 'Welcome to our show.'},
        {'start': 3.0, 'end': 6.0, 'text': 'Today we discuss AI.'},
        {'start': 6.0, 'end': 8.0, 'text': 'It\'s fascinating.'},
        {'start': 10.0, 'end': 15.0, 'text': 'What is artificial intelligence exactly?'},
        {'start': 15.0, 'end': 25.0, 'text': 'AI is the simulation of human intelligence in machines.'},
        {'start': 25.0, 'end': 40.0, 'text': 'These systems can learn, reason, and make decisions autonomously.'},
        {'start': 45.0, 'end': 50.0, 'text': 'That sounds amazing.'},
        {'start': 50.0, 'end': 65.0, 'text': 'The applications are endless, from healthcare to transportation.'},
    ]
    
    # Simulate highlight extraction (combine segments into potential highlights)
    potential_highlights = []
    
    # Create highlights by combining consecutive segments
    for i in range(len(transcript_segments)):
        for j in range(i, len(transcript_segments)):
            start_time = transcript_segments[i]['start']
            end_time = transcript_segments[j]['end']
            duration = end_time - start_time
            
            # Skip if there's a gap between segments
            if j > i:
                prev_end = transcript_segments[j-1]['end']
                curr_start = transcript_segments[j]['start']
                if curr_start - prev_end > 2.0:  # 2 second gap tolerance
                    break
            
            if duration <= 30.0:  # Maximum highlight duration
                text = ' '.join(seg['text'] for seg in transcript_segments[i:j+1])
                potential_highlights.append({
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'text': text,
                    'segments': list(range(i, j+1))
                })
    
    print(f"Generated {len(potential_highlights)} potential highlights")
    
    # Apply minimum duration filter
    validator = DemoHighlightValidator()
    valid_highlights = validator.enforce_minimum_duration(potential_highlights)
    
    print(f"After applying {MIN_HIGHLIGHT_DURATION_SECONDS}s minimum: {len(valid_highlights)} valid highlights")
    print()
    
    print("Valid highlights:")
    for i, highlight in enumerate(valid_highlights, 1):
        print(f"{i}. Duration: {highlight['duration']:.1f}s")
        print(f"   Time: {highlight['start_time']:.1f}s - {highlight['end_time']:.1f}s")
        print(f"   Text: {highlight['text'][:80]}{'...' if len(highlight['text']) > 80 else ''}")
        print()


def main():
    """Main demonstration function"""
    print("🎬 HIGHLIGHT VIDEO MINIMUM DURATION ENFORCEMENT DEMO")
    print("📏 Enforcing 10-second minimum for optimal viewer engagement")
    print()
    
    demo_duration_validation()
    demo_highlight_filtering()
    demo_custom_minimum()
    demo_real_world_scenario()
    
    print("=" * 60)
    print("✅ DEMO COMPLETE")
    print("📋 Summary:")
    print(f"   • Minimum duration requirement: {MIN_HIGHLIGHT_DURATION_SECONDS} seconds")
    print("   • Validation enforced across all highlight processing")
    print("   • Clear error messages for debugging")
    print("   • Support for custom minimum durations")
    print("   • Consistent filtering and logging")
    print("=" * 60)


if __name__ == '__main__':
    main()
