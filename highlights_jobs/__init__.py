#!/usr/bin/env python3
"""
Video Highlights Extraction Jobs

A standalone system for extracting intelligent video highlights from transcription data.
This system is designed to run independently from the main video processing pipeline,
allowing for dedicated resources and specialized processing for the complex task of
identifying the best parts of a video.

Core Features:
- Intelligent highlights extraction with multi-component scoring
- Question-Answer pair detection and extraction
- Separate video segment generation
- Cost-effective processing without expensive API calls
- Flexible job management and execution

Usage:
    from highlights_jobs import HighlightsJobManager
    
    manager = HighlightsJobManager()
    job_id = manager.submit_job(transcription_data, video_path, keywords)
    result = manager.get_result(job_id)
"""

__version__ = "1.0.0"
__author__ = "Video Highlights Team"

from .jobs.manager import HighlightsJobManager
from .core.extractor import IntelligentHighlightsExtractor
from .core.qa_extractor import QAHighlightsExtractor

__all__ = [
    'HighlightsJobManager',
    'IntelligentHighlightsExtractor', 
    'QAHighlightsExtractor'
]
