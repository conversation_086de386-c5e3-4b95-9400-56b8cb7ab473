#!/usr/bin/env python3
"""
Job executor for highlights extraction jobs
"""

import os
import time
import logging
from pathlib import Path
from typing import Dict, Any, List

from .models import HighlightsJob, HighlightsJobResult, JobStatus
from ..core.extractor import IntelligentHighlightsExtractor
from ..core.qa_extractor import QAHighlightsExtractor
from ..core.segment_extractor import Q<PERSON>airSegmentExtractor
from ..config.settings import HIGHLIGHTS_OUTPUT_DIR, LOG_FORMAT, LOG_LEVEL


class JobExecutor:
    """
    Executor for highlights extraction jobs
    
    Handles the actual execution of highlights extraction tasks,
    coordinating between different extraction components.
    """

    def __init__(self):
        """Initialize the job executor"""
        self.logger = self._setup_logging()
        
        # Initialize extractors
        self.highlights_extractor = IntelligentHighlightsExtractor()
        self.qa_extractor = QAHighlightsExtractor()
        self.segment_extractor = QAPairSegmentExtractor()
        
        self.logger.info("JobExecutor initialized")

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the job executor"""
        logger = logging.getLogger('highlights_job_executor')
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        # Create formatter
        formatter = logging.Formatter(LOG_FORMAT)
        
        # Create console handler if not exists
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setLevel(getattr(logging, LOG_LEVEL))
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger

    def execute(self, job: HighlightsJob) -> HighlightsJobResult:
        """
        Execute a highlights extraction job
        
        Args:
            job: The job to execute
            
        Returns:
            Job result with extracted highlights and metadata
        """
        start_time = time.time()
        job_id = job.job_id
        input_data = job.input_data
        
        self.logger.info(f"Executing highlights extraction job: {job_id}")
        
        try:
            # Create job output directory
            job_dir = Path(HIGHLIGHTS_OUTPUT_DIR) / job_id
            job_dir.mkdir(exist_ok=True)
            
            # Step 1: Extract intelligent highlights
            job.update_progress(10.0, "Extracting intelligent highlights")
            self.logger.info(f"Step 1: Extracting intelligent highlights for job {job_id}")
            
            highlights_result = self.highlights_extractor.extract(
                transcription_data=input_data.transcription_data,
                video_path=input_data.video_path,
                keywords=input_data.keywords,
                target_length=input_data.target_length,
                output_dir=str(job_dir),
                params=input_data.params or {}
            )
            
            highlights = highlights_result.get('highlights', [])
            self.logger.info(f"Extracted {len(highlights)} intelligent highlights")
            
            # Step 2: Extract Q&A pairs if requested
            qa_pairs = []
            if input_data.extract_qa_pairs:
                job.update_progress(40.0, "Extracting Q&A pairs")
                self.logger.info(f"Step 2: Extracting Q&A pairs for job {job_id}")
                
                qa_result = self.qa_extractor.extract(
                    highlights_data=highlights_result,
                    transcription_data=input_data.transcription_data,
                    keywords=input_data.keywords,
                    output_dir=str(job_dir),
                    params=input_data.params or {}
                )
                
                qa_pairs = qa_result.get('qa_pairs', [])
                self.logger.info(f"Extracted {len(qa_pairs)} Q&A pairs")
            
            # Step 3: Extract individual segments if requested
            segments = []
            if input_data.extract_segments:
                job.update_progress(70.0, "Extracting individual segments")
                self.logger.info(f"Step 3: Extracting individual segments for job {job_id}")
                
                # Use Q&A pairs if available, otherwise use highlights
                source_data = qa_pairs if qa_pairs else highlights
                
                segment_result = self.segment_extractor.extract(
                    source_data=source_data,
                    transcription_data=input_data.transcription_data,
                    video_path=input_data.video_path,
                    output_dir=str(job_dir),
                    params=input_data.params or {}
                )
                
                segments = segment_result.get('segments', [])
                self.logger.info(f"Extracted {len(segments)} individual segments")
            
            # Step 4: Generate output files
            job.update_progress(90.0, "Generating output files")
            self.logger.info(f"Step 4: Generating output files for job {job_id}")
            
            output_files = self._generate_output_files(
                job_dir, highlights, qa_pairs, segments, input_data
            )
            
            # Calculate total duration
            total_duration = sum(h.get('duration', 0) for h in highlights)
            
            # Create result
            execution_time = time.time() - start_time
            
            result = HighlightsJobResult(
                job_id=job_id,
                status=JobStatus.COMPLETED,
                highlights=highlights,
                qa_pairs=qa_pairs,
                segments=segments,
                output_files=output_files,
                execution_time=execution_time,
                total_duration=total_duration,
                metadata={
                    'keywords': input_data.keywords,
                    'target_length': input_data.target_length,
                    'highlights_count': len(highlights),
                    'qa_pairs_count': len(qa_pairs),
                    'segments_count': len(segments),
                    'video_path': input_data.video_path
                }
            )
            
            job.update_progress(100.0, "Completed")
            self.logger.info(f"Job {job_id} completed successfully in {execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Job {job_id} failed: {e}")
            raise

    def _generate_output_files(self, 
                              job_dir: Path,
                              highlights: List[Dict[str, Any]],
                              qa_pairs: List[Dict[str, Any]],
                              segments: List[Dict[str, Any]],
                              input_data) -> Dict[str, str]:
        """Generate output files for the job results"""
        output_files = {}
        
        try:
            # Save highlights JSON
            if highlights:
                highlights_file = job_dir / 'highlights.json'
                import json
                with open(highlights_file, 'w') as f:
                    json.dump(highlights, f, indent=2)
                output_files['highlights_json'] = str(highlights_file)
            
            # Save Q&A pairs JSON
            if qa_pairs:
                qa_file = job_dir / 'qa_pairs.json'
                import json
                with open(qa_file, 'w') as f:
                    json.dump(qa_pairs, f, indent=2)
                output_files['qa_pairs_json'] = str(qa_file)
            
            # Save segments JSON
            if segments:
                segments_file = job_dir / 'segments.json'
                import json
                with open(segments_file, 'w') as f:
                    json.dump(segments, f, indent=2)
                output_files['segments_json'] = str(segments_file)
            
            # Generate summary report
            summary_file = job_dir / 'summary.txt'
            self._generate_summary_report(summary_file, highlights, qa_pairs, segments, input_data)
            output_files['summary_report'] = str(summary_file)
            
        except Exception as e:
            self.logger.warning(f"Failed to generate some output files: {e}")
        
        return output_files

    def _generate_summary_report(self,
                                summary_file: Path,
                                highlights: List[Dict[str, Any]],
                                qa_pairs: List[Dict[str, Any]],
                                segments: List[Dict[str, Any]],
                                input_data) -> None:
        """Generate a human-readable summary report"""
        try:
            with open(summary_file, 'w') as f:
                f.write("HIGHLIGHTS EXTRACTION SUMMARY REPORT\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"Video: {input_data.video_path}\n")
                f.write(f"Keywords: {', '.join(input_data.keywords)}\n")
                f.write(f"Target Length: {input_data.target_length} seconds\n\n")
                
                # Highlights summary
                f.write(f"INTELLIGENT HIGHLIGHTS: {len(highlights)}\n")
                f.write("-" * 30 + "\n")
                total_duration = sum(h.get('duration', 0) for h in highlights)
                f.write(f"Total Duration: {total_duration:.1f} seconds\n")
                
                for i, highlight in enumerate(highlights, 1):
                    f.write(f"{i}. {highlight.get('start_time', 0):.1f}s - {highlight.get('end_time', 0):.1f}s ")
                    f.write(f"({highlight.get('duration', 0):.1f}s) - Score: {highlight.get('score', 0):.3f}\n")
                
                # Q&A pairs summary
                if qa_pairs:
                    f.write(f"\nQ&A PAIRS: {len(qa_pairs)}\n")
                    f.write("-" * 20 + "\n")
                    for i, qa in enumerate(qa_pairs, 1):
                        f.write(f"{i}. Q: {qa.get('question_text', '')[:100]}...\n")
                        f.write(f"   A: {qa.get('answer_text', '')[:100]}...\n")
                        f.write(f"   Duration: {qa.get('duration', 0):.1f}s\n\n")
                
                # Segments summary
                if segments:
                    f.write(f"INDIVIDUAL SEGMENTS: {len(segments)}\n")
                    f.write("-" * 25 + "\n")
                    for i, segment in enumerate(segments, 1):
                        f.write(f"{i}. {segment.get('start_time', 0):.1f}s - {segment.get('end_time', 0):.1f}s ")
                        f.write(f"({segment.get('duration', 0):.1f}s)\n")
                
        except Exception as e:
            self.logger.warning(f"Failed to generate summary report: {e}")
