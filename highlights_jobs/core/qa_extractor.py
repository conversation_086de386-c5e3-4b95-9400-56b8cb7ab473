#!/usr/bin/env python3
"""
Question-Answer Highlights Extractor for Video Highlights Jobs

This module processes intelligent highlights to extract engaging question-answer pairs
for short-form vertical video content optimized for social media platforms.
"""

import os
import time
import logging
from typing import List, Dict, Any, Optional

from ..utils.question_identifier import QuestionSegmentIdentifier
from ..config.settings import MIN_QA_QUALITY_SCORE, MIN_ANSWER_LENGTH


class QAHighlightsExtractor:
    """
    Question-Answer Highlights Extractor
    
    Processes highlights to extract engaging Q&A pairs for social media content.
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.question_identifier = QuestionSegmentIdentifier()
        self.logger.info("QAHighlightsExtractor initialized")

    def extract(self,
                highlights_data: Dict[str, Any],
                transcription_data: Dict[str, Any],
                keywords: List[str],
                output_dir: str = "",
                params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extract Q&A pairs from highlights data
        
        Args:
            highlights_data: Results from intelligent highlights extraction
            transcription_data: Original transcription data
            keywords: Keywords for relevance scoring
            output_dir: Output directory for results
            params: Additional parameters
            
        Returns:
            Extraction result with Q&A pairs and metadata
        """
        start_time = time.time()
        params = params or {}
        
        self.logger.info("Starting Q&A highlights extraction")
        
        try:
            # Get highlights and transcript segments
            highlights = highlights_data.get('highlights', [])
            segments = transcription_data.get('segments', [])
            
            if not highlights:
                self.logger.warning("No highlights provided for Q&A extraction")
                return self._empty_result(start_time)
            
            if not segments:
                self.logger.warning("No transcript segments available")
                return self._empty_result(start_time)
            
            self.logger.info(f"Processing {len(highlights)} highlights for Q&A extraction")
            
            # Extract Q&A pairs from each highlight
            qa_pairs = []
            for i, highlight in enumerate(highlights):
                highlight_qa_pairs = self._extract_qa_from_highlight(highlight, segments, keywords, i)
                qa_pairs.extend(highlight_qa_pairs)
            
            # Filter and rank Q&A pairs
            filtered_qa_pairs = self._filter_and_rank_qa_pairs(qa_pairs, keywords)
            
            # Generate titles and descriptions for social media
            enhanced_qa_pairs = self._enhance_qa_pairs_for_social_media(filtered_qa_pairs)
            
            execution_time = time.time() - start_time
            
            self.logger.info(f"Q&A extraction completed in {execution_time:.2f}s")
            self.logger.info(f"Extracted {len(enhanced_qa_pairs)} Q&A pairs from {len(highlights)} highlights")
            
            return {
                'qa_pairs': enhanced_qa_pairs,
                'total_qa_pairs': len(enhanced_qa_pairs),
                'source_highlights': len(highlights),
                'execution_time': execution_time,
                'keywords': keywords
            }
            
        except Exception as e:
            self.logger.error(f"Q&A extraction failed: {e}")
            raise

    def _extract_qa_from_highlight(self, highlight: Dict[str, Any], 
                                  segments: List[Dict[str, Any]], 
                                  keywords: List[str], highlight_idx: int) -> List[Dict[str, Any]]:
        """Extract Q&A pairs from a single highlight"""
        qa_pairs = []
        
        # Find segments within the highlight timeframe
        highlight_start = highlight.get('start_time', 0)
        highlight_end = highlight.get('end_time', 0)
        
        highlight_segments = []
        for segment in segments:
            seg_start = segment.get('start', 0)
            seg_end = segment.get('end', 0)
            
            # Check if segment overlaps with highlight
            if (seg_start < highlight_end and seg_end > highlight_start):
                highlight_segments.append(segment)
        
        if not highlight_segments:
            return qa_pairs
        
        # Use question identifier to find Q&A patterns within this highlight
        question_segments = self.question_identifier.identify_all_question_segments(highlight_segments)
        
        for qs in question_segments:
            # Ensure Q&A pair is within highlight boundaries
            qa_start = max(qs['qa_span_start'], highlight_start)
            qa_end = min(qs['qa_span_end'], highlight_end)
            
            if qa_end <= qa_start:
                continue
            
            # Check quality thresholds
            if (qs['quality_score'] < MIN_QA_QUALITY_SCORE or 
                len(qs['answer_text']) < MIN_ANSWER_LENGTH):
                continue
            
            # Calculate keyword relevance
            combined_text = qs['question_text'] + ' ' + qs['answer_text']
            keyword_relevance = self._calculate_keyword_relevance(combined_text, keywords)
            
            qa_pair = {
                'question_text': qs['question_text'],
                'answer_text': qs['answer_text'],
                'start_time': qa_start,
                'end_time': qa_end,
                'duration': qa_end - qa_start,
                'quality_score': qs['quality_score'],
                'engagement_score': qs['engagement_score'],
                'keyword_relevance': keyword_relevance,
                'source_highlight_idx': highlight_idx,
                'question_speaker': qs.get('question_speaker', 'unknown'),
                'answer_speaker': qs.get('answer_speaker', 'unknown'),
                'type': 'qa_pair'
            }
            
            qa_pairs.append(qa_pair)
        
        return qa_pairs

    def _calculate_keyword_relevance(self, text: str, keywords: List[str]) -> float:
        """Calculate keyword relevance score for text"""
        if not keywords or not text:
            return 0.0
        
        text_lower = text.lower()
        matches = 0
        
        for keyword in keywords:
            if keyword.lower() in text_lower:
                matches += 1
        
        return min(1.0, matches / len(keywords))

    def _filter_and_rank_qa_pairs(self, qa_pairs: List[Dict[str, Any]], 
                                 keywords: List[str]) -> List[Dict[str, Any]]:
        """Filter and rank Q&A pairs by quality and relevance"""
        if not qa_pairs:
            return []
        
        # Calculate composite scores
        for qa_pair in qa_pairs:
            # Weighted combination of quality, engagement, and keyword relevance
            composite_score = (
                0.4 * qa_pair['quality_score'] +
                0.3 * qa_pair['engagement_score'] +
                0.3 * qa_pair['keyword_relevance']
            )
            qa_pair['composite_score'] = composite_score
        
        # Sort by composite score (highest first)
        qa_pairs.sort(key=lambda x: x['composite_score'], reverse=True)
        
        # Filter out low-quality pairs
        filtered_pairs = [
            qa for qa in qa_pairs 
            if qa['composite_score'] > 0.3  # Minimum composite score threshold
        ]
        
        return filtered_pairs

    def _enhance_qa_pairs_for_social_media(self, qa_pairs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance Q&A pairs with social media optimizations"""
        enhanced_pairs = []
        
        for i, qa_pair in enumerate(qa_pairs):
            enhanced_pair = qa_pair.copy()
            
            # Generate engaging title
            title = self._generate_engaging_title(qa_pair['question_text'], qa_pair['answer_text'])
            
            # Generate description
            description = self._generate_description(qa_pair['question_text'], qa_pair['answer_text'])
            
            # Add social media metadata
            enhanced_pair.update({
                'title': title,
                'description': description,
                'hashtags': self._generate_hashtags(qa_pair['question_text'], qa_pair['answer_text']),
                'optimal_for_vertical': self._is_optimal_for_vertical(qa_pair),
                'clip_id': f"qa_clip_{i:03d}",
                'social_media_ready': True
            })
            
            enhanced_pairs.append(enhanced_pair)
        
        return enhanced_pairs

    def _generate_engaging_title(self, question: str, answer: str) -> str:
        """Generate an engaging title for the Q&A pair"""
        # Truncate question if too long
        if len(question) > 60:
            question = question[:57] + "..."
        
        # Remove question mark for title format
        if question.endswith('?'):
            question = question[:-1]
        
        # Capitalize first letter
        title = question.strip().capitalize()
        
        # Add engaging prefix for certain question types
        question_lower = question.lower()
        if any(word in question_lower for word in ['how', 'what', 'why']):
            if not any(title.startswith(prefix) for prefix in ['How', 'What', 'Why']):
                title = f"Learn: {title}"
        
        return title

    def _generate_description(self, question: str, answer: str) -> str:
        """Generate a description for the Q&A pair"""
        # Truncate answer if too long
        answer_preview = answer[:100] + "..." if len(answer) > 100 else answer
        
        description = f"Q: {question}\nA: {answer_preview}"
        
        return description

    def _generate_hashtags(self, question: str, answer: str) -> List[str]:
        """Generate relevant hashtags for the Q&A pair"""
        hashtags = ['#QA', '#Learn', '#Knowledge']
        
        # Add topic-specific hashtags based on content
        combined_text = (question + ' ' + answer).lower()
        
        topic_hashtags = {
            'technology': '#Tech',
            'ai': '#AI',
            'business': '#Business',
            'science': '#Science',
            'health': '#Health',
            'education': '#Education',
            'future': '#Future',
            'innovation': '#Innovation'
        }
        
        for topic, hashtag in topic_hashtags.items():
            if topic in combined_text:
                hashtags.append(hashtag)
        
        return hashtags[:5]  # Limit to 5 hashtags

    def _is_optimal_for_vertical(self, qa_pair: Dict[str, Any]) -> bool:
        """Check if Q&A pair is optimal for vertical video format"""
        duration = qa_pair.get('duration', 0)
        
        # Optimal duration for vertical videos (15-30 seconds)
        if 15 <= duration <= 30:
            return True
        
        # Also good if slightly shorter but high engagement
        if 10 <= duration < 15 and qa_pair.get('engagement_score', 0) > 0.7:
            return True
        
        return False

    def _empty_result(self, start_time: float) -> Dict[str, Any]:
        """Return empty result structure"""
        return {
            'qa_pairs': [],
            'total_qa_pairs': 0,
            'source_highlights': 0,
            'execution_time': time.time() - start_time,
            'keywords': []
        }
