#!/usr/bin/env python3
"""
Intelligent Highlights Extractor for Video Highlights Jobs

This module implements the core highlights extraction logic with multi-component scoring.
"""

import os
import time
import logging
from typing import Dict, Any, List, Optional
from collections import defaultdict

# Optional numpy import
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

from ..utils.scoring import HighlightsScorer
from ..utils.iframe_utils import IFrameExtractor
from ..utils.question_identifier import QuestionSegmentIdentifier
from ..config.settings import (
    DEFAULT_TARGET_LENGTH,
    MIN_SPAN_DURATION,
    MAX_SPAN_DURATION,
    PADDING_SECONDS,
    MIN_QUALITY_SCORE,
    MIN_WORDS_PER_SPAN,
    QA_COVERAGE_THRESHOLD
)

# Import validation utilities for consistent minimum duration enforcement
try:
    from utils.validation_utils import HighlightValidationUtils
except ImportError:
    HighlightValidationUtils = None


class IntelligentHighlightsExtractor:
    """
    Advanced Intelligent Video Highlights Extractor

    Implements sophisticated scoring algorithms for automatic highlight detection
    with I-frame boundary snapping and cost-effective processing.
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # Initialize components
        self.highlights_scorer = HighlightsScorer()
        self.iframe_extractor = IFrameExtractor()
        self.question_identifier = QuestionSegmentIdentifier()

        self.logger.info("IntelligentHighlightsExtractor initialized")

    def extract(self,
                transcription_data: Dict[str, Any],
                video_path: str,
                keywords: List[str],
                target_length: int = DEFAULT_TARGET_LENGTH,
                output_dir: str = "",
                params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extract intelligent highlights from video transcription

        Args:
            transcription_data: Transcription data with segments
            video_path: Path to the video file
            keywords: Keywords to search for
            target_length: Target total duration in seconds
            output_dir: Output directory for results
            params: Additional parameters

        Returns:
            Extraction result with highlights and metadata
        """
        start_time = time.time()
        params = params or {}

        self.logger.info(f"Starting intelligent highlights extraction")
        self.logger.info(f"Target length: {target_length}s, Keywords: {keywords}")

        try:
            # Extract segments from transcription data
            segments = transcription_data.get('segments', [])
            if not segments:
                raise ValueError("No transcript segments found")

            self.logger.info(f"Processing {len(segments)} transcript segments")

            # Step 1: PRIORITY - Scan entire video for complete Q&A pairs first
            complete_qa_pairs = self._scan_for_complete_qa_pairs(segments, keywords)
            self.logger.info(f"🎯 Found {len(complete_qa_pairs)} complete Q&A pairs in full video")

            # Step 2: If we have good Q&A pairs, prioritize them heavily
            if complete_qa_pairs:
                qa_highlights = self._create_qa_priority_highlights(complete_qa_pairs, target_length)
                self.logger.info(f"✨ Created {len(qa_highlights)} Q&A priority highlights")

                # If Q&A pairs cover most of target duration, use them as primary highlights
                qa_total_duration = sum(qa['duration'] for qa in qa_highlights)
                if qa_total_duration >= target_length * QA_COVERAGE_THRESHOLD:
                    self.logger.info(f"🚀 Q&A pairs provide {qa_total_duration:.1f}s of {target_length}s target - using Q&A-first approach")
                    return self._finalize_qa_first_result(qa_highlights, complete_qa_pairs, start_time, video_path, output_dir)

            # Step 3: Fallback to traditional approach if insufficient Q&A content
            self.logger.info("📺 Insufficient Q&A content, falling back to traditional highlights approach")

            # Identify all question-based segments throughout the video
            question_segments = self.question_identifier.identify_all_question_segments(segments)
            self.logger.info(f"Identified {len(question_segments)} question-based segments")

            # Generate continuous sentence spans with question prioritization
            spans = self._generate_sentence_spans(segments, question_segments)
            self.logger.info(f"Generated {len(spans)} sentence spans")

            # Score each span using multi-component algorithm with question boost
            scored_spans = self._score_spans(spans, segments, keywords, question_segments)
            self.logger.info(f"Scored {len(scored_spans)} spans")

            # Apply quality filters
            filtered_spans = self._apply_quality_filters(scored_spans, segments)
            self.logger.info(f"Filtered to {len(filtered_spans)} high-quality spans")

            # Select optimal spans within target duration
            selected_spans = self._select_optimal_spans(filtered_spans, target_length)
            self.logger.info(f"Selected {len(selected_spans)} optimal spans")

            # Add padding and merge overlapping segments
            padded_spans = self._add_padding_and_merge(selected_spans)
            self.logger.info(f"Merged to {len(padded_spans)} final spans")

            # Step 6: Extract I-frame timestamps and snap boundaries
            iframe_timestamps = self.iframe_extractor.extract_iframe_timestamps(video_path)
            snapped_highlights = self.iframe_extractor.snap_highlights_to_iframes(padded_spans, iframe_timestamps)

            # Calculate total duration
            total_duration = sum(h['duration'] for h in snapped_highlights)
            execution_time = time.time() - start_time

            self.logger.info(f"Intelligent highlights extraction completed in {execution_time:.2f}s")
            self.logger.info(f"Generated {len(snapped_highlights)} highlights with total duration {total_duration:.1f}s")

            return {
                'highlights': snapped_highlights,
                'total_duration': total_duration,
                'execution_time': execution_time,
                'qa_first_approach': False,
                'complete_qa_pairs_found': len(complete_qa_pairs),
                'question_segments_found': len(question_segments),
                'spans_generated': len(spans),
                'spans_after_filtering': len(filtered_spans),
                'final_highlights_count': len(snapped_highlights),
                'target_length': target_length,
                'keywords': keywords
            }

        except Exception as e:
            self.logger.error(f"Intelligent highlights extraction failed: {e}")
            raise

    def _scan_for_complete_qa_pairs(self, segments: List[Dict[str, Any]], keywords: List[str]) -> List[Dict[str, Any]]:
        """Scan entire video for complete Q&A pairs"""
        question_segments = self.question_identifier.identify_all_question_segments(segments)

        # Filter for high-quality Q&A pairs
        complete_qa_pairs = []
        for qs in question_segments:
            if (qs['quality_score'] > 0.4 and
                qs['qa_span_duration'] >= MIN_SPAN_DURATION and
                qs['qa_span_duration'] <= MAX_SPAN_DURATION):

                # Add keyword relevance scoring
                combined_text = qs['question_text'] + ' ' + qs['answer_text']
                keyword_score = self.highlights_scorer.calculate_keyword_density(combined_text, keywords)

                qa_pair = {
                    'start_time': qs['qa_span_start'],
                    'end_time': qs['qa_span_end'],
                    'duration': qs['qa_span_duration'],
                    'question_text': qs['question_text'],
                    'answer_text': qs['answer_text'],
                    'quality_score': qs['quality_score'],
                    'engagement_score': qs['engagement_score'],
                    'keyword_score': keyword_score,
                    'composite_score': (qs['quality_score'] + qs['engagement_score'] + keyword_score) / 3,
                    'type': 'qa_pair'
                }
                complete_qa_pairs.append(qa_pair)

        # Sort by composite score
        complete_qa_pairs.sort(key=lambda x: x['composite_score'], reverse=True)
        return complete_qa_pairs

    def _create_qa_priority_highlights(self, qa_pairs: List[Dict[str, Any]], target_length: int) -> List[Dict[str, Any]]:
        """Create highlights prioritizing Q&A pairs"""
        selected_pairs = []
        total_duration = 0.0

        for qa_pair in qa_pairs:
            if total_duration + qa_pair['duration'] <= target_length:
                selected_pairs.append(qa_pair)
                total_duration += qa_pair['duration']

            if total_duration >= target_length * 0.9:  # 90% of target
                break

        return selected_pairs

    def _finalize_qa_first_result(self, qa_highlights: List[Dict[str, Any]],
                                 complete_qa_pairs: List[Dict[str, Any]],
                                 start_time: float, video_path: str, output_dir: str) -> Dict[str, Any]:
        """Finalize result when using Q&A-first approach"""
        # Extract I-frame timestamps and snap boundaries
        iframe_timestamps = self.iframe_extractor.extract_iframe_timestamps(video_path)
        snapped_highlights = self.iframe_extractor.snap_highlights_to_iframes(qa_highlights, iframe_timestamps)

        # Calculate metrics
        total_duration = sum(h['duration'] for h in snapped_highlights)
        execution_time = time.time() - start_time

        self.logger.info(f"🎯 Q&A-first highlights extraction completed in {execution_time:.2f}s")
        self.logger.info(f"✨ Generated {len(snapped_highlights)} Q&A highlights with total duration {total_duration:.1f}s")

        return {
            'highlights': snapped_highlights,
            'total_duration': total_duration,
            'execution_time': execution_time,
            'qa_first_approach': True,
            'complete_qa_pairs_found': len(complete_qa_pairs),
            'qa_highlights_selected': len(qa_highlights),
            'final_highlights_count': len(snapped_highlights),
            'approach': 'qa_priority'
        }

    def _generate_sentence_spans(self, segments: List[Dict[str, Any]],
                                question_segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate continuous sentence spans for scoring"""
        spans = []
        question_indices = {qs['question_idx'] for qs in question_segments}

        for start_idx in range(len(segments)):
            for end_idx in range(start_idx, len(segments)):
                # Calculate span duration
                start_time = segments[start_idx].get('start', 0)
                end_time = segments[end_idx].get('end', 0)
                duration = end_time - start_time

                # Check duration constraints
                if duration < MIN_SPAN_DURATION or duration > MAX_SPAN_DURATION:
                    continue

                # Combine text from all segments in span
                span_text = ' '.join(seg.get('text', '') for seg in segments[start_idx:end_idx + 1])

                # Check minimum word count
                if len(span_text.split()) < MIN_WORDS_PER_SPAN:
                    continue

                # Check if span contains question segments
                contains_question = any(idx in question_indices for idx in range(start_idx, end_idx + 1))

                span = {
                    'start_segment_idx': start_idx,
                    'end_segment_idx': end_idx,
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'text': span_text,
                    'contains_question': contains_question,
                    'segment_count': end_idx - start_idx + 1
                }
                spans.append(span)

        return spans

    def _score_spans(self, spans: List[Dict[str, Any]], segments: List[Dict[str, Any]],
                    keywords: List[str], question_segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Score spans using multi-component algorithm"""
        # Pre-compute Q&A scores for all segments
        qa_scores = self.highlights_scorer.detect_qa_patterns(segments)

        scored_spans = []

        for span in spans:
            start_idx = span['start_segment_idx']
            end_idx = span['end_segment_idx']
            span_text = span['text']

            # Component 1: Q&A Detection (35%)
            qa_score = max(qa_scores.get(i, 0.0) for i in range(start_idx, end_idx + 1))

            # Component 2: Keyword Density (25%)
            keyword_density = self.highlights_scorer.calculate_keyword_density(span_text, keywords)

            # Component 3: Emotion Intensity (25%)
            emotion_intensity = self.highlights_scorer.calculate_emotion_intensity(span_text)

            # Component 4: Novelty (15%)
            # Get context from previous 60 seconds
            context_texts = self._get_context_texts(segments, start_idx, 60.0)
            novelty = self.highlights_scorer.calculate_novelty_score(span_text, context_texts)

            # Calculate composite score
            composite_score = self.highlights_scorer.calculate_composite_score(
                qa_score, keyword_density, emotion_intensity, novelty
            )

            # Question boost
            if span['contains_question']:
                composite_score *= 1.2  # 20% boost for question-containing spans

            # Score density (score per second)
            score_density = composite_score / span['duration'] if span['duration'] > 0 else 0

            scored_span = span.copy()
            scored_span.update({
                'qa_score': qa_score,
                'keyword_density': keyword_density,
                'emotion_intensity': emotion_intensity,
                'novelty': novelty,
                'composite_score': composite_score,
                'score_density': score_density
            })

            scored_spans.append(scored_span)

        # Sort by score density (score per second)
        scored_spans.sort(key=lambda x: x['score_density'], reverse=True)

        return scored_spans

    def _get_context_texts(self, segments: List[Dict[str, Any]], current_idx: int,
                          context_duration: float) -> List[str]:
        """Get context texts from previous segments within duration"""
        context_texts = []
        current_start = segments[current_idx].get('start', 0)

        for i in range(current_idx - 1, -1, -1):
            segment = segments[i]
            segment_start = segment.get('start', 0)

            if current_start - segment_start > context_duration:
                break

            text = segment.get('text', '').strip()
            if text:
                context_texts.insert(0, text)  # Insert at beginning to maintain order

        return context_texts

    def _apply_quality_filters(self, scored_spans: List[Dict[str, Any]],
                              segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply quality filters to remove low-quality spans"""
        filtered_spans = []

        for span in scored_spans:
            # Filter by minimum quality score
            if span['composite_score'] < MIN_QUALITY_SCORE:
                continue

            # Filter by minimum word count
            if len(span['text'].split()) < MIN_WORDS_PER_SPAN:
                continue

            # Filter by minimum duration using validation utilities
            if HighlightValidationUtils:
                validation = HighlightValidationUtils.validate_highlight_duration(span.get('duration', 0))
                if not validation['valid']:
                    continue
            else:
                # Fallback duration check
                if span.get('duration', 0) < MIN_SPAN_DURATION:
                    continue

            # Additional quality checks can be added here
            filtered_spans.append(span)

        # Apply final duration enforcement using validation utilities
        if HighlightValidationUtils:
            filtered_spans = HighlightValidationUtils.enforce_minimum_duration(
                filtered_spans,
                min_duration=MIN_SPAN_DURATION,
                log_filtered=True
            )

        return filtered_spans

    def _select_optimal_spans(self, filtered_spans: List[Dict[str, Any]],
                             target_length: int) -> List[Dict[str, Any]]:
        """Select optimal spans within target duration using greedy algorithm"""
        selected_spans = []
        total_duration = 0.0
        used_segments = set()

        for span in filtered_spans:
            # Check for overlap with already selected spans
            span_segments = set(range(span['start_segment_idx'], span['end_segment_idx'] + 1))
            if span_segments & used_segments:
                continue  # Skip overlapping spans

            # Check if adding this span would exceed target length
            if total_duration + span['duration'] > target_length:
                continue

            # Add span
            selected_spans.append(span)
            total_duration += span['duration']
            used_segments.update(span_segments)

            # Stop if we've reached the target length
            if total_duration >= target_length * 0.95:  # 95% of target
                break

        # Sort by start time for final output
        selected_spans.sort(key=lambda x: x['start_time'])

        return selected_spans

    def _add_padding_and_merge(self, selected_spans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Add padding and merge overlapping segments"""
        if not selected_spans:
            return []

        # Add padding to each span
        padded_spans = []
        for span in selected_spans:
            padded_span = span.copy()
            padded_span['start_time'] = max(0, span['start_time'] - PADDING_SECONDS)
            padded_span['end_time'] = span['end_time'] + PADDING_SECONDS
            padded_span['duration'] = padded_span['end_time'] - padded_span['start_time']
            padded_spans.append(padded_span)

        # Sort by start time
        padded_spans.sort(key=lambda x: x['start_time'])

        # Merge overlapping spans
        merged_spans = []
        current_span = padded_spans[0]

        for next_span in padded_spans[1:]:
            if next_span['start_time'] <= current_span['end_time']:
                # Merge spans
                current_span['end_time'] = max(current_span['end_time'], next_span['end_time'])
                current_span['duration'] = current_span['end_time'] - current_span['start_time']
                # Combine text
                current_span['text'] = current_span['text'] + ' ' + next_span['text']
                # Take higher score
                current_span['composite_score'] = max(current_span['composite_score'], next_span['composite_score'])
            else:
                # No overlap, add current span and move to next
                merged_spans.append(current_span)
                current_span = next_span

        # Add the last span
        merged_spans.append(current_span)

        return merged_spans
