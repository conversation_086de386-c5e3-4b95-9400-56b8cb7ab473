#!/usr/bin/env python3
"""
Q&A Pair Segment Extractor for Video Highlights Jobs

This module extracts individual video segments for each question-answer pair,
creating separate video clips optimized for social media platforms.
"""

import os
import time
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..utils.iframe_utils import IFrameExtractor
from ..config.settings import OUTPUT_RESOLUTION, OUTPUT_FORMAT


class QAPairSegmentExtractor:
    """
    Q&A Pair Segment Extractor
    
    Extracts individual video segments for each Q&A pair with vertical video optimization.
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.iframe_extractor = IFrameExtractor()
        self.logger.info("QAPairSegmentExtractor initialized")

    def extract(self,
                source_data: List[Dict[str, Any]],
                transcription_data: Dict[str, Any],
                video_path: str,
                output_dir: str = "",
                params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extract individual video segments for Q&A pairs
        
        Args:
            source_data: Q&A pairs or highlights data
            transcription_data: Original transcription data
            video_path: Path to source video file
            output_dir: Output directory for segments
            params: Additional parameters
            
        Returns:
            Extraction result with segment information
        """
        start_time = time.time()
        params = params or {}
        
        self.logger.info("Starting Q&A segment extraction")
        
        try:
            if not source_data:
                self.logger.warning("No source data provided for segment extraction")
                return self._empty_result(start_time)
            
            if not os.path.exists(video_path):
                raise ValueError(f"Video file not found: {video_path}")
            
            # Create output directory
            segments_dir = Path(output_dir) / "segments"
            segments_dir.mkdir(parents=True, exist_ok=True)
            
            self.logger.info(f"Processing {len(source_data)} items for segment extraction")
            
            # Extract I-frame timestamps for precise cutting
            iframe_timestamps = self.iframe_extractor.extract_iframe_timestamps(video_path)
            
            # Process each item (Q&A pair or highlight)
            extracted_segments = []
            for i, item in enumerate(source_data):
                segment_info = self._extract_single_segment(
                    item, video_path, segments_dir, iframe_timestamps, i
                )
                if segment_info:
                    extracted_segments.append(segment_info)
            
            execution_time = time.time() - start_time
            
            self.logger.info(f"Segment extraction completed in {execution_time:.2f}s")
            self.logger.info(f"Extracted {len(extracted_segments)} video segments")
            
            return {
                'segments': extracted_segments,
                'total_segments': len(extracted_segments),
                'source_items': len(source_data),
                'output_directory': str(segments_dir),
                'execution_time': execution_time,
                'video_format': OUTPUT_FORMAT,
                'resolution': OUTPUT_RESOLUTION
            }
            
        except Exception as e:
            self.logger.error(f"Segment extraction failed: {e}")
            raise

    def _extract_single_segment(self, 
                               item: Dict[str, Any],
                               video_path: str,
                               output_dir: Path,
                               iframe_timestamps: List[float],
                               index: int) -> Optional[Dict[str, Any]]:
        """Extract a single video segment"""
        try:
            # Get timing information
            start_time = item.get('start_time', 0)
            end_time = item.get('end_time', 0)
            duration = end_time - start_time
            
            if duration <= 0:
                self.logger.warning(f"Invalid duration for item {index}: {duration}")
                return None
            
            # Snap to I-frames for precise cutting
            if iframe_timestamps:
                snapped_item = self.iframe_extractor.snap_highlights_to_iframes([item], iframe_timestamps)[0]
                start_time = snapped_item['start_time']
                end_time = snapped_item['end_time']
                duration = snapped_item['duration']
            
            # Generate output filename
            item_type = item.get('type', 'segment')
            if item_type == 'qa_pair':
                # Use question text for filename (sanitized)
                question_text = item.get('question_text', f'qa_{index}')
                safe_filename = self._sanitize_filename(question_text)
                filename = f"qa_{index:03d}_{safe_filename}_{start_time:.1f}s-{end_time:.1f}s.{OUTPUT_FORMAT}"
            else:
                filename = f"segment_{index:03d}_{start_time:.1f}s-{end_time:.1f}s.{OUTPUT_FORMAT}"
            
            output_path = output_dir / filename
            
            # Extract the segment
            success = self._extract_video_segment(
                video_path, output_path, start_time, duration
            )
            
            if not success:
                return None
            
            # Create segment information
            segment_info = {
                'index': index,
                'filename': filename,
                'file_path': str(output_path),
                'start_time': start_time,
                'end_time': end_time,
                'duration': duration,
                'type': item_type,
                'file_size': output_path.stat().st_size if output_path.exists() else 0,
                'resolution': OUTPUT_RESOLUTION,
                'format': OUTPUT_FORMAT
            }
            
            # Add Q&A specific information
            if item_type == 'qa_pair':
                segment_info.update({
                    'question_text': item.get('question_text', ''),
                    'answer_text': item.get('answer_text', ''),
                    'title': item.get('title', ''),
                    'description': item.get('description', ''),
                    'hashtags': item.get('hashtags', []),
                    'quality_score': item.get('quality_score', 0),
                    'engagement_score': item.get('engagement_score', 0),
                    'optimal_for_vertical': item.get('optimal_for_vertical', False)
                })
            
            # Add highlight specific information
            elif 'composite_score' in item:
                segment_info.update({
                    'composite_score': item.get('composite_score', 0),
                    'qa_score': item.get('qa_score', 0),
                    'keyword_density': item.get('keyword_density', 0),
                    'emotion_intensity': item.get('emotion_intensity', 0),
                    'novelty': item.get('novelty', 0)
                })
            
            return segment_info
            
        except Exception as e:
            self.logger.warning(f"Failed to extract segment {index}: {e}")
            return None

    def _extract_video_segment(self, 
                              video_path: str,
                              output_path: Path,
                              start_time: float,
                              duration: float) -> bool:
        """Extract a video segment using FFmpeg"""
        try:
            import subprocess
            
            # Build FFmpeg command for vertical video optimization
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(duration),
                '-vf', f'scale={OUTPUT_RESOLUTION}:force_original_aspect_ratio=decrease,pad={OUTPUT_RESOLUTION}:(ow-iw)/2:(oh-ih)/2',
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-c:a', 'aac',
                '-b:a', '128k',
                '-avoid_negative_ts', 'make_zero',
                '-y',  # Overwrite output file
                str(output_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.logger.debug(f"Successfully extracted segment: {output_path}")
                return True
            else:
                self.logger.warning(f"FFmpeg failed for {output_path}: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.warning(f"Timeout extracting segment: {output_path}")
            return False
        except Exception as e:
            self.logger.warning(f"Error extracting segment {output_path}: {e}")
            return False

    def _sanitize_filename(self, text: str, max_length: int = 50) -> str:
        """Sanitize text for use in filename"""
        import re
        
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '', text)
        sanitized = re.sub(r'\s+', '_', sanitized)
        sanitized = sanitized.strip('._')
        
        # Truncate if too long
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
        
        # Ensure it's not empty
        if not sanitized:
            sanitized = "segment"
        
        return sanitized

    def _empty_result(self, start_time: float) -> Dict[str, Any]:
        """Return empty result structure"""
        return {
            'segments': [],
            'total_segments': 0,
            'source_items': 0,
            'output_directory': '',
            'execution_time': time.time() - start_time,
            'video_format': OUTPUT_FORMAT,
            'resolution': OUTPUT_RESOLUTION
        }
