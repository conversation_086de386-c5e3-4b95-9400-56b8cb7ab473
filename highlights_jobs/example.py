#!/usr/bin/env python3
"""
Example usage of the Video Highlights Extraction Jobs system

This script demonstrates how to use the highlights jobs system programmatically.
"""

import os
import sys
import json
import time
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from highlights_jobs import HighlightsJobManager


def create_sample_transcription_data():
    """Create sample transcription data for testing"""
    return {
        "segments": [
            {
                "start": 0.0,
                "end": 5.2,
                "text": "Welcome to our discussion about artificial intelligence and its impact on the future.",
                "speaker": "host"
            },
            {
                "start": 5.5,
                "end": 8.1,
                "text": "What do you think about the current state of AI technology?",
                "speaker": "host"
            },
            {
                "start": 8.5,
                "end": 15.3,
                "text": "I think we're at a fascinating inflection point. AI is becoming more capable every day, but we're also seeing important questions about safety and ethics emerge.",
                "speaker": "guest"
            },
            {
                "start": 15.8,
                "end": 18.9,
                "text": "That's really interesting. Can you explain what you mean by safety concerns?",
                "speaker": "host"
            },
            {
                "start": 19.2,
                "end": 28.7,
                "text": "Well, as AI systems become more powerful, we need to ensure they're aligned with human values. There's also the question of job displacement and how we prepare society for these changes.",
                "speaker": "guest"
            },
            {
                "start": 29.0,
                "end": 32.4,
                "text": "How do you see the future of work evolving with AI?",
                "speaker": "host"
            },
            {
                "start": 32.8,
                "end": 42.1,
                "text": "I believe AI will augment human capabilities rather than simply replace jobs. We'll see new types of work emerge that we can't even imagine today. The key is education and adaptation.",
                "speaker": "guest"
            },
            {
                "start": 42.5,
                "end": 45.8,
                "text": "What advice would you give to young people entering the workforce?",
                "speaker": "host"
            },
            {
                "start": 46.2,
                "end": 55.9,
                "text": "Focus on developing uniquely human skills - creativity, emotional intelligence, critical thinking. These will remain valuable regardless of how AI evolves. Also, stay curious and keep learning.",
                "speaker": "guest"
            },
            {
                "start": 56.3,
                "end": 60.0,
                "text": "That's excellent advice. Thank you for sharing your insights with us today.",
                "speaker": "host"
            }
        ]
    }


def run_highlights_extraction_example():
    """Run a complete example of highlights extraction"""
    print("🎬 Video Highlights Extraction Jobs - Example")
    print("=" * 50)
    
    # Create sample data
    transcription_data = create_sample_transcription_data()
    video_path = "sample_video.mp4"  # This would be a real video file
    keywords = ["AI", "artificial intelligence", "future", "technology", "work"]
    
    print(f"📝 Sample transcription with {len(transcription_data['segments'])} segments")
    print(f"🔍 Keywords: {', '.join(keywords)}")
    print(f"⏱️  Target length: 30 seconds")
    print()
    
    # Initialize job manager
    print("🚀 Initializing job manager...")
    manager = HighlightsJobManager()
    
    try:
        # Submit job
        print("📋 Submitting highlights extraction job...")
        job_id = manager.submit_job(
            transcription_data=transcription_data,
            video_path=video_path,
            keywords=keywords,
            target_length=30,
            extract_qa_pairs=True,
            extract_segments=True,
            params={
                'output_format': 'mp4',
                'resolution': '720x1280'
            }
        )
        
        print(f"✅ Job submitted with ID: {job_id}")
        print()
        
        # Monitor job progress
        print("⏳ Monitoring job progress...")
        last_progress = -1
        
        while True:
            job = manager.get_job(job_id)
            if not job:
                print("❌ Job not found!")
                break
            
            # Show progress updates
            if job.progress != last_progress:
                print(f"📈 Progress: {job.progress:.1f}% - {job.current_step}")
                last_progress = job.progress
            
            # Check if job is finished
            if job.is_finished:
                print()
                if job.status.value == 'completed':
                    print("🎉 Job completed successfully!")
                    display_results(job.result)
                elif job.status.value == 'failed':
                    print(f"❌ Job failed: {job.error_message}")
                else:
                    print(f"⚠️  Job {job.status.value}")
                break
            
            time.sleep(1)  # Check every second
        
        # List all jobs
        print("\n📋 All jobs:")
        jobs = manager.list_jobs()
        for job_data in jobs:
            job_id_short = job_data['job_id'][:8] + "..."
            status = job_data['status']
            progress = job_data['progress']
            print(f"   🆔 {job_id_short} | 📊 {status} | 📈 {progress:.1f}%")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        print("\n🔄 Shutting down job manager...")
        manager.shutdown()
        print("✅ Done!")


def display_results(result):
    """Display job results in a formatted way"""
    if not result:
        print("❌ No results available")
        return
    
    print(f"📊 Extraction Results:")
    print(f"   ⏱️  Execution time: {result.execution_time:.2f}s")
    print(f"   🎯 Highlights: {len(result.highlights)}")
    print(f"   ❓ Q&A pairs: {len(result.qa_pairs)}")
    print(f"   📹 Segments: {len(result.segments)}")
    print(f"   ⏱️  Total duration: {result.total_duration:.1f}s")
    print()
    
    # Show highlights
    if result.highlights:
        print("🎯 Highlights:")
        for i, highlight in enumerate(result.highlights[:3], 1):  # Show first 3
            start = highlight.get('start_time', 0)
            end = highlight.get('end_time', 0)
            duration = highlight.get('duration', 0)
            score = highlight.get('composite_score', 0)
            print(f"   {i}. {start:.1f}s - {end:.1f}s ({duration:.1f}s) - Score: {score:.3f}")
        
        if len(result.highlights) > 3:
            print(f"   ... and {len(result.highlights) - 3} more")
        print()
    
    # Show Q&A pairs
    if result.qa_pairs:
        print("❓ Q&A Pairs:")
        for i, qa in enumerate(result.qa_pairs[:2], 1):  # Show first 2
            question = qa.get('question_text', '')[:60] + "..." if len(qa.get('question_text', '')) > 60 else qa.get('question_text', '')
            duration = qa.get('duration', 0)
            quality = qa.get('quality_score', 0)
            print(f"   {i}. Q: {question}")
            print(f"      Duration: {duration:.1f}s, Quality: {quality:.3f}")
        
        if len(result.qa_pairs) > 2:
            print(f"   ... and {len(result.qa_pairs) - 2} more")
        print()
    
    # Show output files
    if result.output_files:
        print("📁 Output Files:")
        for file_type, file_path in result.output_files.items():
            print(f"   {file_type}: {file_path}")


def demonstrate_api_usage():
    """Demonstrate direct API usage without job manager"""
    print("\n🔧 Direct API Usage Example")
    print("=" * 30)
    
    from highlights_jobs.core.extractor import IntelligentHighlightsExtractor
    from highlights_jobs.core.qa_extractor import QAHighlightsExtractor
    
    # Create sample data
    transcription_data = create_sample_transcription_data()
    video_path = "sample_video.mp4"
    keywords = ["AI", "technology"]
    
    print("📝 Using core extractors directly...")
    
    try:
        # Use intelligent highlights extractor
        extractor = IntelligentHighlightsExtractor()
        highlights_result = extractor.extract(
            transcription_data=transcription_data,
            video_path=video_path,
            keywords=keywords,
            target_length=20
        )
        
        print(f"✅ Extracted {len(highlights_result['highlights'])} highlights")
        
        # Use Q&A extractor
        qa_extractor = QAHighlightsExtractor()
        qa_result = qa_extractor.extract(
            highlights_data=highlights_result,
            transcription_data=transcription_data,
            keywords=keywords
        )
        
        print(f"✅ Extracted {len(qa_result['qa_pairs'])} Q&A pairs")
        
    except Exception as e:
        print(f"❌ Error in direct API usage: {e}")


if __name__ == '__main__':
    print("🎬 Video Highlights Extraction Jobs - Examples")
    print("=" * 50)
    print()
    
    # Run the main example
    run_highlights_extraction_example()
    
    # Demonstrate direct API usage
    demonstrate_api_usage()
    
    print("\n🎉 Examples completed!")
    print("\n💡 To use the CLI:")
    print("   python -m highlights_jobs.cli submit video.mp4 transcript.json --keywords ai technology")
    print("\n💡 To use programmatically:")
    print("   from highlights_jobs import HighlightsJobManager")
    print("   manager = HighlightsJobManager()")
    print("   job_id = manager.submit_job(...)")
