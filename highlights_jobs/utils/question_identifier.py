#!/usr/bin/env python3
"""
Question Segment Identifier for Video Highlights

This utility identifies all question-based segments throughout the entire video
and provides prioritization logic for question-answer pairs in highlight generation.
"""

import logging
from typing import List, Dict, Any, Tuple, Optional

logger = logging.getLogger(__name__)


class QuestionSegmentIdentifier:
    """
    Identifies and analyzes question-based segments in video transcripts
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # Question quality thresholds
        self.min_question_length = 10
        self.max_question_length = 200
        self.min_answer_length = 15
        self.max_answer_gap = 3  # Maximum segments to look ahead for answer

    def identify_all_question_segments(self, segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Identify all question-based segments throughout the entire video

        Args:
            segments: List of transcript segments

        Returns:
            List of question segment data with metadata
        """
        question_segments = []

        for i, segment in enumerate(segments):
            text = segment.get('text', '').strip()
            speaker = segment.get('speaker', 'unknown')

            if self._is_high_quality_question(text):
                # Find corresponding answer
                answer_data = self._find_best_answer(segments, i, speaker)

                if answer_data:
                    question_segment = {
                        'question_idx': i,
                        'question_text': text,
                        'question_speaker': speaker,
                        'question_start': segment.get('start', 0),
                        'question_end': segment.get('end', 0),
                        'answer_idx': answer_data['answer_idx'],
                        'answer_text': answer_data['answer_text'],
                        'answer_speaker': answer_data['answer_speaker'],
                        'answer_start': answer_data['answer_start'],
                        'answer_end': answer_data['answer_end'],
                        'qa_span_start': segment.get('start', 0),
                        'qa_span_end': answer_data['answer_end'],
                        'qa_span_duration': answer_data['answer_end'] - segment.get('start', 0),
                        'quality_score': self._calculate_qa_quality_score(text, answer_data),
                        'engagement_score': self._calculate_engagement_score(text, answer_data),
                        'priority_score': 0.0  # Will be calculated later
                    }
                    question_segments.append(question_segment)

        # Calculate priority scores based on relative quality
        question_segments = self._calculate_priority_scores(question_segments)

        # Sort by priority score (highest first)
        question_segments.sort(key=lambda x: x['priority_score'], reverse=True)

        self.logger.info(f"Identified {len(question_segments)} question-based segments")

        return question_segments

    def _is_high_quality_question(self, text: str) -> bool:
        """
        Determine if text represents a high-quality question

        Args:
            text: Text to analyze

        Returns:
            True if high-quality question
        """
        if not text or len(text) < self.min_question_length or len(text) > self.max_question_length:
            return False

        text_lower = text.lower().strip()

        # Direct question markers
        if text.endswith('?'):
            return True

        # High-value question starters
        high_value_starters = [
            'what do you think', 'how do you', 'why do you', 'what would you',
            'can you explain', 'could you tell me', 'would you say',
            'what are your thoughts', 'how would you describe', 'what makes',
            'how does', 'why is', 'what happens when', 'how can we'
        ]

        if any(text_lower.startswith(starter) for starter in high_value_starters):
            return True

        # Standard question words with sufficient context
        question_words = ['what', 'how', 'why', 'when', 'where', 'who', 'which']
        if any(text_lower.startswith(word) and len(text) > 20 for word in question_words):
            return True

        # Indirect questions that create engagement
        engagement_patterns = [
            'tell me about', 'explain how', 'describe what', 'walk me through',
            'help me understand', 'i\'m curious about', 'i want to know'
        ]

        if any(pattern in text_lower for pattern in engagement_patterns):
            return True

        return False

    def _find_best_answer(self, segments: List[Dict[str, Any]],
                         question_idx: int, question_speaker: str) -> Optional[Dict[str, Any]]:
        """
        Find the best answer for a question (works without speaker diarization)

        Args:
            segments: All transcript segments
            question_idx: Index of question segment
            question_speaker: Speaker who asked the question (may be 'unknown')

        Returns:
            Answer data or None if no suitable answer found
        """
        best_answer = None
        best_score = 0.0

        # Look ahead for answers
        for i in range(1, min(self.max_answer_gap + 1, len(segments) - question_idx)):
            answer_idx = question_idx + i
            answer_segment = segments[answer_idx]
            answer_speaker = answer_segment.get('speaker', 'unknown')
            answer_text = answer_segment.get('text', '').strip()

            # Skip if too short
            if len(answer_text) < self.min_answer_length:
                continue

            # If we have speaker info, skip if same speaker
            if question_speaker != 'unknown' and answer_speaker != 'unknown' and answer_speaker == question_speaker:
                continue

            # Calculate answer quality score
            score = self._score_answer_quality(answer_text, answer_speaker, i == 1, question_speaker)

            if score > best_score:
                best_score = score
                best_answer = {
                    'answer_idx': answer_idx,
                    'answer_text': answer_text,
                    'answer_speaker': answer_speaker,
                    'answer_start': answer_segment.get('start', 0),
                    'answer_end': answer_segment.get('end', 0),
                    'answer_score': score
                }

        return best_answer if best_score > 0.2 else None  # Lower threshold when no speaker info

    def _score_answer_quality(self, answer_text: str, answer_speaker: str, is_immediate: bool, question_speaker: str = 'unknown') -> float:
        """
        Score the quality of an answer (enhanced for no-speaker-diarization scenarios)

        Args:
            answer_text: The answer text
            answer_speaker: Speaker providing the answer
            is_immediate: Whether this is the immediate next segment
            question_speaker: Speaker who asked the question

        Returns:
            Quality score (0-1)
        """
        score = 0.0
        answer_lower = answer_text.lower()

        # Base score - higher when we don't have speaker info
        if answer_speaker != 'unknown' and question_speaker != 'unknown' and answer_speaker != question_speaker:
            score += 0.4  # Strong bonus for different speakers
        elif answer_speaker == 'unknown' or question_speaker == 'unknown':
            score += 0.2  # Moderate base score when speaker info missing

        # Length-based scoring (more important without speaker info)
        if len(answer_text) > 80:
            score += 0.4
        elif len(answer_text) > 50:
            score += 0.3
        elif len(answer_text) > 30:
            score += 0.2
        elif len(answer_text) > 15:
            score += 0.1

        # Response quality indicators (more important without speaker info)
        quality_indicators = [
            'actually', 'basically', 'essentially', 'fundamentally',
            'i think', 'i believe', 'in my opinion', 'from my perspective',
            'the key is', 'the important thing', 'what matters is',
            'well', 'so', 'you know', 'i mean', 'the thing is'
        ]

        if any(indicator in answer_lower for indicator in quality_indicators):
            score += 0.25

        # Look for answer-like patterns when no speaker info
        answer_patterns = [
            'that\'s', 'it\'s', 'there\'s', 'you see', 'the answer is',
            'what happens is', 'the reason is', 'because', 'since'
        ]

        if any(pattern in answer_lower for pattern in answer_patterns):
            score += 0.15

        # Immediate response bonus
        if is_immediate:
            score += 0.15

        # Avoid very generic responses
        generic_responses = ['yes', 'no', 'okay', 'sure', 'right', 'exactly', 'mm-hmm', 'uh-huh']
        if answer_text.lower().strip() in generic_responses:
            score *= 0.2

        # Avoid questions as answers
        if answer_text.strip().endswith('?'):
            score *= 0.5

        return min(1.0, score)

    def _calculate_qa_quality_score(self, question_text: str, answer_data: Dict[str, Any]) -> float:
        """
        Calculate overall Q&A quality score

        Args:
            question_text: The question text
            answer_data: Answer information

        Returns:
            Quality score (0-1)
        """
        question_score = self._score_question_quality(question_text)
        answer_score = answer_data.get('answer_score', 0.0)

        # Weighted combination: 40% question quality, 60% answer quality
        return 0.4 * question_score + 0.6 * answer_score

    def _score_question_quality(self, question_text: str) -> float:
        """
        Score question quality based on engagement potential

        Args:
            question_text: The question text

        Returns:
            Quality score (0-1)
        """
        score = 0.5  # Base score
        question_lower = question_text.lower()

        # High-engagement question types
        if any(word in question_lower for word in ['future', 'think', 'believe', 'predict']):
            score += 0.2

        # Thought-provoking questions
        if any(phrase in question_lower for phrase in ['what if', 'imagine', 'suppose']):
            score += 0.2

        # Technical/educational value
        if any(word in question_lower for word in ['how does', 'why does', 'what makes']):
            score += 0.1

        # Optimal length
        if 20 <= len(question_text) <= 100:
            score += 0.1

        return min(1.0, score)

    def _calculate_engagement_score(self, question_text: str, answer_data: Dict[str, Any]) -> float:
        """
        Calculate engagement potential score

        Args:
            question_text: The question text
            answer_data: Answer information

        Returns:
            Engagement score (0-1)
        """
        score = 0.0
        combined_text = (question_text + ' ' + answer_data.get('answer_text', '')).lower()

        # Trending topics boost
        trending_keywords = [
            'ai', 'artificial intelligence', 'technology', 'future', 'robot',
            'automation', 'digital', 'innovation', 'breakthrough', 'revolution'
        ]

        keyword_count = sum(1 for keyword in trending_keywords if keyword in combined_text)
        score += min(0.3, keyword_count * 0.1)

        # Question type engagement
        if any(word in question_text.lower() for word in ['what', 'how', 'why']):
            score += 0.2

        # Duration appropriateness for social media
        duration = answer_data.get('answer_end', 0) - answer_data.get('answer_start', 0)
        if 10 <= duration <= 30:  # Sweet spot for short-form content
            score += 0.3
        elif 5 <= duration <= 45:
            score += 0.2

        # Conversational flow
        if len(answer_data.get('answer_text', '')) > 30:
            score += 0.2

        return min(1.0, score)

    def _calculate_priority_scores(self, question_segments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Calculate priority scores for question segments

        Args:
            question_segments: List of question segment data

        Returns:
            Updated question segments with priority scores
        """
        if not question_segments:
            return question_segments

        # Normalize scores
        max_quality = max(qs['quality_score'] for qs in question_segments)
        max_engagement = max(qs['engagement_score'] for qs in question_segments)

        for qs in question_segments:
            normalized_quality = qs['quality_score'] / max_quality if max_quality > 0 else 0
            normalized_engagement = qs['engagement_score'] / max_engagement if max_engagement > 0 else 0

            # Priority = 60% quality + 40% engagement
            qs['priority_score'] = 0.6 * normalized_quality + 0.4 * normalized_engagement

        return question_segments


# Global instance for easy access
question_identifier = QuestionSegmentIdentifier()
