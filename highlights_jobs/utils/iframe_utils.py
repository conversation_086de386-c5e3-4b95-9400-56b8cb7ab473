#!/usr/bin/env python3
"""
I-Frame extraction utilities for highlights extraction jobs
"""

import os
import logging
import subprocess
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class IFrameExtractor:
    """
    Utility for extracting I-frame timestamps and snapping highlights to frame boundaries
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    def extract_iframe_timestamps(self, video_path: str) -> List[float]:
        """
        Extract I-frame timestamps from video using ffprobe
        
        Args:
            video_path: Path to the video file
            
        Returns:
            List of I-frame timestamps in seconds
        """
        if not os.path.exists(video_path):
            self.logger.warning(f"Video file not found: {video_path}")
            return []

        try:
            # Use ffprobe to extract I-frame timestamps
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-select_streams', 'v:0',
                '-show_entries', 'frame=pkt_pts_time',
                '-of', 'csv=p=0',
                '-skip_frame', 'nokey',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                self.logger.warning(f"ffprobe failed: {result.stderr}")
                return []

            # Parse timestamps
            timestamps = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        timestamp = float(line.strip())
                        timestamps.append(timestamp)
                    except ValueError:
                        continue

            self.logger.info(f"Extracted {len(timestamps)} I-frame timestamps")
            return sorted(timestamps)

        except subprocess.TimeoutExpired:
            self.logger.warning("ffprobe timeout while extracting I-frames")
            return []
        except Exception as e:
            self.logger.warning(f"Error extracting I-frames: {e}")
            return []

    def snap_highlights_to_iframes(self, highlights: List[Dict[str, Any]], 
                                  iframe_timestamps: List[float]) -> List[Dict[str, Any]]:
        """
        Snap highlight boundaries to nearest I-frame timestamps
        
        Args:
            highlights: List of highlight segments with start_time and end_time
            iframe_timestamps: List of I-frame timestamps
            
        Returns:
            List of highlights with snapped boundaries
        """
        if not iframe_timestamps:
            self.logger.warning("No I-frame timestamps available, returning original highlights")
            return highlights

        snapped_highlights = []
        
        for highlight in highlights:
            start_time = highlight.get('start_time', 0)
            end_time = highlight.get('end_time', 0)
            
            # Find nearest I-frames
            snapped_start = self._find_nearest_iframe(start_time, iframe_timestamps, prefer_earlier=True)
            snapped_end = self._find_nearest_iframe(end_time, iframe_timestamps, prefer_earlier=False)
            
            # Ensure end is after start
            if snapped_end <= snapped_start:
                # Find next I-frame after start
                next_iframe_idx = self._find_iframe_index(snapped_start, iframe_timestamps) + 1
                if next_iframe_idx < len(iframe_timestamps):
                    snapped_end = iframe_timestamps[next_iframe_idx]
                else:
                    snapped_end = snapped_start + (end_time - start_time)  # Fallback to original duration

            # Create snapped highlight
            snapped_highlight = highlight.copy()
            snapped_highlight.update({
                'start_time': snapped_start,
                'end_time': snapped_end,
                'duration': snapped_end - snapped_start,
                'original_start': start_time,
                'original_end': end_time,
                'snapped_to_iframes': True
            })
            
            snapped_highlights.append(snapped_highlight)

        self.logger.info(f"Snapped {len(snapped_highlights)} highlights to I-frame boundaries")
        return snapped_highlights

    def _find_nearest_iframe(self, timestamp: float, iframe_timestamps: List[float], 
                           prefer_earlier: bool = True) -> float:
        """
        Find the nearest I-frame timestamp to a given timestamp
        
        Args:
            timestamp: Target timestamp
            iframe_timestamps: List of I-frame timestamps
            prefer_earlier: If True, prefer earlier I-frame when equidistant
            
        Returns:
            Nearest I-frame timestamp
        """
        if not iframe_timestamps:
            return timestamp

        # Find the closest I-frame
        min_distance = float('inf')
        nearest_iframe = iframe_timestamps[0]

        for iframe_ts in iframe_timestamps:
            distance = abs(iframe_ts - timestamp)
            
            if distance < min_distance:
                min_distance = distance
                nearest_iframe = iframe_ts
            elif distance == min_distance:
                # Handle equidistant case
                if prefer_earlier and iframe_ts < timestamp:
                    nearest_iframe = iframe_ts
                elif not prefer_earlier and iframe_ts > timestamp:
                    nearest_iframe = iframe_ts

        return nearest_iframe

    def _find_iframe_index(self, timestamp: float, iframe_timestamps: List[float]) -> int:
        """
        Find the index of the I-frame timestamp in the list
        
        Args:
            timestamp: I-frame timestamp to find
            iframe_timestamps: List of I-frame timestamps
            
        Returns:
            Index of the timestamp, or -1 if not found
        """
        try:
            return iframe_timestamps.index(timestamp)
        except ValueError:
            return -1

    def generate_ffmpeg_concat_file(self, highlights: List[Dict[str, Any]], 
                                   video_path: str, output_path: str) -> None:
        """
        Generate FFmpeg concat demuxer file for highlights
        
        Args:
            highlights: List of highlight segments
            video_path: Path to source video
            output_path: Path for the concat file
        """
        try:
            with open(output_path, 'w') as f:
                f.write("ffconcat version 1.0\n")
                
                for i, highlight in enumerate(highlights):
                    start_time = highlight.get('start_time', 0)
                    end_time = highlight.get('end_time', 0)
                    duration = end_time - start_time
                    
                    f.write(f"file '{os.path.abspath(video_path)}'\n")
                    f.write(f"inpoint {start_time:.3f}\n")
                    f.write(f"outpoint {end_time:.3f}\n")
                    
                    if i < len(highlights) - 1:
                        f.write("\n")

            self.logger.info(f"Generated FFmpeg concat file: {output_path}")

        except Exception as e:
            self.logger.error(f"Failed to generate concat file: {e}")

    def extract_highlight_clips(self, highlights: List[Dict[str, Any]], 
                               video_path: str, output_dir: str) -> List[str]:
        """
        Extract individual highlight clips using FFmpeg
        
        Args:
            highlights: List of highlight segments
            video_path: Path to source video
            output_dir: Directory for output clips
            
        Returns:
            List of paths to extracted clips
        """
        output_paths = []
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        for i, highlight in enumerate(highlights):
            start_time = highlight.get('start_time', 0)
            end_time = highlight.get('end_time', 0)
            duration = end_time - start_time

            if duration <= 0:
                self.logger.warning(f"Invalid duration for highlight {i}: {duration}")
                continue

            # Generate output filename
            output_filename = f"highlight_{i:03d}_{start_time:.1f}s-{end_time:.1f}s.mp4"
            output_path = output_dir / output_filename

            try:
                # Extract clip using FFmpeg
                cmd = [
                    'ffmpeg',
                    '-i', video_path,
                    '-ss', str(start_time),
                    '-t', str(duration),
                    '-c', 'copy',  # Copy streams without re-encoding for speed
                    '-avoid_negative_ts', 'make_zero',
                    '-y',  # Overwrite output file
                    str(output_path)
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    output_paths.append(str(output_path))
                    self.logger.debug(f"Extracted highlight clip: {output_path}")
                else:
                    self.logger.warning(f"Failed to extract clip {i}: {result.stderr}")

            except subprocess.TimeoutExpired:
                self.logger.warning(f"Timeout extracting clip {i}")
            except Exception as e:
                self.logger.warning(f"Error extracting clip {i}: {e}")

        self.logger.info(f"Extracted {len(output_paths)} highlight clips")
        return output_paths


# Global instance for easy access
iframe_extractor = IFrameExtractor()
