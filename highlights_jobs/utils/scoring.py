#!/usr/bin/env python3
"""
Highlights Scoring Utility for Intelligent Video Highlights

This module implements the multi-component scoring system for video highlights:
- Q&A Detection (35%)
- Keyword Density using KeyBERT (25%)
- Emotion Intensity via sentiment analysis (25%)
- Novelty scoring (15%)
"""

import logging
from typing import List, Dict, Any, Optional
import warnings

# Optional numpy import
try:
    import numpy as np
    NUMPY_AVAILABLE_LOCAL = True
except ImportError:
    NUMPY_AVAILABLE_LOCAL = False

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

# Try to import required libraries with fallbacks
try:
    from keybert import KeyBERT
    KEYBERT_AVAILABLE = True
except ImportError:
    KeyBERT = None
    KEYBERT_AVAILABLE = False
    logger.warning("KeyBERT not available. Keyword density scoring will be disabled.")

try:
    from transformers.pipelines import pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    pipeline = None
    TRANSFORMERS_AVAILABLE = False
    logger.warning("Transformers not available. Sentiment analysis will be disabled.")

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SentenceTransformer = None
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logger.warning("SentenceTransformers not available. Novelty scoring will be disabled.")

try:
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    cosine_similarity = None
    SKLEARN_AVAILABLE = False
    logger.warning("Scikit-learn not available. Similarity calculations will be disabled.")

# Import settings from highlights jobs config
from ..config.settings import SCORING_WEIGHTS, OPENAI_ENABLED, OPENAI_MODEL, OPENAI_MAX_REQUESTS


class HighlightsScorer:
    """
    Advanced scoring system for video highlights extraction
    """

    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2",
                 openai_enabled: bool = OPENAI_ENABLED, openai_model: str = OPENAI_MODEL):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.embedding_model_name = embedding_model

        # OpenAI configuration - disabled by default for cost optimization
        self.openai_enabled = False  # Explicitly disabled for cost-effective processing
        self.openai_model = openai_model
        self.openai_client = None
        self.openai_request_count = 0
        self.max_openai_requests_per_session = OPENAI_MAX_REQUESTS
        self.openai_cache = {}

        # Initialize models
        self._init_models()

        # Scoring weights from config
        self.weights = SCORING_WEIGHTS

        # Quality filter thresholds
        self.quality_thresholds = {
            'min_loudness_lufs': -40.0,
            'min_asr_confidence': 0.85,
            'max_silence_gap': 2.0
        }

        self.logger.info(f"HighlightsScorer initialized with weights: {self.weights}")

    def _init_models(self):
        """Initialize ML models with fallbacks"""
        # Initialize KeyBERT for keyword extraction
        self.keybert_model = None
        if KEYBERT_AVAILABLE and KeyBERT is not None:
            try:
                self.keybert_model = KeyBERT()
                self.logger.info("KeyBERT model loaded successfully")
            except Exception as e:
                self.logger.warning(f"Failed to load KeyBERT: {e}")

        # Initialize sentiment analysis model
        self.sentiment_model = None
        if TRANSFORMERS_AVAILABLE and pipeline is not None:
            try:
                self.sentiment_model = pipeline(
                    "sentiment-analysis",
                    model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                    return_all_scores=True
                )
                self.logger.info("Sentiment analysis model loaded successfully")
            except Exception as e:
                self.logger.warning(f"Failed to load sentiment model: {e}")

        # Initialize embedding model for novelty scoring
        self.embedding_model = None
        if SENTENCE_TRANSFORMERS_AVAILABLE and SentenceTransformer is not None:
            try:
                self.embedding_model = SentenceTransformer(self.embedding_model_name)
                self.logger.info(f"Embedding model {self.embedding_model_name} loaded successfully")
            except Exception as e:
                self.logger.warning(f"Failed to load embedding model: {e}")

    def detect_qa_patterns(self, segments: List[Dict[str, Any]]) -> Dict[int, float]:
        """
        Detect question-answer patterns in transcript segments

        Args:
            segments: List of transcript segments with text and speaker info

        Returns:
            Dictionary mapping segment indices to Q&A scores (0-1)
        """
        qa_scores = {}

        for i, segment in enumerate(segments):
            text = segment.get('text', '').strip()
            speaker = segment.get('speaker', 'unknown')

            if not text:
                qa_scores[i] = 0.0
                continue

            # Check if this segment contains a question
            is_question = self._is_question(text)
            qa_score = 0.0

            if is_question:
                # Look for answer in next 1-3 segments
                answer_score = self._find_answer_for_question(segments, i, speaker)
                qa_score = answer_score

                if qa_score > 0.0:
                    self.logger.debug(f"Q&A pattern detected at segment {i}: {text[:50]}... (score: {qa_score:.2f})")

            qa_scores[i] = qa_score

        return qa_scores

    def _is_question(self, text: str) -> bool:
        """Check if text contains a question"""
        text = text.strip()

        # Direct question markers
        if text.endswith('?'):
            return True

        # Question word patterns
        question_words = [
            'what', 'how', 'why', 'when', 'where', 'who', 'which', 'whose',
            'can you', 'could you', 'would you', 'will you', 'do you', 'did you',
            'are you', 'is it', 'was it', 'have you', 'has it'
        ]

        text_lower = text.lower()
        return any(text_lower.startswith(word) for word in question_words)

    def _find_answer_for_question(self, segments: List[Dict[str, Any]],
                                 question_idx: int, question_speaker: str) -> float:
        """
        Find and score potential answers following a question

        Args:
            segments: All transcript segments
            question_idx: Index of the question segment
            question_speaker: Speaker who asked the question

        Returns:
            Score indicating quality of Q&A pattern (0-1)
        """
        max_lookahead = min(3, len(segments) - question_idx - 1)
        best_score = 0.0

        for i in range(1, max_lookahead + 1):
            if question_idx + i >= len(segments):
                break

            answer_segment = segments[question_idx + i]
            answer_text = answer_segment.get('text', '').strip()
            answer_speaker = answer_segment.get('speaker', 'unknown')

            if not answer_text:
                continue

            # Score based on answer quality indicators
            score = 0.0

            # Different speaker responding = good (when we have speaker info)
            if (answer_speaker != 'unknown' and question_speaker != 'unknown' and
                answer_speaker != question_speaker):
                score += 0.4
            elif answer_speaker == 'unknown' or question_speaker == 'unknown':
                # Base score when no speaker info available
                score += 0.2

            # Answer length indicates substantive response
            if len(answer_text) > 50:
                score += 0.4
            elif len(answer_text) > 30:
                score += 0.3
            elif len(answer_text) > 15:
                score += 0.2

            # Answer starts with typical response patterns
            answer_lower = answer_text.lower()
            response_starters = [
                'yes', 'no', 'well', 'so', 'actually', 'i think', 'i believe',
                'absolutely', 'definitely', 'certainly', 'of course', 'sure',
                'that\'s', 'it\'s', 'you know', 'basically', 'essentially'
            ]

            if any(answer_lower.startswith(starter) for starter in response_starters):
                score += 0.2

            # Look for answer-like content patterns
            answer_patterns = [
                'because', 'since', 'the reason', 'what happens', 'the answer',
                'you see', 'the thing is', 'it depends', 'in my view'
            ]

            if any(pattern in answer_lower for pattern in answer_patterns):
                score += 0.15

            # Bonus for immediate response (next segment)
            if i == 1:
                score += 0.1

            # Penalty for questions as answers
            if answer_text.strip().endswith('?'):
                score *= 0.5

            best_score = max(best_score, min(1.0, score))

        return best_score

    def calculate_keyword_density(self, text: str, keywords: List[str]) -> float:
        """
        Calculate keyword density using KeyBERT and direct matching

        Args:
            text: Text to analyze
            keywords: Target keywords

        Returns:
            Keyword density score (0-1)
        """
        if not text.strip() or not keywords:
            return 0.0

        text_lower = text.lower()
        direct_matches = 0

        # Direct keyword matching
        for keyword in keywords:
            if keyword.lower() in text_lower:
                direct_matches += 1

        # Base score from direct matches
        direct_score = min(1.0, direct_matches / len(keywords))

        # Enhanced scoring with KeyBERT if available
        keybert_score = 0.0
        if self.keybert_model:
            try:
                # Extract keywords and check similarity to target keywords
                extracted_keywords = self.keybert_model.extract_keywords(
                    text, keyphrase_ngram_range=(1, 2), stop_words='english'
                )

                if extracted_keywords:
                    # Calculate semantic similarity
                    extracted_terms = [kw[0] if isinstance(kw, tuple) else str(kw) for kw in extracted_keywords]
                    semantic_matches = 0

                    for target_kw in keywords:
                        for extracted_kw in extracted_terms:
                            # Simple semantic matching
                            if (target_kw.lower() in extracted_kw.lower() or
                                extracted_kw.lower() in target_kw.lower()):
                                semantic_matches += 1
                                break

                    keybert_score = min(1.0, semantic_matches / len(keywords))

            except Exception as e:
                self.logger.warning(f"KeyBERT keyword extraction failed: {e}")

        # Combine direct and semantic scores
        final_score = max(direct_score, keybert_score * 0.8)  # Slight preference for direct matches
        return min(1.0, final_score)

    def calculate_emotion_intensity(self, text: str, audio_features: Optional[Dict] = None) -> float:
        """
        Calculate emotion intensity using sentiment analysis

        Args:
            text: Text to analyze
            audio_features: Optional audio features (not used in cost-effective version)

        Returns:
            Emotion intensity score (0-1)
        """
        if not text.strip():
            return 0.0

        sentiment_score = 0.0

        # Text-based sentiment analysis
        if self.sentiment_model:
            try:
                results = self.sentiment_model(text)
                if results and isinstance(results, list) and len(results) > 0:
                    # Get the highest confidence score from the first result
                    first_result = results[0]
                    if isinstance(first_result, list):
                        max_score = max(result.get('score', 0) for result in first_result if isinstance(result, dict))
                        sentiment_score = max_score
                    elif isinstance(first_result, dict):
                        sentiment_score = first_result.get('score', 0)
            except Exception as e:
                self.logger.warning(f"Error in sentiment analysis: {e}")

        # Fallback: simple emotion word detection
        if sentiment_score == 0.0:
            emotion_words = [
                'amazing', 'incredible', 'fantastic', 'awesome', 'brilliant',
                'terrible', 'horrible', 'awful', 'devastating', 'shocking',
                'exciting', 'thrilling', 'surprising', 'unexpected', 'dramatic'
            ]

            text_lower = text.lower()
            emotion_count = sum(1 for word in emotion_words if word in text_lower)
            sentiment_score = min(1.0, emotion_count * 0.3)

        return sentiment_score

    def calculate_novelty_score(self, current_text: str, context_texts: List[str]) -> float:
        """
        Calculate novelty score based on semantic similarity to context

        Args:
            current_text: Text to score for novelty
            context_texts: Previous texts for context comparison

        Returns:
            Novelty score (0-1, higher = more novel)
        """
        if not current_text.strip():
            return 0.0

        if not self.embedding_model or not SKLEARN_AVAILABLE:
            # Fallback: simple word overlap analysis
            return self._calculate_simple_novelty(current_text, context_texts)

        try:
            # Limit context size to prevent memory issues
            limited_context = context_texts[-10:] if len(context_texts) > 10 else context_texts

            if not limited_context:
                return 0.5

            # Generate embeddings
            current_embedding = self.embedding_model.encode([current_text])
            context_embeddings = self.embedding_model.encode(limited_context)

            # Calculate mean similarity to context
            if SKLEARN_AVAILABLE and cosine_similarity is not None and NUMPY_AVAILABLE_LOCAL:
                similarities = cosine_similarity(current_embedding, context_embeddings)[0]
                mean_similarity = np.mean(similarities)
            else:
                # Fallback to simple calculation
                return self._calculate_simple_novelty(current_text, context_texts)

            # Novelty is inverse of similarity
            novelty_score = 1.0 - mean_similarity
            return max(0.0, min(1.0, novelty_score))

        except Exception as e:
            self.logger.warning(f"Error calculating novelty score: {e}")
            return 0.5

    def _calculate_simple_novelty(self, current_text: str, context_texts: List[str]) -> float:
        """Simple novelty calculation based on word overlap"""
        if not context_texts:
            return 0.8  # High novelty if no context

        current_words = set(current_text.lower().split())
        context_words = set()

        for context_text in context_texts[-5:]:  # Last 5 contexts
            context_words.update(context_text.lower().split())

        if not current_words or not context_words:
            return 0.5

        # Calculate Jaccard similarity
        intersection = len(current_words.intersection(context_words))
        union = len(current_words.union(context_words))

        similarity = intersection / union if union > 0 else 0
        novelty = 1.0 - similarity

        return max(0.0, min(1.0, novelty))

    def calculate_composite_score(self, qa_score: float, keyword_density: float,
                                emotion_intensity: float, novelty: float) -> float:
        """
        Calculate composite score using weighted combination

        Args:
            qa_score: Q&A detection score (0-1)
            keyword_density: Keyword density score (0-1)
            emotion_intensity: Emotion intensity score (0-1)
            novelty: Novelty score (0-1)

        Returns:
            Composite score (0-1)
        """
        composite_score = (
            self.weights['qa_score'] * qa_score +
            self.weights['keyword_density'] * keyword_density +
            self.weights['emotion_intensity'] * emotion_intensity +
            self.weights['novelty'] * novelty
        )

        return min(1.0, max(0.0, composite_score))


# Create a global instance for easy access
highlights_scorer = HighlightsScorer()
