#!/usr/bin/env python3
"""
Command Line Interface for Video Highlights Extraction Jobs

This CLI provides easy access to the highlights extraction system for standalone use.
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from highlights_jobs import HighlightsJobManager
from highlights_jobs.config.settings import LOG_LEVEL, HIGHLIGHTS_OUTPUT_DIR


def setup_logging(verbose: bool = False):
    """Setup logging for CLI"""
    level = logging.DEBUG if verbose else getattr(logging, LOG_LEVEL)
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('highlights_jobs_cli.log')
        ]
    )


def load_transcription_data(transcription_path: str) -> Dict[str, Any]:
    """Load transcription data from file"""
    if not os.path.exists(transcription_path):
        raise FileNotFoundError(f"Transcription file not found: {transcription_path}")
    
    with open(transcription_path, 'r') as f:
        if transcription_path.endswith('.json'):
            return json.load(f)
        else:
            raise ValueError("Only JSON transcription files are supported")


def submit_job(args) -> str:
    """Submit a new highlights extraction job"""
    print(f"🎬 Submitting highlights extraction job...")
    print(f"📹 Video: {args.video}")
    print(f"📝 Transcription: {args.transcription}")
    print(f"🔍 Keywords: {', '.join(args.keywords)}")
    print(f"⏱️  Target length: {args.target_length}s")
    
    # Load transcription data
    try:
        transcription_data = load_transcription_data(args.transcription)
        print(f"✅ Loaded transcription with {len(transcription_data.get('segments', []))} segments")
    except Exception as e:
        print(f"❌ Failed to load transcription: {e}")
        return ""
    
    # Initialize job manager
    manager = HighlightsJobManager()
    
    # Submit job
    try:
        job_id = manager.submit_job(
            transcription_data=transcription_data,
            video_path=args.video,
            keywords=args.keywords,
            target_length=args.target_length,
            extract_qa_pairs=args.extract_qa,
            extract_segments=args.extract_segments,
            params={
                'output_format': args.format,
                'resolution': args.resolution
            }
        )
        
        print(f"🚀 Job submitted successfully!")
        print(f"📋 Job ID: {job_id}")
        
        if args.wait:
            print("⏳ Waiting for job completion...")
            wait_for_job(manager, job_id)
        else:
            print(f"💡 Use 'python -m highlights_jobs.cli status {job_id}' to check progress")
        
        return job_id
        
    except Exception as e:
        print(f"❌ Failed to submit job: {e}")
        return ""
    finally:
        manager.shutdown()


def check_status(args) -> None:
    """Check job status"""
    manager = HighlightsJobManager()
    
    try:
        job = manager.get_job(args.job_id)
        if not job:
            print(f"❌ Job not found: {args.job_id}")
            return
        
        print(f"📋 Job ID: {job.job_id}")
        print(f"📊 Status: {job.status.value}")
        print(f"📈 Progress: {job.progress:.1f}%")
        print(f"🔄 Current step: {job.current_step}")
        print(f"⏰ Created: {job.created_at}")
        
        if job.started_at:
            print(f"🚀 Started: {job.started_at}")
        
        if job.completed_at:
            print(f"✅ Completed: {job.completed_at}")
            print(f"⏱️  Duration: {job.duration:.2f}s")
        
        if job.error_message:
            print(f"❌ Error: {job.error_message}")
        
        if job.result:
            result = job.result
            print(f"\n📊 Results:")
            print(f"   🎯 Highlights: {len(result.highlights)}")
            print(f"   ❓ Q&A pairs: {len(result.qa_pairs)}")
            print(f"   📹 Segments: {len(result.segments)}")
            print(f"   ⏱️  Total duration: {result.total_duration:.1f}s")
            print(f"   📁 Output files: {len(result.output_files)}")
            
            if args.verbose and result.output_files:
                print(f"\n📁 Output files:")
                for file_type, file_path in result.output_files.items():
                    print(f"   {file_type}: {file_path}")
    
    finally:
        manager.shutdown()


def list_jobs(args) -> None:
    """List all jobs"""
    manager = HighlightsJobManager()
    
    try:
        jobs = manager.list_jobs()
        
        if not jobs:
            print("📭 No jobs found")
            return
        
        print(f"📋 Found {len(jobs)} jobs:")
        print()
        
        for job_data in jobs:
            job_id = job_data['job_id'][:8] + "..."  # Truncate for display
            status = job_data['status']
            progress = job_data['progress']
            created_at = job_data['created_at']
            
            print(f"🆔 {job_id} | 📊 {status} | 📈 {progress:.1f}% | ⏰ {created_at}")
            
            if args.verbose:
                input_data = job_data.get('input_data', {})
                keywords = input_data.get('keywords', [])
                target_length = input_data.get('target_length', 0)
                print(f"   🔍 Keywords: {', '.join(keywords)}")
                print(f"   ⏱️  Target: {target_length}s")
                print()
    
    finally:
        manager.shutdown()


def wait_for_job(manager: HighlightsJobManager, job_id: str) -> None:
    """Wait for job completion with progress updates"""
    import time
    
    last_progress = -1
    
    while True:
        job = manager.get_job(job_id)
        if not job:
            print(f"❌ Job not found: {job_id}")
            break
        
        if job.progress != last_progress:
            print(f"📈 Progress: {job.progress:.1f}% - {job.current_step}")
            last_progress = job.progress
        
        if job.is_finished:
            if job.status.value == 'completed':
                print(f"✅ Job completed successfully!")
                if job.result:
                    print(f"🎯 Generated {len(job.result.highlights)} highlights")
                    print(f"❓ Generated {len(job.result.qa_pairs)} Q&A pairs")
                    print(f"📹 Generated {len(job.result.segments)} segments")
            elif job.status.value == 'failed':
                print(f"❌ Job failed: {job.error_message}")
            else:
                print(f"⚠️  Job {job.status.value}")
            break
        
        time.sleep(2)  # Check every 2 seconds


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Video Highlights Extraction Jobs CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Submit a new job
  python -m highlights_jobs.cli submit video.mp4 transcript.json --keywords ai technology --target-length 60

  # Check job status
  python -m highlights_jobs.cli status abc123def456

  # List all jobs
  python -m highlights_jobs.cli list --verbose
        """
    )
    
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Submit command
    submit_parser = subparsers.add_parser('submit', help='Submit a new highlights extraction job')
    submit_parser.add_argument('video', help='Path to video file')
    submit_parser.add_argument('transcription', help='Path to transcription JSON file')
    submit_parser.add_argument('--keywords', '-k', nargs='+', required=True, help='Keywords to search for')
    submit_parser.add_argument('--target-length', '-t', type=int, default=75, help='Target total duration in seconds')
    submit_parser.add_argument('--extract-qa', action='store_true', default=True, help='Extract Q&A pairs')
    submit_parser.add_argument('--extract-segments', action='store_true', default=True, help='Extract individual segments')
    submit_parser.add_argument('--format', default='mp4', help='Output video format')
    submit_parser.add_argument('--resolution', default='720x1280', help='Output resolution (9:16 aspect ratio)')
    submit_parser.add_argument('--wait', '-w', action='store_true', help='Wait for job completion')
    
    # Status command
    status_parser = subparsers.add_parser('status', help='Check job status')
    status_parser.add_argument('job_id', help='Job ID to check')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List all jobs')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Execute command
    if args.command == 'submit':
        submit_job(args)
    elif args.command == 'status':
        check_status(args)
    elif args.command == 'list':
        list_jobs(args)


if __name__ == '__main__':
    main()
