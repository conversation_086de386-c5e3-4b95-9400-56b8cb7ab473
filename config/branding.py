"""
Branding configuration for video generation.
This file contains settings for branding elements like logo, watermark, and brand colors.
"""

import os
from PIL import ImageColor

# Branding settings
BRANDING_ENABLED = True

# Logo settings
LOGO_ENABLED = False
LOGO_PATH = os.path.join("assets", "logo.png")  # Path to logo file
LOGO_SIZE = 0.15  # Size as a percentage of video height (0.0-1.0)
LOGO_POSITION = "top-right"  # Options: "top-left", "top-right", "bottom-left", "bottom-right"
LOGO_MARGIN = 20  # Margin from the edge in pixels
LOGO_OPACITY = 0.9  # Opacity of the logo (0.0-1.0)
LOGO_DISPLAY_DURATION = 0.0  # Duration in seconds (0 means entire video)
LOGO_ANIMATION = True  # Whether to animate the logo entrance
LOGO_ANIMATION_STYLE = "fade_in"  # Options: "fade_in", "slide_in", "pop"
LOGO_ANIMATION_DURATION = 0.7  # Duration of animation in seconds

# Brand colors (defined first so they can be used in watermark settings)
BRAND_PRIMARY_COLOR = "#FF5733"  # Primary brand color (hex)
BRAND_SECONDARY_COLOR = "#33A1FF"  # Secondary brand color (hex)
BRAND_ACCENT_COLOR = "#33FF57"  # Accent brand color (hex)

# Watermark settings
WATERMARK_ENABLED = True
WATERMARK_TEXT = "@MNSuperHitSongs"  # Text to display as watermark
WATERMARK_FONT = "Arial-Bold"  # Font for watermark text
WATERMARK_SIZE = 20  # Font size for watermark (increased for better visibility)
WATERMARK_COLOR = BRAND_PRIMARY_COLOR  # Color of watermark text (using brand color)
WATERMARK_OPACITY = 0  # Opacity of watermark (increased for better visibility)
WATERMARK_POSITION = "bottom-right"  # Options: "top-left", "top-right", "bottom-left", "bottom-right"
WATERMARK_MARGIN = 5  # Margin from the edge in pixels
WATERMARK_SHADOW = True  # Whether to add shadow to watermark
WATERMARK_BG_COLOR = "rgba(0,0,0,0.7)"  # Semi-transparent background for better visibility

# Convert hex colors to RGB tuples for MoviePy
BRAND_PRIMARY_RGB = ImageColor.getcolor(BRAND_PRIMARY_COLOR, "RGB")
BRAND_SECONDARY_RGB = ImageColor.getcolor(BRAND_SECONDARY_COLOR, "RGB")
BRAND_ACCENT_RGB = ImageColor.getcolor(BRAND_ACCENT_COLOR, "RGB")

# Brand color theme for text elements
BRAND_TITLE_COLOR = BRAND_PRIMARY_COLOR
BRAND_CAPTION_COLOR = "white"  # Default caption color
BRAND_SCENE_NUMBER_COLOR = BRAND_SECONDARY_COLOR

# Brand style settings
BRAND_STYLE = "modern"  # Options: "modern", "minimal", "bold", "playful", "elegant"

# Helper functions
def get_position_coordinates(position, width, height, margin, video_width, video_height):
    """
    Get the x, y coordinates for a position string

    Args:
        position (str): Position string (e.g., "top-left")
        width (int): Width of the element
        height (int): Height of the element
        margin (int): Margin from the edge
        video_width (int): Width of the video
        video_height (int): Height of the video

    Returns:
        tuple: (x, y) coordinates
    """
    if position == "top-left":
        return (margin, margin)
    elif position == "top-right":
        return (video_width - width - margin, margin)
    elif position == "bottom-left":
        return (margin, video_height - height - margin)
    elif position == "bottom-right":
        return (video_width - width - margin, video_height - height - margin)
    elif position == "center":
        return (video_width // 2 - width // 2, video_height // 2 - height // 2)
    else:
        # Default to top-left
        return (margin, margin)
