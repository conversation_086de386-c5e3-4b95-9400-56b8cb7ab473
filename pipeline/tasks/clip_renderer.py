#!/usr/bin/env python3
"""
Clip <PERSON>derer Task for the Smart Video Highlight Generator Pipeline


This version uses direct function calls instead  for task execution.
"""

import os
import json
import subprocess
import concurrent.futures
from typing import Dict, Any, Optional, List, Tuple

import ffmpeg

from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR

class ClipRenderer(BaseTask):
    """
    Stage-07: Clip Renderer

    Executes parallel ffmpeg "smart trim" operations with codec copying for speed,
    generates intermediate transport stream segments, and creates a JSON manifest
    containing metadata for each segment.
    """

    task_name = "clip_renderer"
    requires_gpu = False

    def run(self, job_id: str, aligned_result: Dict[str, Any], ingestor_result: Dict[str, Any], params: Dict[str, Any], qa_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Render highlight clips from aligned windows or QA highlights

        Args:
            job_id: Unique identifier for the job
            aligned_result: Results from the shot boundary aligner
            ingestor_result: Results from the video ingestor
            params: Additional parameters for the task
            qa_result: Optional QA highlights result to use instead of aligned windows

        Returns:
            Dictionary containing rendered clip results
        """
        self.logger.info(f"Rendering highlight clips for job {job_id}")

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Create job directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        clips_dir = os.path.join(job_dir, "clips")
        os.makedirs(clips_dir, exist_ok=True)

        # Determine which windows to use - prioritize QA highlights if available
        if qa_result and qa_result.get('clip_windows'):
            windows_to_render = qa_result['clip_windows']
            source_type = "QA highlights"
            self.logger.info(f"Using QA highlights for clip rendering")
        else:
            # Fallback to aligned windows from shot boundary aligner
            aligned_path = aligned_result.get('aligned_path')
            if not aligned_path or not os.path.exists(aligned_path):
                raise ValueError(f"Invalid aligned path from shot boundary aligner: {aligned_path}")

            # Load aligned windows
            with open(aligned_path, 'r') as f:
                windows_to_render = json.load(f)
            source_type = "aligned windows"
            self.logger.info(f"Using shot boundary aligner results for clip rendering")

        if not windows_to_render:
            raise ValueError(f"No {source_type} found for clip rendering")

        self.logger.info(f"Found {len(windows_to_render)} {source_type} to render")

        # Get video path from ingestor result
        video_path = ingestor_result.get('video_path')
        if not video_path or not os.path.exists(video_path):
            raise ValueError(f"Invalid video path from ingestor: {video_path}")

        # Get parameters
        max_workers = params.get('max_workers', 4)  # Default 4 parallel workers

        # Render clips in parallel
        clip_results = self._render_clips_parallel(windows_to_render, video_path, clips_dir, max_workers)

        # Create clip manifest
        manifest = {
            'clips': clip_results,
            'total_clips': len(clip_results),
            'total_duration': sum(clip.get('duration', 0) for clip in clip_results)
        }

        # Save manifest to file
        manifest_path = os.path.join(clips_dir, "clip_manifest.json")
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)

        # Create result with clip data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'clips_dir': clips_dir,
            'manifest_path': manifest_path,
            'metadata': {
                'clip_count': len(clip_results),
                'total_duration': manifest['total_duration'],
                'clips': clip_results
            }
        }

        # Save state for idempotency
        self.save_state(job_id, result, self.task_name)

        return result

    def _render_clips_parallel(self, aligned_windows: List[Dict[str, Any]], video_path: str,
                              clips_dir: str, max_workers: int) -> List[Dict[str, Any]]:
        """
        Render clips in parallel using multiple workers

        Args:
            aligned_windows: List of aligned highlight windows
            video_path: Path to the source video file
            clips_dir: Directory to save rendered clips
            max_workers: Maximum number of parallel workers

        Returns:
            List of clip results with metadata
        """
        clip_results = []

        # Define a function to render a single clip
        def render_clip(window, index):
            try:
                # Generate clip ID
                clip_id = f"clip_{index:03d}"

                # Get window timestamps
                start_time = window.get('start_time', 0)
                end_time = window.get('end_time', 0)
                duration = window.get('duration', 0)

                # Define output path
                output_path = os.path.join(clips_dir, f"{clip_id}.mp4")

                # Use ffmpeg to extract the clip
                (
                    ffmpeg
                    .input(video_path, ss=start_time)
                    .output(
                        output_path,
                        t=duration,
                        vcodec='libx264',  # Use H.264 codec for video
                        acodec='aac',      # Use AAC codec for audio
                        preset='fast',     # Fast encoding preset
                        crf=23,            # Constant Rate Factor (quality)
                        f='mp4',           # Use MP4 format for better compatibility
                        movflags='+faststart'  # Optimize for streaming
                    )
                    .global_args('-y')  # Overwrite output file if it exists
                    .run(quiet=True, overwrite_output=True)
                )

                # Create clip result
                clip_result = {
                    'clip_id': clip_id,
                    'file_path': output_path,
                    'start_time': float(start_time),
                    'end_time': float(end_time),
                    'duration': float(duration),
                    'score': float(window.get('final_score', 0.5)),
                    'keyword': window.get('keyword', ''),
                    'text': window.get('text', '')
                }

                return clip_result

            except Exception as e:
                self.logger.error(f"Error rendering clip {index}: {str(e)}")
                return None

        # Render clips in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all rendering tasks
            future_to_index = {
                executor.submit(render_clip, window, i): i
                for i, window in enumerate(aligned_windows)
            }

            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    clip_result = future.result()
                    if clip_result:
                        clip_results.append(clip_result)
                except Exception as e:
                    self.logger.error(f"Error processing clip {index}: {str(e)}")

        # Sort clips by start time
        clip_results.sort(key=lambda x: x.get('start_time', 0))

        return clip_results
# Create a task instance
clip_renderer = ClipRenderer()

def render_clips(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the clip_renderer task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return clip_renderer.run(job_id, *args, **kwargs)
