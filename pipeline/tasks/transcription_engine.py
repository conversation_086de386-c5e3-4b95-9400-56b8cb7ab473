#!/usr/bin/env python3
"""
Transcription Engine Task for the Smart Video Highlight Generator Pipeline


This version uses direct function calls instead  for task execution.
"""

import os
import json
import subprocess
from typing import Dict, Any, Optional, List

import ffmpeg

from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR, WHISPER_MODEL
from utils.openai_client import OpenAIClient

class TranscriptionEngine(BaseTask):
    """
    Stage-02: Transcription Engine

    Processes audio using Whisper with optimized parameters, generates timestamped
    transcription in .VTT format, and stores detailed segment data.
    """

    task_name = "transcription_engine"
    requires_gpu = True  # Transcription benefits from GPU acceleration

    def run(self, job_id: str, ingestor_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transcribe the audio from the video

        Args:
            job_id: Unique identifier for the job
            ingestor_result: Results from the video ingestor
            params: Additional parameters for the task

        Returns:
            Dictionary containing transcription results
        """
        self.logger.info(f"Transcribing audio for job {job_id}")

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Get video path from ingestor result
        video_path = ingestor_result.get('video_path')
        if not video_path or not os.path.exists(video_path):
            raise ValueError(f"Invalid video path from ingestor: {video_path}")

        # Create job directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        transcript_dir = os.path.join(job_dir, "transcript")
        os.makedirs(transcript_dir, exist_ok=True)

        # Extract audio to a temporary file
        audio_path = os.path.join(transcript_dir, "audio.mp3")

        try:
            # Use ffmpeg to extract audio
            (
                ffmpeg
                .input(video_path)
                .output(
                    audio_path,
                    acodec='libmp3lame',  # MP3 codec
                    q=2,  # Quality level (2 is high quality)
                    ac=1,  # Mono audio (1 channel)
                    ar=16000  # 16kHz sample rate (optimal for speech recognition)
                )
                .global_args('-y')  # Overwrite output file if it exists
                .run(quiet=True, overwrite_output=True)
            )
        except ffmpeg.Error as e:
            self.logger.error(f"Error extracting audio: {e.stderr.decode() if hasattr(e, 'stderr') else str(e)}")
            raise ValueError(f"Failed to extract audio from video: {str(e)}")

        # Check if audio file is too large for OpenAI API (26MB limit)
        audio_size_mb = os.path.getsize(audio_path) / (1024 * 1024)
        max_size_mb = 25  # Use 25MB as safe limit

        if audio_size_mb > max_size_mb:
            self.logger.info(f"Audio file is {audio_size_mb:.1f}MB, exceeds {max_size_mb}MB limit - splitting into chunks")
            transcript_data = self._transcribe_large_audio(audio_path, transcript_dir)
        else:
            transcript_data = self._transcribe_single_audio(audio_path, job_id)

        if not transcript_data:
            raise ValueError("Failed to transcribe audio: No transcript data generated")

        # Save transcript to files
        transcript_json_path = os.path.join(transcript_dir, "transcript.json")
        with open(transcript_json_path, 'w') as f:
            json.dump(transcript_data, f, indent=2)

        # Save plain text transcript
        transcript_txt_path = os.path.join(transcript_dir, "transcript.txt")
        if not os.path.exists(transcript_txt_path):
            with open(transcript_txt_path, 'w') as f:
                f.write(transcript_data.get('text', ''))

        # Generate VTT file from segments
        vtt_path = os.path.join(transcript_dir, "transcript.vtt")
        if not os.path.exists(vtt_path):
            self._generate_vtt(transcript_data, vtt_path)

        # Create result with transcript data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'transcript_path': transcript_json_path,
            'transcript_txt_path': transcript_txt_path,
            'transcript_vtt_path': vtt_path,
            'audio_path': audio_path,
            'metadata': {
                'duration': ingestor_result.get('metadata', {}).get('duration'),
                'word_count': len(transcript_data.get('text', '').split()),
                'segment_count': len(transcript_data.get('segments', [])),
                'language': transcript_data.get('language'),
            }
        }

        # Save state for idempotency
        self.save_state(job_id, result, self.task_name)

        return result

    def _load_existing_transcript_data(self, transcript_json_path: str, audio_path: str) -> Optional[Dict[str, Any]]:
        """
        Load existing transcript data if it exists and is valid

        Args:
            transcript_json_path: Path to the transcript JSON file
            audio_path: Path to the audio file (for validation)

        Returns:
            Dictionary containing transcript data or None if not found/invalid
        """
        # Check if transcript JSON file exists
        if not os.path.exists(transcript_json_path):
            self.logger.debug(f"Transcript file not found: {transcript_json_path}")
            return None

        # Check if audio file exists (basic validation)
        if not os.path.exists(audio_path):
            self.logger.debug(f"Audio file not found: {audio_path}")
            return None

        try:
            # Load the transcript data
            with open(transcript_json_path, 'r') as f:
                transcript_data = json.load(f)

            # Validate the transcript data structure
            if not self._validate_transcript_data(transcript_data):
                self.logger.warning(f"Invalid transcript data structure in {transcript_json_path}")
                return None

            # Check if the transcript data is complete (has text and segments)
            if not transcript_data.get('text') or not transcript_data.get('segments'):
                self.logger.warning(f"Incomplete transcript data in {transcript_json_path}")
                return None

            self.logger.debug(f"Successfully loaded existing transcript data from {transcript_json_path}")
            return transcript_data

        except (json.JSONDecodeError, IOError) as e:
            self.logger.warning(f"Error loading transcript data from {transcript_json_path}: {str(e)}")
            return None

    def _validate_transcript_data(self, transcript_data: Dict[str, Any]) -> bool:
        """
        Validate the structure of transcript data

        Args:
            transcript_data: Dictionary containing transcript data

        Returns:
            True if valid, False otherwise
        """
        # Check if it's a dictionary
        if not isinstance(transcript_data, dict):
            return False

        # Check for required fields
        required_fields = ['text', 'segments']
        for field in required_fields:
            if field not in transcript_data:
                return False

        # Check if segments is a list
        if not isinstance(transcript_data.get('segments'), list):
            return False

        # Check if text is a string
        if not isinstance(transcript_data.get('text'), str):
            return False

        # Validate segment structure (check first few segments)
        segments = transcript_data.get('segments', [])
        for i, segment in enumerate(segments[:3]):  # Check first 3 segments
            if not isinstance(segment, dict):
                return False
            # Check for required segment fields
            segment_fields = ['start', 'end', 'text']
            for field in segment_fields:
                if field not in segment:
                    return False

        return True

    def _generate_vtt(self, transcript_data: Dict[str, Any], output_path: str) -> None:
        """
        Generate a WebVTT file from transcript segments

        Args:
            transcript_data: Transcript data from Whisper API
            output_path: Path to save the VTT file
        """
        segments = transcript_data.get('segments', [])

        with open(output_path, 'w') as f:
            # Write VTT header
            f.write("WEBVTT\n\n")

            # Write segments
            for i, segment in enumerate(segments):
                start_time = segment.get('start', 0)
                end_time = segment.get('end', 0)
                text = segment.get('text', '').strip()

                # Format timestamps as HH:MM:SS.mmm
                start_formatted = self._format_timestamp(start_time)
                end_formatted = self._format_timestamp(end_time)

                # Write cue
                f.write(f"{i + 1}\n")
                f.write(f"{start_formatted} --> {end_formatted}\n")
                f.write(f"{text}\n\n")

    def _format_timestamp(self, seconds: float) -> str:
        """
        Format seconds as HH:MM:SS.mmm for VTT

        Args:
            seconds: Time in seconds

        Returns:
            Formatted timestamp string
        """
        hours = int(seconds / 3600)
        minutes = int((seconds % 3600) / 60)
        seconds = seconds % 60

        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}".replace('.', ',')

    def _transcribe_single_audio(self, audio_path: str, job_id: str) -> Dict[str, Any]:
        """
        Transcribe a single audio file using OpenAI Whisper API

        Args:
            audio_path: Path to the audio file
            job_id: Job identifier for cost tracking

        Returns:
            Transcript data dictionary
        """
        # Check if transcript data already exists and is valid
        transcript_json_path = os.path.join(os.path.dirname(audio_path), "transcript.json")
        existing_transcript_data = self._load_existing_transcript_data(transcript_json_path, audio_path)

        if existing_transcript_data:
            self.logger.info(f"Reusing existing transcript data for job {job_id} - API call skipped")
            # Record that we skipped an API call for cost tracking
            self.record_api_call(job_id, "openai", "whisper_transcription", 0.0)
            return existing_transcript_data

        # Initialize OpenAI client
        openai_client = OpenAIClient()

        # Transcribe audio using OpenAI's Whisper API
        self.logger.info(f"Making new Whisper API call for job {job_id}")
        try:
            api_response = openai_client.transcribe_audio(
                audio_file=audio_path,
                model=WHISPER_MODEL,
                response_format="verbose_json"  # Get detailed segment information
            )

            if not api_response:
                raise ValueError("Failed to transcribe audio: Empty response from Whisper API")

            # Ensure we have a dictionary response
            if isinstance(api_response, dict):
                transcript_data = api_response
            else:
                raise ValueError(f"Unexpected response type from Whisper API: {type(api_response)}")

            # Record the API call for cost tracking (estimated cost for Whisper API)
            # Whisper API costs $0.006 per minute, estimate based on audio duration
            try:
                # Use ffprobe to get audio duration
                probe = ffmpeg.probe(audio_path)
                duration_seconds = float(probe['format']['duration'])
                duration_minutes = duration_seconds / 60.0
                estimated_cost = duration_minutes * 0.006
                self.record_api_call(job_id, "openai", "whisper_transcription", estimated_cost)
            except Exception as e:
                self.logger.warning(f"Could not calculate API cost: {str(e)}")
                # Use a default cost estimate
                self.record_api_call(job_id, "openai", "whisper_transcription", 0.05)

            return transcript_data

        except Exception as e:
            self.logger.error(f"Error transcribing audio: {str(e)}")
            raise ValueError(f"Failed to transcribe audio: {str(e)}")

    def _transcribe_large_audio(self, audio_path: str, transcript_dir: str) -> Dict[str, Any]:
        """
        Transcribe a large audio file by splitting it into chunks

        Args:
            audio_path: Path to the large audio file
            transcript_dir: Directory to store transcript files

        Returns:
            Combined transcript data dictionary
        """
        # Check if transcript data already exists and is valid
        transcript_json_path = os.path.join(transcript_dir, "transcript.json")
        existing_transcript_data = self._load_existing_transcript_data(transcript_json_path, audio_path)

        if existing_transcript_data:
            self.logger.info(f"Reusing existing transcript data for large audio file - API calls skipped")
            # Record that we skipped API calls for cost tracking
            self.record_api_call("large_audio", "openai", "whisper_transcription", 0.0)
            return existing_transcript_data

        # Get audio duration
        try:
            probe = ffmpeg.probe(audio_path)
            total_duration = float(probe['format']['duration'])
        except Exception as e:
            self.logger.error(f"Error getting audio duration: {str(e)}")
            raise ValueError(f"Failed to get audio duration: {str(e)}")

        # Calculate chunk duration (aim for ~20MB chunks, roughly 30-40 minutes)
        chunk_duration = 30 * 60  # 30 minutes per chunk
        chunks_needed = int((total_duration + chunk_duration - 1) // chunk_duration)  # Ceiling division

        self.logger.info(f"Splitting {total_duration/60:.1f} minute audio into {chunks_needed} chunks of {chunk_duration/60:.1f} minutes each")

        # Create chunks directory
        chunks_dir = os.path.join(transcript_dir, "audio_chunks")
        os.makedirs(chunks_dir, exist_ok=True)

        # Split audio into chunks and transcribe each
        all_segments = []
        total_cost = 0.0

        for i in range(chunks_needed):
            start_time = i * chunk_duration
            actual_duration = min(chunk_duration, total_duration - start_time)

            if actual_duration <= 0:
                break

            # Create chunk file
            chunk_filename = f"chunk_{i+1:03d}.mp3"
            chunk_path = os.path.join(chunks_dir, chunk_filename)

            # Extract chunk using ffmpeg
            try:
                (
                    ffmpeg
                    .input(audio_path, ss=start_time)
                    .output(
                        chunk_path,
                        t=actual_duration,
                        acodec='libmp3lame',
                        q=2,
                        ac=1,
                        ar=16000
                    )
                    .global_args('-y')
                    .run(quiet=True, overwrite_output=True)
                )
            except ffmpeg.Error as e:
                self.logger.error(f"Error creating audio chunk {i+1}: {str(e)}")
                continue

            # Transcribe chunk
            self.logger.info(f"Transcribing chunk {i+1}/{chunks_needed} ({start_time/60:.1f}-{(start_time+actual_duration)/60:.1f} min)")

            try:
                openai_client = OpenAIClient()
                chunk_response = openai_client.transcribe_audio(
                    audio_file=chunk_path,
                    model=WHISPER_MODEL,
                    response_format="verbose_json"
                )

                if chunk_response and isinstance(chunk_response, dict):
                    # Adjust timestamps to be relative to the original audio
                    chunk_segments = chunk_response.get('segments', [])
                    for segment in chunk_segments:
                        segment['start'] += start_time
                        segment['end'] += start_time

                    all_segments.extend(chunk_segments)

                    # Track cost
                    chunk_duration_minutes = actual_duration / 60.0
                    chunk_cost = chunk_duration_minutes * 0.006
                    total_cost += chunk_cost

                else:
                    self.logger.warning(f"Failed to transcribe chunk {i+1}")

            except Exception as e:
                self.logger.error(f"Error transcribing chunk {i+1}: {str(e)}")
                continue

            # Clean up chunk file to save space
            try:
                os.remove(chunk_path)
            except:
                pass

        # Record total API cost
        self.record_api_call("multi_chunk", "openai", "whisper_transcription", total_cost)

        # Combine all segments into a single transcript
        combined_transcript = {
            'text': ' '.join(segment.get('text', '') for segment in all_segments),
            'segments': all_segments,
            'language': all_segments[0].get('language', 'en') if all_segments else 'en'
        }

        self.logger.info(f"Combined {len(all_segments)} segments from {chunks_needed} chunks, total cost: ${total_cost:.3f}")

        return combined_transcript



# Create a task instance
transcription_engine = TranscriptionEngine()

def transcribe_video(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the transcription_engine task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return transcription_engine.run(job_id, *args, **kwargs)
