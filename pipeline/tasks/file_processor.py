#!/usr/bin/env python3
"""
File Processor Task for handling file operations and directory management
"""

import os
import json
import shutil
import logging
import tempfile
from typing import Dict, Any, Optional
from pathlib import Path

from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR


class FileProcessor(BaseTask):
    """
    File Processor Task
    
    Handles atomic file operations, directory creation, and file movement
    to ensure consistent state during processing.
    """
    
    task_name = "file_processor"
    requires_gpu = False
    
    def process(self, job_id: str, validation_result: Dict[str, Any], params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process and move validated files to the job directory
        
        Args:
            job_id: Unique identifier for the job
            validation_result: Results from the video validator
            params: Additional parameters for processing
            
        Returns:
            Dictionary containing processing results
        """
        params = params or {}
        self.logger.info(f"Processing files for job {job_id}")
        
        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached processing results for job {job_id}")
            return existing_state
        
        try:
            # Validate input
            if validation_result.get('status') != 'completed':
                raise ValueError("Cannot process files: validation did not complete successfully")
            
            video_file = validation_result.get('video_file')
            transcript_dir = validation_result.get('transcript_dir')
            
            if not video_file or not transcript_dir:
                raise ValueError("Missing video file or transcript directory in validation result")
            
            # Create job directory structure
            job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
            os.makedirs(job_dir, exist_ok=True)
            
            # Create subdirectories
            processed_video_dir = os.path.join(job_dir, "video")
            processed_transcript_dir = os.path.join(job_dir, "transcript")
            
            os.makedirs(processed_video_dir, exist_ok=True)
            os.makedirs(processed_transcript_dir, exist_ok=True)
            
            # Perform atomic file operations
            moved_files = self._move_files_atomically(
                video_file, 
                transcript_dir, 
                processed_video_dir, 
                processed_transcript_dir
            )
            
            # Create processing metadata
            processing_metadata = {
                'job_id': job_id,
                'original_sample_dir': validation_result.get('sample_dir'),
                'moved_files': moved_files,
                'job_directory_structure': {
                    'job_dir': job_dir,
                    'video_dir': processed_video_dir,
                    'transcript_dir': processed_transcript_dir
                }
            }
            
            # Save processing metadata
            metadata_file = os.path.join(job_dir, "processing_metadata.json")
            with open(metadata_file, 'w') as f:
                json.dump(processing_metadata, f, indent=2)
            
            result = {
                'status': 'completed',
                'job_id': job_id,
                'job_dir': job_dir,
                'video_dir': processed_video_dir,
                'transcript_dir': processed_transcript_dir,
                'moved_files': moved_files,
                'processing_metadata': processing_metadata,
                'video_file_path': moved_files.get('video_file'),
                'transcript_files': moved_files.get('transcript_files', {})
            }
            
            self.logger.info(f"File processing completed successfully for job {job_id}")
            self.save_state(job_id, result, self.task_name)
            return result
            
        except Exception as e:
            error_msg = f"File processing failed: {str(e)}"
            self.logger.error(error_msg)
            
            result = {
                'status': 'failed',
                'job_id': job_id,
                'errors': [error_msg],
                'exception': str(e)
            }
            
            self.save_state(job_id, result, self.task_name)
            return result
    
    def _move_files_atomically(self, video_file: str, transcript_dir: str, 
                              dest_video_dir: str, dest_transcript_dir: str) -> Dict[str, Any]:
        """
        Move files atomically to prevent partial moves
        
        Args:
            video_file: Source video file path
            transcript_dir: Source transcript directory
            dest_video_dir: Destination video directory
            dest_transcript_dir: Destination transcript directory
            
        Returns:
            Dictionary with information about moved files
        """
        moved_files = {
            'video_file': None,
            'transcript_files': {}
        }
        
        try:
            # Move video file
            video_filename = os.path.basename(video_file)
            dest_video_path = os.path.join(dest_video_dir, video_filename)
            
            # Use atomic move via temporary file
            with tempfile.NamedTemporaryFile(dir=dest_video_dir, delete=False) as temp_file:
                temp_path = temp_file.name
            
            shutil.copy2(video_file, temp_path)
            os.rename(temp_path, dest_video_path)
            moved_files['video_file'] = dest_video_path
            
            self.logger.info(f"Video file moved to: {dest_video_path}")
            
            # Move transcript files
            for filename in os.listdir(transcript_dir):
                src_file = os.path.join(transcript_dir, filename)
                if os.path.isfile(src_file):
                    dest_file = os.path.join(dest_transcript_dir, filename)
                    
                    # Use atomic move via temporary file
                    with tempfile.NamedTemporaryFile(dir=dest_transcript_dir, delete=False) as temp_file:
                        temp_path = temp_file.name
                    
                    shutil.copy2(src_file, temp_path)
                    os.rename(temp_path, dest_file)
                    moved_files['transcript_files'][filename] = dest_file
                    
                    self.logger.info(f"Transcript file moved: {filename} -> {dest_file}")
            
            return moved_files
            
        except Exception as e:
            # Clean up any partial moves
            self._cleanup_partial_moves(moved_files)
            raise Exception(f"Atomic file move failed: {str(e)}")
    
    def _cleanup_partial_moves(self, moved_files: Dict[str, Any]) -> None:
        """
        Clean up any files that were partially moved during a failed operation
        
        Args:
            moved_files: Dictionary tracking moved files
        """
        try:
            # Remove video file if it was moved
            if moved_files.get('video_file') and os.path.exists(moved_files['video_file']):
                os.remove(moved_files['video_file'])
                self.logger.info(f"Cleaned up partial video move: {moved_files['video_file']}")
            
            # Remove transcript files if they were moved
            for filename, filepath in moved_files.get('transcript_files', {}).items():
                if os.path.exists(filepath):
                    os.remove(filepath)
                    self.logger.info(f"Cleaned up partial transcript move: {filepath}")
                    
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")


# Create a task instance
file_processor = FileProcessor()


def process_validated_files(job_id: str, validation_result: Dict[str, Any], params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Run the file processor task
    
    Args:
        job_id: Unique identifier for the job
        validation_result: Results from the video validator
        params: Additional parameters for processing
        
    Returns:
        Dictionary containing processing results
    """
    return file_processor.run(job_id, validation_result, params or {})
