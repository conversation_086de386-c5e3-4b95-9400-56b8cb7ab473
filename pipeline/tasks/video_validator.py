#!/usr/bin/env python3
"""
Video Validator Task for validating video files and transcript data
"""

import os
import json
import logging
from typing import Dict, Any, Optional

from pipeline.tasks.base_task import BaseTask
from utils.validation_utils import VideoValidationUtils, ValidationError
from config.settings import PIPELINE_OUTPUT_DIR


class VideoValidator(BaseTask):
    """
    Video Validator Task
    
    Validates video files and their associated transcript data to ensure
    they meet the requirements for processing in the pipeline.
    """
    
    task_name = "video_validator"
    requires_gpu = False
    
    def process(self, job_id: str, sample_dir: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Validate video file and transcript data in a sample directory
        
        Args:
            job_id: Unique identifier for the job
            sample_dir: Path to the sample directory containing video and transcript
            params: Additional parameters for validation
            
        Returns:
            Dictionary containing validation results
        """
        params = params or {}
        self.logger.info(f"Validating sample directory: {sample_dir}")
        
        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached validation results for job {job_id}")
            return existing_state
        
        try:
            # Validate the sample directory
            validation_result = VideoValidationUtils.validate_sample_directory(sample_dir)
            
            if not validation_result['valid']:
                # Validation failed
                result = {
                    'status': 'failed',
                    'job_id': job_id,
                    'sample_dir': sample_dir,
                    'validation_result': validation_result,
                    'errors': validation_result['errors']
                }
                
                self.logger.error(f"Validation failed for {sample_dir}: {validation_result['errors']}")
                self.save_state(job_id, result, self.task_name)
                return result
            
            # Extract validated paths for downstream tasks
            video_file = None
            transcript_dir = None
            
            # Find the video file
            for file in os.listdir(sample_dir):
                file_path = os.path.join(sample_dir, file)
                if os.path.isfile(file_path):
                    from pathlib import Path
                    file_ext = Path(file).suffix.lower()
                    if file_ext in VideoValidationUtils.SUPPORTED_VIDEO_FORMATS:
                        video_file = file_path
                        break
            
            # Set transcript directory
            transcript_dir = os.path.join(sample_dir, 'transcript')
            
            # Validation passed
            result = {
                'status': 'completed',
                'job_id': job_id,
                'sample_dir': sample_dir,
                'video_file': video_file,
                'transcript_dir': transcript_dir,
                'validation_result': validation_result,
                'metadata': {
                    'video_size_bytes': validation_result['video_validation']['size_bytes'],
                    'transcript_duration': validation_result['transcript_json_validation']['duration'],
                    'transcript_language': validation_result['transcript_json_validation']['language'],
                    'transcript_files': validation_result['transcript_validation']['files_found']
                }
            }
            
            self.logger.info(f"Validation completed successfully for {sample_dir}")
            self.save_state(job_id, result, self.task_name)
            return result
            
        except Exception as e:
            error_msg = f"Unexpected error during validation: {str(e)}"
            self.logger.error(error_msg)
            
            result = {
                'status': 'failed',
                'job_id': job_id,
                'sample_dir': sample_dir,
                'errors': [error_msg],
                'exception': str(e)
            }
            
            self.save_state(job_id, result, self.task_name)
            return result


# Create a task instance
video_validator = VideoValidator()


def validate_video_and_transcript(job_id: str, sample_dir: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Run the video validator task
    
    Args:
        job_id: Unique identifier for the job
        sample_dir: Path to the sample directory
        params: Additional parameters for validation
        
    Returns:
        Dictionary containing validation results
    """
    return video_validator.run(job_id, sample_dir, params or {})
