#!/usr/bin/env python3
"""
Pre-flight Validator Task for the Smart Video Highlight Generator Pipeline

This version uses direct function calls instead  for task execution.
"""

import os
import shutil
from typing import Dict, Any

import ffmpeg

from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_MAX_VIDEO_DURATION, PIPELINE_OUTPUT_DIR

class PreflightValidator(BaseTask):
    """
    Stage-00: Pre-flight Validator

    Validates input video container MIME type compatibility, enforces constraints,
    and rejects incompatible sources.
    """

    task_name = "preflight_validator"
    requires_gpu = False

    def process(self, job_id: str, video_path: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Validate the input video file

        Args:
            job_id: Unique identifier for the job
            video_path: Path to the input video file
            params: Additional parameters for the task

        Returns:
            Dictionary containing validation results and metadata
        """
        params = params or {}
        self.logger.info(f"Validating video file: {video_path}")

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Create job directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        os.makedirs(job_dir, exist_ok=True)

        # Validate that the file exists
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")

        # Get video metadata using ffprobe
        try:
            probe = ffmpeg.probe(video_path)
        except ffmpeg.Error as e:
            self.logger.error(f"Error probing video file: {e.stderr.decode()}")
            raise ValueError(f"Invalid video file: {e.stderr.decode()}")

        # Extract video stream information
        video_streams = [stream for stream in probe['streams'] if stream['codec_type'] == 'video']
        audio_streams = [stream for stream in probe['streams'] if stream['codec_type'] == 'audio']

        if not video_streams:
            raise ValueError("No video streams found in the file")

        # Get primary video stream
        video_stream = video_streams[0]

        # Extract key metadata
        duration = float(probe['format'].get('duration', 0))
        width = int(video_stream.get('width', 0))
        height = int(video_stream.get('height', 0))

        # Get frame rate as a float
        frame_rate = 0.0
        if 'avg_frame_rate' in video_stream:
            try:
                num, den = map(int, video_stream['avg_frame_rate'].split('/'))
                frame_rate = num / den if den != 0 else 0.0
            except (ValueError, ZeroDivisionError):
                frame_rate = 0.0

        # Check if the video has a variable frame rate
        has_variable_frame_rate = False
        if 'r_frame_rate' in video_stream and video_stream['r_frame_rate'] != video_stream['avg_frame_rate']:
            has_variable_frame_rate = True

        # Validate constraints
        validation_errors = []

        # Check duration - reject videos over 240 minutes (4 hours)
        if duration > PIPELINE_MAX_VIDEO_DURATION:
            validation_errors.append(f"Video duration ({duration:.2f}s) exceeds maximum allowed ({PIPELINE_MAX_VIDEO_DURATION}s)")

        # Check frame rate
        if frame_rate > 60:
            validation_errors.append(f"Frame rate ({frame_rate:.2f} fps) exceeds maximum allowed (60 fps)")

        # Check for audio stream
        if not audio_streams:
            validation_errors.append("No audio streams found in the file")

        # Check for variable frame rate
        if has_variable_frame_rate:
            validation_errors.append("Video has a variable frame rate, which may cause issues with processing")

        # If validation failed, return errors
        if validation_errors:
            result = {
                'status': 'failed',
                'job_id': job_id,
                'errors': validation_errors,
                'video_path': video_path,
                'metadata': {
                    'duration': duration,
                    'width': width,
                    'height': height,
                    'frame_rate': frame_rate,
                    'has_audio': bool(audio_streams),
                    'has_variable_frame_rate': has_variable_frame_rate
                }
            }
            self.save_state(job_id, result, self.task_name)
            return result

        # Copy the video to the job directory with a standardized name
        output_video_path = os.path.join(job_dir, "raw.mp4")
        shutil.copy2(video_path, output_video_path)

        # Create successful result
        result = {
            'status': 'completed',
            'job_id': job_id,
            'video_path': output_video_path,
            'original_video_path': video_path,
            'metadata': {
                'duration': duration,
                'width': width,
                'height': height,
                'frame_rate': frame_rate,
                'has_audio': bool(audio_streams),
                'has_variable_frame_rate': has_variable_frame_rate,
                'video_codec': video_stream.get('codec_name', 'unknown'),
                'audio_codec': audio_streams[0].get('codec_name', 'unknown') if audio_streams else None,
                'format_name': probe['format'].get('format_name', 'unknown'),
                'bit_rate': int(probe['format'].get('bit_rate', 0)) if 'bit_rate' in probe['format'] else None
            }
        }

        # Save state for idempotency
        self.save_state(job_id, result, self.task_name)

        return result

# Create a task instance
preflight_validator = PreflightValidator()

def validate_video(job_id: str, video_path: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Run the preflight validator task

    Args:
        job_id: Unique identifier for the job
        video_path: Path to the input video file
        params: Additional parameters for the task

    Returns:
        Dictionary containing validation results and metadata
    """
    return preflight_validator.run(job_id, video_path, params or {})
