#!/usr/bin/env python3
"""
Example task implementation for the Smart Video Highlight Generator Pipeline

This demonstrates how to implement a task using the BaseTask class.
"""

import os
import time
import logging
from typing import Dict, Any, Optional

from pipeline.tasks.base_task import BaseTask
from pipeline.job_state_manager import get_job_state_manager

class ExampleTask(BaseTask):
    """Example task implementation"""
    
    # Set the task name
    task_name = "example_task"
    
    # Whether this task requires GPU
    requires_gpu = False
    
    # Whether this task can be resumed
    supports_resume = True
    
    # Dependencies on other stages
    dependencies = []
    
    def process(self, job_id: str, input_data: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the task
        
        Args:
            job_id: Unique identifier for the job
            input_data: Input data from the previous stage
            params: Additional parameters for the task
            
        Returns:
            Task result
        """
        self.logger.info(f"Processing example task for job {job_id}")
        
        # Simulate some processing time
        time.sleep(1)
        
        # Get parameters
        example_param = params.get('example_param', 'default_value')
        
        # Process the input data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'input_data': input_data,
            'example_param': example_param,
            'processed_at': time.time(),
            'metadata': {
                'processing_time': 1.0
            }
        }
        
        # Record an API call (for demonstration purposes)
        self.record_api_call(job_id, 'example_service', 'example_endpoint', 0.01)
        
        return result

# Create a task instance
example_task = ExampleTask()

# Function to run the task (for direct imports)
def process_example(job_id: str, input_data: Dict[str, Any], params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Run the example task
    
    Args:
        job_id: Unique identifier for the job
        input_data: Input data from the previous stage
        params: Additional parameters for the task
        
    Returns:
        Task result
    """
    return example_task.run(job_id, input_data, params or {})

if __name__ == "__main__":
    # Example usage
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python example_task.py <job_id>")
        sys.exit(1)
    
    job_id = sys.argv[1]
    
    # Create a simple input data
    input_data = {
        'example_key': 'example_value'
    }
    
    # Create parameters
    params = {
        'example_param': 'custom_value'
    }
    
    # Run the task
    result = example_task.run(job_id, input_data, params)
    
    # Print the result
    import json
    print(json.dumps(result, indent=2))
