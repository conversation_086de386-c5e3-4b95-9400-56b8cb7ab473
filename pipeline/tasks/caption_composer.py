#!/usr/bin/env python3
"""
Caption Composer Task for the Smart Video Highlight Generator Pipeline

This task handles intelligent caption styling and placement for highlight videos,
with dynamic positioning based on face detection and optimized readability.
"""

import os
import json
import logging
import re
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
import ffmpeg

from pipeline.tasks.base_task import BaseTask
from config.settings import (
    CAPTION_ENABLED, CAPTION_FONT_SIZE, CAPTION_FONT_COLOR, CAPTION_BG_COLOR,
    CAPTION_POSITION, CAPTION_FONT, CAPTION_WIDTH_PERCENT, CAPTION_MAX_LINES,
    CAPTION_LINE_HEIGHT_MULTIPLIER, CAPTION_LINE_SPACING, CAPTION_PADDING_HORIZONTAL, CAPTION_PADDING_VERTICAL,
    CAPTION_MARGIN_FROM_EDGE, CAPTION_FACE_AVOIDANCE_MARGIN, CAPTION_DYNAMIC_POSITIONING,
    CAPTION_WORD_WRAP_ALGORITHM, CAPTION_STROKE_WIDTH, CAPTION_STROKE_COLOR,
    CAPTION_SHADOW_ENABLED, CAPTION_SHADOW_OFFSET_X, CAPTION_SHADOW_OFFSET_Y,
    CAPTION_SHADOW_BLUR, CAPTION_SHADOW_COLOR, VIDEO_WIDTH, VIDEO_HEIGHT,
    CAPTION_LANGUAGE_DETECTION, CAPTION_FONT_FALLBACKS, CAPTION_UNICODE_SUPPORT,
    CAPTION_FONT_SIZE, CAPTION_AUTO_SCALE, CAPTION_MIN_FONT_SIZE, CAPTION_MAX_FONT_SIZE,
    CAPTION_CHAR_WIDTH_RATIO, CAPTION_LANGUAGE_SCALING, CAPTION_SCALE_Y, CAPTION_USE_SCALE_Y,
    CAPTION_MARGIN_V, CAPTION_MARGIN_L, CAPTION_MARGIN_R, CAPTION_POSITION_FINE_TUNE
)
from utils.language_detector import language_detector


@dataclass
class CaptionSegment:
    """Represents a caption segment with timing and positioning"""
    start_time: float
    end_time: float
    text: str
    lines: List[str]
    position_y: int
    width: int
    height: int
    language_info: Optional[Dict] = None  # Language detection results for font selection


@dataclass
class FaceRegion:
    """Represents a face region to avoid when positioning captions"""
    x: int
    y: int
    width: int
    height: int
    timestamp: float


class CaptionComposer(BaseTask):
    """
    Stage-09: Caption & Graphics Composer

    Applies intelligent captions to reframed video clips with:
    - Dynamic font size calculation
    - Face-aware positioning
    - Maximum 2-line restriction
    - Optimal readability for short-form content
    """

    task_name = "caption_composer"
    requires_gpu = False

    def __init__(self):
        super().__init__()
        self.font_path = self._get_font_path()
        self.base_font_size = CAPTION_FONT_SIZE

    def _get_font_path(self, font_preference: str = "default") -> str:
        """Get the path to the caption font file with language support

        Args:
            font_preference: Font preference key (e.g., 'hindi', 'arabic', 'default')
        """
        # Get font name based on preference
        if CAPTION_LANGUAGE_DETECTION and font_preference in CAPTION_FONT_FALLBACKS:
            font_name = CAPTION_FONT_FALLBACKS[font_preference]
        else:
            font_name = CAPTION_FONT

        # Try different font locations
        font_locations = [
            # Project fonts directory
            os.path.join("assets", "fonts", f"{font_name}.ttf"),
            # Language-specific subdirectories
            os.path.join("assets", "fonts", "hindi", f"{font_name}.ttf"),
            os.path.join("assets", "fonts", "arabic", f"{font_name}.ttf"),
            os.path.join("assets", "fonts", "chinese", f"{font_name}.ttf"),
            # Default font as fallback
            os.path.join("assets", "fonts", f"{CAPTION_FONT}.ttf"),
        ]

        for font_path in font_locations:
            if os.path.exists(font_path):
                self.logger.info(f"🔤 Using font: {font_path} for preference: {font_preference}")
                return font_path

        # Fallback to system fonts with Unicode support
        system_fonts = [
            # Hindi/Devanagari support
            "/usr/share/fonts/truetype/noto/NotoSansDevanagari-Bold.ttf",
            "/usr/share/fonts/truetype/noto/NotoSansDevanagari-Regular.ttf",
            "/System/Library/Fonts/Helvetica.ttc",  # macOS (has some Unicode support)
            # General Unicode fonts
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",  # Linux
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf",
            # Windows fonts
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/calibri.ttf"
        ]

        for font in system_fonts:
            if os.path.exists(font):
                self.logger.info(f"🔤 Using system font: {font} for preference: {font_preference}")
                return font

        self.logger.warning(f"No suitable font found for preference: {font_preference}, using default")
        return ""

    def run(self, job_id: str, reframer_result: Dict[str, Any],
            transcription_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply captions to reframed video clips

        Args:
            job_id: Unique identifier for the job
            reframer_result: Results from the reframer stage
            transcription_result: Results from transcription engine
            params: Additional parameters

        Returns:
            Dictionary containing captioned video paths and metadata
        """
        try:
            self.logger.info("🎬 Starting Caption Composer...")

            if not CAPTION_ENABLED or params.get("skip_captions", False):
                self.logger.info("📝 Captions disabled, skipping caption composition")
                return self._create_passthrough_result(job_id, reframer_result)

            # Get reframed clips
            clips = reframer_result.get("metadata", {}).get("clips", [])
            if not clips:
                raise ValueError("No reframed clips found from reframer stage")

            # Get transcript data
            transcript_vtt_path = transcription_result.get("transcript_vtt_path")
            if not transcript_vtt_path or not os.path.exists(transcript_vtt_path):
                raise ValueError(f"VTT transcript file not found: {transcript_vtt_path}")

            # Load VTT segments
            vtt_segments = self._load_vtt_segments(transcript_vtt_path)


            self.logger.info(f"📝 Loaded {len(vtt_segments)} VTT segments")

            # Process each clip
            captioned_clips = []
            for clip in clips:
                try:
                    captioned_clip = self._process_clip_captions(
                        job_id, clip, vtt_segments, params
                    )
                    if captioned_clip:
                        captioned_clips.append(captioned_clip)
                except Exception as e:
                    self.logger.error(f"Failed to process captions for clip {clip.get('clip_id', 'unknown')}: {str(e)}")
                    # Add original clip without captions as fallback
                    captioned_clips.append(clip)

            # Create result with proper structure for deliverables publisher
            result = {
                'status': 'completed',
                'job_id': job_id,
                'captioned_clips': captioned_clips,  # Keep for backward compatibility
                'metadata': {
                    'clips': captioned_clips,  # CRITICAL FIX: Add clips to metadata for deliverables publisher
                    'total_clips': len(captioned_clips),
                    'caption_settings': {
                        'font_size': CAPTION_FONT_SIZE,
                        'max_lines': CAPTION_MAX_LINES,
                        'dynamic_positioning': CAPTION_DYNAMIC_POSITIONING,
                        'face_avoidance': CAPTION_FACE_AVOIDANCE_MARGIN > 0
                    }
                }
            }

            # Save state for idempotency
            self.save_state(job_id, result, self.task_name)

            self.logger.info(f"✅ Caption composition completed for {len(captioned_clips)} clips")
            return result

        except Exception as e:
            error_msg = f"Caption composition failed: {str(e)}"
            self.logger.error(error_msg)
            return {
                'status': 'failed',
                'job_id': job_id,
                'error': error_msg
            }

    def _create_passthrough_result(self, job_id: str, reframer_result: Dict[str, Any]) -> Dict[str, Any]:
        """Create a passthrough result when captions are disabled"""
        clips = reframer_result.get("metadata", {}).get("clips", [])

        return {
            'status': 'completed',
            'job_id': job_id,
            'captioned_clips': clips,  # Keep for backward compatibility
            'metadata': {
                'clips': clips,  # CRITICAL FIX: Add clips to metadata for deliverables publisher
                'total_clips': len(clips),
                'captions_enabled': False
            }
        }

    def _load_vtt_segments(self, vtt_path: str) -> List[Dict[str, Any]]:
        """Load and parse VTT segments from file"""
        segments = []

        with open(vtt_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Parse VTT format
        lines = content.strip().split('\n')
        i = 0

        while i < len(lines):
            line = lines[i].strip()

            # Skip empty lines and WEBVTT header
            if not line or line == 'WEBVTT':
                i += 1
                continue

            # Check if this is a cue number
            if line.isdigit():
                cue_number = int(line)
                i += 1

                # Next line should be timestamp
                if i < len(lines):
                    timestamp_line = lines[i].strip()
                    if ' --> ' in timestamp_line:
                        start_str, end_str = timestamp_line.split(' --> ')
                        start_time = self._parse_vtt_timestamp(start_str)
                        end_time = self._parse_vtt_timestamp(end_str)
                        i += 1

                        # Collect text lines until empty line
                        text_lines = []
                        while i < len(lines) and lines[i].strip():
                            text_lines.append(lines[i].strip())
                            i += 1

                        if text_lines:
                            segments.append({
                                'cue_number': cue_number,
                                'start_time': start_time,
                                'end_time': end_time,
                                'text': ' '.join(text_lines)
                            })

            i += 1

        return segments

    def _parse_vtt_timestamp(self, timestamp_str: str) -> float:
        """Parse VTT timestamp format (HH:MM:SS.mmm) to seconds"""
        # Handle both comma and dot as decimal separator
        timestamp_str = timestamp_str.replace(',', '.')

        parts = timestamp_str.split(':')
        if len(parts) == 3:
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds = float(parts[2])
            return hours * 3600 + minutes * 60 + seconds
        elif len(parts) == 2:
            minutes = int(parts[0])
            seconds = float(parts[1])
            return minutes * 60 + seconds
        else:
            return float(parts[0])

    def _process_clip_captions(self, job_id: str, clip: Dict[str, Any],
                             vtt_segments: List[Dict[str, Any]], params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process captions for a single clip"""
        try:
            clip_id = clip.get('clip_id', 'unknown')
            reframed_path = clip.get('reframed_path')
            start_time = clip.get('start_time', 0)
            end_time = clip.get('end_time', 0)

            if not reframed_path or not os.path.exists(reframed_path):
                self.logger.warning(f"Reframed video not found for clip {clip_id}")
                return None

            self.logger.info(f"📝 Processing captions for clip {clip_id} ({start_time:.1f}s - {end_time:.1f}s)")

            # Find relevant VTT segments for this clip
            relevant_segments = self._find_relevant_segments(vtt_segments, start_time, end_time)

            if not relevant_segments:
                self.logger.info(f"No relevant transcript segments found for clip {clip_id}")
                return clip  # Return original clip without captions

            # Load temporal tracking data if available
            # CRITICAL FIX: Pass clip start time for proper timestamp conversion
            face_regions = self._load_face_regions(job_id, clip_id, start_time)

            # Process segments into caption format
            caption_segments = self._process_segments_for_captions(
                relevant_segments, start_time, face_regions, params
            )

            if not caption_segments:
                self.logger.info(f"No caption segments generated for clip {clip_id}")
                return clip

            # Apply captions to video with fallback methods
            captioned_path = self._apply_captions_with_font_fallback(
                reframed_path, caption_segments, job_id, clip_id, params
            )

            if captioned_path:
                # Update clip with captioned path
                captioned_clip = clip.copy()
                captioned_clip['captioned_path'] = captioned_path
                captioned_clip['caption_segments'] = len(caption_segments)
                return captioned_clip
            else:
                return clip

        except Exception as e:
            self.logger.error(f"Error processing captions for clip {clip.get('clip_id', 'unknown')}: {str(e)}")
            return clip

    def _find_relevant_segments(self, vtt_segments: List[Dict[str, Any]],
                              start_time: float, end_time: float) -> List[Dict[str, Any]]:
        """Find VTT segments that overlap with the clip timeframe"""
        relevant_segments = []

        self.logger.info(f"🎯 TIMING DEBUG: Looking for VTT segments between {start_time:.3f}s and {end_time:.3f}s (clip duration: {end_time - start_time:.3f}s)")

        for segment in vtt_segments:
            seg_start = segment['start_time']
            seg_end = segment['end_time']

            # Check for overlap with the original video timeframe
            if seg_start < end_time and seg_end > start_time:
                # Adjust segment timing relative to clip start (0-based)
                adjusted_segment = segment.copy()
                adjusted_segment['original_start'] = seg_start
                adjusted_segment['original_end'] = seg_end
                adjusted_segment['start_time'] = max(0, seg_start - start_time)
                adjusted_segment['end_time'] = min(end_time - start_time, seg_end - start_time)

                # Only include segments with positive duration
                if adjusted_segment['end_time'] > adjusted_segment['start_time']:
                    relevant_segments.append(adjusted_segment)
                    self.logger.info(f"📝 TIMING DEBUG: VTT segment converted: {seg_start:.3f}-{seg_end:.3f}s -> {adjusted_segment['start_time']:.3f}-{adjusted_segment['end_time']:.3f}s | Text: '{segment['text'][:50]}...'")

        self.logger.info(f"✅ Found {len(relevant_segments)} relevant segments for clip timeframe")
        if relevant_segments:
            first_caption = relevant_segments[0]
            last_caption = relevant_segments[-1]
            self.logger.info(f"📊 Caption timing range: {first_caption['start_time']:.3f}s - {last_caption['end_time']:.3f}s")

        return relevant_segments

    def _load_face_regions(self, job_id: str, clip_id: str, clip_start_time: float = 0.0) -> List[FaceRegion]:
        """Load face detection data for intelligent caption positioning

        Args:
            job_id: Job identifier
            clip_id: Clip identifier
            clip_start_time: Start time of the clip in the original video (for timestamp conversion)
        """
        face_regions = []

        try:
            # Look for temporal tracking data
            tracking_path = os.path.join("output", job_id, "temporal_tracking", f"{clip_id}_tracking.json")

            if os.path.exists(tracking_path):
                with open(tracking_path, 'r') as f:
                    tracking_data = json.load(f)

                for frame_data in tracking_data.get('tracking_data', []):
                    absolute_timestamp = frame_data.get('timestamp', 0)
                    primary_face = frame_data.get('primary_face')

                    if primary_face:
                        # CRITICAL FIX: Convert absolute timestamp to clip-relative timestamp
                        # This ensures face regions use the same time reference as captions
                        clip_relative_timestamp = absolute_timestamp - clip_start_time

                        # Only include faces that fall within the clip timeframe
                        if clip_relative_timestamp >= 0:
                            face_regions.append(FaceRegion(
                                x=primary_face['x'],
                                y=primary_face['y'],
                                width=primary_face['width'],
                                height=primary_face['height'],
                                timestamp=clip_relative_timestamp  # Now using clip-relative time
                            ))

                self.logger.info(f"🎭 TIMING DEBUG: Loaded {len(face_regions)} face regions for clip {clip_id} (converted to clip-relative timestamps)")
                if face_regions:
                    min_time = min(f.timestamp for f in face_regions)
                    max_time = max(f.timestamp for f in face_regions)
                    self.logger.info(f"🎭 TIMING DEBUG: Face region time range: {min_time:.3f}s - {max_time:.3f}s (clip-relative)")
                    # Show a few sample face regions for debugging
                    for i, face in enumerate(face_regions[:3]):
                        self.logger.info(f"🎭 TIMING DEBUG: Face {i+1}: timestamp={face.timestamp:.3f}s, position=({face.x}, {face.y}), size=({face.width}x{face.height})")
                else:
                    self.logger.info(f"🎭 TIMING DEBUG: No face regions found for clip {clip_id}")

        except Exception as e:
            self.logger.warning(f"Could not load face regions for clip {clip_id}: {str(e)}")

        return face_regions

    def _process_segments_for_captions(self, segments: List[Dict[str, Any]],
                                     clip_start_time: float, face_regions: List[FaceRegion],
                                     params: Dict[str, Any]) -> List[CaptionSegment]:
        """Process transcript segments into optimized caption segments with language detection"""
        caption_segments = []

        # Detect language from all segments combined for consistency
        all_text = ' '.join(segment.get('text', '') for segment in segments)
        language_analysis = None

        if CAPTION_LANGUAGE_DETECTION and all_text.strip():
            language_analysis = language_detector.analyze_text(all_text)
            self.logger.info(f"🌐 Language detected: {language_analysis['language']} "
                           f"(confidence: {language_analysis['confidence']:.2f}, "
                           f"script: {language_analysis['primary_script']}, "
                           f"font preference: {language_analysis['font_preference']})")

        for segment in segments:
            text = segment['text'].strip()
            if not text:
                continue

            # Apply smart word wrapping to restrict to max 2 lines
            lines = self._smart_word_wrap(text, params, language_analysis)

            # Calculate optimal positioning
            position_y = self._calculate_optimal_position(
                segment['start_time'], face_regions, params
            )

            # Calculate caption area dimensions
            width, height = self._calculate_caption_dimensions(lines, params, language_analysis)

            caption_segment = CaptionSegment(
                start_time=segment['start_time'],
                end_time=segment['end_time'],
                text=text,
                lines=lines,
                position_y=position_y,
                width=width,
                height=height
            )

            # Add language information to segment for font selection
            if language_analysis:
                caption_segment.language_info = language_analysis

            caption_segments.append(caption_segment)

        return caption_segments

    def _smart_word_wrap(self, text: str, params: Dict[str, Any], language_info: Optional[Dict] = None) -> List[str]:
        """Apply intelligent word wrapping with 2-line maximum and dynamic font sizing"""
        max_lines = CAPTION_MAX_LINES
        algorithm = CAPTION_WORD_WRAP_ALGORITHM

        # Calculate optimal font size for this text
        font_size = self._calculate_optimal_font_size(text, language_info)

        # Estimate characters per line based on video width and calculated font size
        video_width = VIDEO_WIDTH
        caption_width = int(video_width * CAPTION_WIDTH_PERCENT / 100)

        # Use improved character width calculation
        char_width = font_size * CAPTION_CHAR_WIDTH_RATIO
        chars_per_line = int(caption_width / char_width)

        words = text.split()

        if algorithm == "smart":
            return self._smart_wrap_algorithm(words, chars_per_line, max_lines)
        elif algorithm == "balanced":
            return self._balanced_wrap_algorithm(words, chars_per_line, max_lines)
        else:
            return self._simple_wrap_algorithm(words, chars_per_line, max_lines)

    def _smart_wrap_algorithm(self, words: List[str], chars_per_line: int, max_lines: int) -> List[str]:
        """Smart wrapping that tries to balance line lengths and avoid orphans"""
        if not words:
            return []

        # If text is short enough for one line, use it
        full_text = ' '.join(words)
        if len(full_text) <= chars_per_line:
            return [full_text]

        # For two lines, try to balance the lengths
        if max_lines >= 2:
            mid_point = len(words) // 2

            # Try different split points around the middle
            best_split = mid_point
            best_balance = float('inf')

            for split_point in range(max(1, mid_point - 2), min(len(words), mid_point + 3)):
                line1 = ' '.join(words[:split_point])
                line2 = ' '.join(words[split_point:])

                if len(line1) <= chars_per_line and len(line2) <= chars_per_line:
                    balance = abs(len(line1) - len(line2))
                    if balance < best_balance:
                        best_balance = balance
                        best_split = split_point

            line1 = ' '.join(words[:best_split])
            line2 = ' '.join(words[best_split:])

            if len(line1) <= chars_per_line and len(line2) <= chars_per_line:
                return [line1, line2]

        # Fallback to simple wrapping
        return self._simple_wrap_algorithm(words, chars_per_line, max_lines)

    def _balanced_wrap_algorithm(self, words: List[str], chars_per_line: int, max_lines: int) -> List[str]:
        """Balanced wrapping that minimizes line length variance"""
        if not words:
            return []

        full_text = ' '.join(words)
        if len(full_text) <= chars_per_line:
            return [full_text]

        # For balanced approach, aim for equal line lengths
        target_length = len(full_text) // max_lines

        lines = []
        current_line = []
        current_length = 0

        for word in words:
            word_length = len(word) + (1 if current_line else 0)  # +1 for space

            if current_length + word_length <= chars_per_line and len(lines) < max_lines - 1:
                current_line.append(word)
                current_length += word_length
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                current_line = [word]
                current_length = len(word)

        if current_line:
            lines.append(' '.join(current_line))

        return lines[:max_lines]

    def _simple_wrap_algorithm(self, words: List[str], chars_per_line: int, max_lines: int) -> List[str]:
        """Simple word wrapping algorithm"""
        lines = []
        current_line = []
        current_length = 0

        for word in words:
            word_length = len(word) + (1 if current_line else 0)  # +1 for space

            if current_length + word_length <= chars_per_line:
                current_line.append(word)
                current_length += word_length
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    if len(lines) >= max_lines:
                        break
                current_line = [word]
                current_length = len(word)

        if current_line and len(lines) < max_lines:
            lines.append(' '.join(current_line))

        return lines[:max_lines]

    def _calculate_optimal_position(self, timestamp: float, face_regions: List[FaceRegion],
                                  params: Dict[str, Any]) -> int:
        """Calculate optimal Y position for captions based on face detection"""
        video_height = VIDEO_HEIGHT
        margin_from_edge = CAPTION_MARGIN_FROM_EDGE
        face_avoidance_margin = CAPTION_FACE_AVOIDANCE_MARGIN

        # Default position based on CAPTION_POSITION setting
        if CAPTION_POSITION == "top":
            default_y = margin_from_edge
        elif CAPTION_POSITION == "bottom":
            default_y = video_height - margin_from_edge - (CAPTION_FONT_SIZE * CAPTION_LINE_HEIGHT_MULTIPLIER * CAPTION_MAX_LINES)
        else:  # center
            default_y = video_height // 2 - (CAPTION_FONT_SIZE * CAPTION_LINE_HEIGHT_MULTIPLIER * CAPTION_MAX_LINES) // 2

        # If face-aware positioning is disabled, use default
        if not CAPTION_DYNAMIC_POSITIONING or not face_regions:
            return int(default_y)

        # Find faces near this timestamp
        relevant_faces = [
            face for face in face_regions
            if abs(face.timestamp - timestamp) <= 1.0  # Within 1 second
        ]

        self.logger.debug(f"🎭 POSITIONING DEBUG: Caption at {timestamp:.3f}s, found {len(relevant_faces)} nearby faces")

        if not relevant_faces:
            self.logger.debug(f"🎭 POSITIONING DEBUG: No nearby faces, using default position: {int(default_y)}")
            return int(default_y)

        # Calculate caption height
        caption_height = int(CAPTION_FONT_SIZE * CAPTION_LINE_HEIGHT_MULTIPLIER * CAPTION_MAX_LINES +
                           CAPTION_PADDING_VERTICAL * 2)

        # Try different positions to avoid faces
        positions_to_try = [
            margin_from_edge,  # Top
            video_height - margin_from_edge - caption_height,  # Bottom
            video_height // 3 - caption_height // 2,  # Upper third
            2 * video_height // 3 - caption_height // 2,  # Lower third
        ]

        for position_y in positions_to_try:
            # Check if this position conflicts with any face
            conflicts = False
            for face in relevant_faces:
                face_top = face.y - face_avoidance_margin
                face_bottom = face.y + face.height + face_avoidance_margin
                caption_bottom = position_y + caption_height

                # Check for overlap
                if not (caption_bottom < face_top or position_y > face_bottom):
                    conflicts = True
                    break

            if not conflicts:
                self.logger.debug(f"🎭 POSITIONING DEBUG: Found conflict-free position: {int(position_y)}")
                return int(position_y)

        # If all positions conflict, use the one with least overlap
        self.logger.debug(f"🎭 POSITIONING DEBUG: All positions conflict with faces, using default: {int(default_y)}")
        return int(default_y)

    def _calculate_caption_dimensions(self, lines: List[str], params: Dict[str, Any],
                                    language_info: Optional[Dict] = None) -> Tuple[int, int]:
        """Calculate caption area dimensions based on text and font settings with dynamic sizing"""
        if not lines:
            return 0, 0

        # Calculate optimal font size for the text
        full_text = " ".join(lines)
        font_size = self._calculate_optimal_font_size(full_text, language_info)

        # Calculate line height with improved spacing control
        line_height = self._calculate_line_height(font_size)

        padding_h = CAPTION_PADDING_HORIZONTAL
        padding_v = CAPTION_PADDING_VERTICAL

        # Calculate width based on longest line with improved accuracy
        max_line_length = max(len(line) for line in lines)
        text_width = self._calculate_text_width(lines[0] if len(lines) == 1 else max(lines, key=len), font_size)
        total_width = text_width + (padding_h * 2)

        # Calculate height based on number of lines with improved spacing
        if len(lines) == 1:
            text_height = font_size
        else:
            # For multiple lines, use line height for spacing
            text_height = font_size + (len(lines) - 1) * line_height

        total_height = text_height + (padding_v * 2)

        self.logger.debug(f"📐 Caption dimensions: {total_width}x{total_height} (font: {font_size}px, lines: {len(lines)})")
        return total_width, total_height

    def _apply_captions_to_video(self, video_path: str, caption_segments: List[CaptionSegment],
                               job_id: str, clip_id: str, params: Dict[str, Any]) -> Optional[str]:
        """Apply captions to video using FFmpeg"""
        try:
            # Create output path
            output_dir = os.path.join("output", job_id, "captioned")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"{clip_id}_captioned.mp4")

            # Generate subtitle file for FFmpeg
            subtitle_path = self._generate_subtitle_file(caption_segments, job_id, clip_id)

            if not subtitle_path:
                self.logger.warning(f"Failed to generate subtitle file for clip {clip_id}")
                return None

            # Build FFmpeg command with subtitle overlay
            input_stream = ffmpeg.input(video_path)

            # Create subtitle filter
            subtitle_filter = self._build_subtitle_filter(subtitle_path, params)

            # Apply subtitle overlay with explicit font directory
            if 'fontsdir' in subtitle_filter:
                # Use subtitles filter with fonts directory
                video_with_subs = input_stream.video.filter('subtitles', subtitle_path, **subtitle_filter)
            else:
                # Fallback to basic subtitles filter
                video_with_subs = input_stream.video.filter('subtitles', subtitle_path)

            # Output with same audio
            output = ffmpeg.output(
                video_with_subs, input_stream.audio, output_path,
                vcodec='libx264',
                acodec='aac',
                preset='fast',
                crf=23,
                movflags='+faststart'
            )

            # Run FFmpeg
            ffmpeg.run(output, quiet=True, overwrite_output=True)

            if os.path.exists(output_path):
                self.logger.info(f"✅ Successfully applied captions to {clip_id}")
                return output_path
            else:
                self.logger.error(f"Failed to create captioned video for {clip_id}")
                return None

        except Exception as e:
            self.logger.error(f"Error applying captions to video {clip_id}: {str(e)}")
            return None

    def _generate_subtitle_file(self, caption_segments: List[CaptionSegment],
                              job_id: str, clip_id: str) -> Optional[str]:
        """Generate ASS subtitle file for FFmpeg with advanced styling"""
        try:
            # Create subtitles directory
            subtitles_dir = os.path.join("output", job_id, "subtitles")
            os.makedirs(subtitles_dir, exist_ok=True)
            subtitle_path = os.path.join(subtitles_dir, f"{clip_id}.ass")

            # Generate ASS content with advanced styling
            ass_content = self._generate_ass_content(caption_segments)

            with open(subtitle_path, 'w', encoding='utf-8') as f:
                f.write(ass_content)

            return subtitle_path

        except Exception as e:
            self.logger.error(f"Error generating subtitle file: {str(e)}")
            return None

    def _generate_ass_content(self, caption_segments: List[CaptionSegment]) -> str:
        """Generate ASS subtitle content with advanced styling and language support"""

        # Determine language and font preference from segments
        font_preference = "default"
        if caption_segments and CAPTION_LANGUAGE_DETECTION:
            # Use language info from first segment (all segments should have same language)
            first_segment = caption_segments[0]
            if hasattr(first_segment, 'language_info') and first_segment.language_info:
                font_preference = first_segment.language_info.get('font_preference', 'default')
                self.logger.info(f"🔤 Using font preference '{font_preference}' for ASS generation")

        # Get appropriate font for the detected language
        font_path = self._get_font_path(font_preference)

        # For ASS files, we need to handle font specification carefully
        if font_path:
            # Use absolute path for better font resolution
            abs_font_path = os.path.abspath(font_path)
            font_name = os.path.splitext(os.path.basename(font_path))[0]

            # Copy font to a location accessible by FFmpeg if needed
            self._ensure_font_accessibility(abs_font_path, font_name)
        else:
            font_name = CAPTION_FONT
            abs_font_path = None

        self.logger.info(f"🔤 ASS font name: {font_name} (from path: {font_path})")

        # Calculate optimal font size for the content
        all_text = " ".join(segment.text for segment in caption_segments)
        optimal_font_size = self._calculate_optimal_font_size(all_text,
                                                            caption_segments[0].language_info if caption_segments else None)

        # Determine ASS alignment based on CAPTION_POSITION setting
        # ASS alignment values: 1=bottom-left, 2=bottom-center, 3=bottom-right,
        #                      4=left-center, 5=center, 6=right-center,
        #                      7=top-left, 8=top-center, 9=top-right
        if CAPTION_POSITION == "top":
            alignment = 8  # Top center
        elif CAPTION_POSITION == "bottom":
            alignment = 2  # Bottom center
        else:  # center
            alignment = 5  # Center

        # Calculate vertical scaling for fine-grained spacing control
        scale_y = CAPTION_SCALE_Y if CAPTION_USE_SCALE_Y else 100

        # Calculate line spacing (can be minimal when using ScaleY)
        if CAPTION_USE_SCALE_Y:
            line_spacing = 0  # Let ScaleY handle vertical spacing
        else:
            line_spacing = self._calculate_line_height(optimal_font_size) - optimal_font_size

        # Calculate precise margins for positioning control
        if CAPTION_POSITION_FINE_TUNE:
            margin_l = CAPTION_MARGIN_L
            margin_r = CAPTION_MARGIN_R
            margin_v = CAPTION_MARGIN_V
        else:
            # Fallback to original padding values
            margin_l = CAPTION_PADDING_HORIZONTAL
            margin_r = CAPTION_PADDING_HORIZONTAL
            margin_v = CAPTION_PADDING_VERTICAL

        self.logger.info(f"🔤 Using font size: {optimal_font_size}px with ScaleY: {scale_y}% (line spacing: {line_spacing}px)")
        self.logger.info(f"📐 Margins: L={margin_l}px, R={margin_r}px, V={margin_v}px (position: {CAPTION_POSITION})")

        # ASS header with styling and language-appropriate font
        ass_header = f"""[Script Info]
Title: Highlight Video Captions
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{optimal_font_size},&H00FFFFFF,&H000000FF,&H00000000,&H80000000,1,0,0,0,100,{scale_y},{line_spacing},0,1,{CAPTION_STROKE_WIDTH},{CAPTION_SHADOW_OFFSET_X},{alignment},{margin_l},{margin_r},{margin_v},1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""

        # Calculate default position for comparison
        if CAPTION_POSITION == "top":
            default_position_y = CAPTION_MARGIN_FROM_EDGE
        elif CAPTION_POSITION == "bottom":
            default_position_y = VIDEO_HEIGHT - CAPTION_MARGIN_FROM_EDGE - (CAPTION_FONT_SIZE * CAPTION_LINE_HEIGHT_MULTIPLIER * CAPTION_MAX_LINES)
        else:  # center
            default_position_y = VIDEO_HEIGHT // 2 - (CAPTION_FONT_SIZE * CAPTION_LINE_HEIGHT_MULTIPLIER * CAPTION_MAX_LINES) // 2

        # Generate dialogue lines
        dialogue_lines = []
        for segment in caption_segments:
            start_time = self._format_ass_timestamp(segment.start_time)
            end_time = self._format_ass_timestamp(segment.end_time)

            # Join lines with line break
            text = "\\N".join(segment.lines)

            # Add positioning if needed - use \pos() for precise positioning
            # when face-aware positioning is used or position differs from default
            if abs(segment.position_y - default_position_y) > 5:  # Allow small tolerance for rounding
                text = f"{{\\pos({VIDEO_WIDTH//2},{segment.position_y})}}{text}"

            dialogue_line = f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{text}"
            dialogue_lines.append(dialogue_line)

        return ass_header + "\n".join(dialogue_lines)

    def _format_ass_timestamp(self, seconds: float) -> str:
        """Format timestamp for ASS format (H:MM:SS.cc)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}:{minutes:02d}:{secs:05.2f}"

    def _build_subtitle_filter(self, subtitle_path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Build subtitle filter parameters for FFmpeg with language-appropriate font"""
        # For better font handling, we need to specify the fonts directory to FFmpeg
        # This ensures FFmpeg can find our project fonts
        filter_params = {}

        # Add fonts directory if we have project fonts
        fonts_dir = os.path.abspath("assets/fonts")
        if os.path.exists(fonts_dir):
            filter_params['fontsdir'] = fonts_dir
            self.logger.info(f"🔤 FFmpeg fonts directory: {fonts_dir}")

        return filter_params

    def _ensure_font_accessibility(self, font_path: str, font_name: str) -> None:
        """Ensure font is accessible to FFmpeg by copying to system location if needed"""
        try:
            # Create a temporary fonts directory in the output for this session
            temp_fonts_dir = "/tmp/vido_fonts"
            os.makedirs(temp_fonts_dir, exist_ok=True)

            # Copy font to temp location with a simple name
            temp_font_path = os.path.join(temp_fonts_dir, f"{font_name}.ttf")

            if not os.path.exists(temp_font_path):
                import shutil
                shutil.copy2(font_path, temp_font_path)
                self.logger.info(f"🔤 Copied font to accessible location: {temp_font_path}")

            # Also try to install to user fonts directory
            user_fonts_dir = os.path.expanduser("~/.local/share/fonts")
            os.makedirs(user_fonts_dir, exist_ok=True)

            user_font_path = os.path.join(user_fonts_dir, f"{font_name}.ttf")
            if not os.path.exists(user_font_path):
                import shutil
                shutil.copy2(font_path, user_font_path)
                self.logger.info(f"🔤 Installed font to user directory: {user_font_path}")

                # Refresh font cache
                os.system("fc-cache -f -v > /dev/null 2>&1")

        except Exception as e:
            self.logger.warning(f"Could not ensure font accessibility: {str(e)}")

    def _calculate_optimal_font_size(self, text: str, language_info: Optional[Dict] = None) -> int:
        """Calculate optimal font size based on text content and language"""
        base_size = CAPTION_FONT_SIZE if CAPTION_AUTO_SCALE else CAPTION_FONT_SIZE

        # Apply language-specific scaling
        if language_info and language_info.get('font_preference') in CAPTION_LANGUAGE_SCALING:
            language_scale = CAPTION_LANGUAGE_SCALING[language_info['font_preference']]
            base_size = int(base_size * language_scale)

        # Auto-scale based on text length if enabled
        if CAPTION_AUTO_SCALE:
            text_length = len(text)

            # Scale down for very long text to prevent overflow
            if text_length > 80:
                scale_factor = 0.85
            elif text_length > 60:
                scale_factor = 0.9
            elif text_length > 40:
                scale_factor = 0.95
            else:
                scale_factor = 1.0

            base_size = int(base_size * scale_factor)

        # Ensure font size is within bounds
        font_size = max(CAPTION_MIN_FONT_SIZE, min(CAPTION_MAX_FONT_SIZE, base_size))

        self.logger.debug(f"🔤 Calculated font size: {font_size}px for text length: {len(text)}")
        return font_size

    def _calculate_line_height(self, font_size: int) -> int:
        """Calculate line height with independent spacing control"""
        # Use fixed line spacing instead of multiplier for better control
        if hasattr(self, 'CAPTION_LINE_SPACING') and CAPTION_LINE_SPACING:
            line_height = font_size + CAPTION_LINE_SPACING
        else:
            # Fallback to multiplier method
            line_height = int(font_size * CAPTION_LINE_HEIGHT_MULTIPLIER)

        return line_height

    def _calculate_text_width(self, text: str, font_size: int) -> int:
        """Calculate text width based on character count and font size"""
        # Use improved character width ratio for better accuracy
        char_width = font_size * CAPTION_CHAR_WIDTH_RATIO
        return int(len(text) * char_width)

    def _apply_captions_with_font_fallback(self, video_path: str, caption_segments: List[CaptionSegment],
                                         job_id: str, clip_id: str, params: Dict[str, Any]) -> Optional[str]:
        """Apply captions with multiple fallback methods for font rendering"""
        # Try the standard ASS method first
        result = self._apply_captions_to_video(video_path, caption_segments, job_id, clip_id, params)

        if result and os.path.exists(result):
            # Verify the captions are actually visible by checking file size
            # If the captioned video is significantly smaller, captions might have failed
            original_size = os.path.getsize(video_path)
            captioned_size = os.path.getsize(result)

            # If captioned video is much smaller, something went wrong
            if captioned_size < original_size * 0.8:
                self.logger.warning(f"Captioned video seems too small, trying fallback method")
                return self._apply_captions_with_drawtext_fallback(video_path, caption_segments, job_id, clip_id, params)

            return result

        # If ASS method failed, try drawtext fallback
        self.logger.warning(f"ASS subtitle method failed for {clip_id}, trying drawtext fallback")
        return self._apply_captions_with_drawtext_fallback(video_path, caption_segments, job_id, clip_id, params)

    def _apply_captions_with_drawtext_fallback(self, video_path: str, caption_segments: List[CaptionSegment],
                                             job_id: str, clip_id: str, params: Dict[str, Any]) -> Optional[str]:
        """Apply captions using FFmpeg drawtext filter as fallback"""
        try:
            # Create output path
            output_dir = os.path.join("output", job_id, "captioned")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"{clip_id}_captioned.mp4")

            # Get font path for the detected language
            font_preference = "default"
            if caption_segments and CAPTION_LANGUAGE_DETECTION:
                first_segment = caption_segments[0]
                if hasattr(first_segment, 'language_info') and first_segment.language_info:
                    font_preference = first_segment.language_info.get('font_preference', 'default')

            font_path = self._get_font_path(font_preference)
            if not font_path:
                font_path = self._get_font_path("default")

            # Calculate optimal font size for drawtext
            all_text = " ".join(segment.text for segment in caption_segments)
            language_info = caption_segments[0].language_info if caption_segments and hasattr(caption_segments[0], 'language_info') else None
            optimal_font_size = self._calculate_optimal_font_size(all_text, language_info)

            # Scale font size for drawtext (drawtext uses different scaling)
            drawtext_font_size = optimal_font_size * 2  # Adjusted scaling for drawtext

            # Build drawtext filters for each caption segment
            input_stream = ffmpeg.input(video_path)
            video_stream = input_stream.video

            for segment in caption_segments:
                # Create drawtext filter for this segment
                text = " ".join(segment.lines)  # Join lines with space instead of \N

                # Escape special characters for drawtext
                text = text.replace("'", "\\'").replace(":", "\\:")

                # Calculate position (drawtext uses different coordinate system)
                x = VIDEO_WIDTH // 2  # Center horizontally
                y = segment.position_y

                # Apply drawtext filter with improved sizing
                video_stream = video_stream.filter(
                    'drawtext',
                    text=text,
                    fontfile=font_path,
                    fontsize=drawtext_font_size,
                    fontcolor='white',
                    borderw=CAPTION_STROKE_WIDTH,
                    bordercolor='black',
                    x=f'(w-text_w)/2',  # Center horizontally
                    y=y,
                    enable=f'between(t,{segment.start_time},{segment.end_time})'
                )

            # Output with same audio
            output = ffmpeg.output(
                video_stream, input_stream.audio, output_path,
                vcodec='libx264',
                acodec='aac',
                preset='fast',
                crf=23,
                movflags='+faststart'
            )

            # Run FFmpeg
            ffmpeg.run(output, quiet=True, overwrite_output=True)

            if os.path.exists(output_path):
                self.logger.info(f"✅ Successfully applied captions using drawtext fallback for {clip_id}")
                return output_path
            else:
                self.logger.error(f"Failed to create captioned video using drawtext fallback for {clip_id}")
                return None

        except Exception as e:
            self.logger.error(f"Error applying captions with drawtext fallback: {str(e)}")
            return None


# Create a task instance
caption_composer = CaptionComposer()

def compose_captions(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the caption_composer task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return caption_composer.run(job_id, *args, **kwargs)
