#!/usr/bin/env python3
"""
Re-framer Task for the Smart Video Highlight Generator Pipeline

This is a wrapper that uses the new modular reframing package for improved
maintainability and better organization.
"""

from typing import Dict, Any

from pipeline.tasks.base_task import BaseTask
from reframing.core.reframer import <PERSON>frame<PERSON> as ModularReframer
from reframing.config.settings import ReframingConfig
from config.settings import PIPELINE_OUTPUT_DIR


class Reframer(BaseTask):
    """
    Stage-08: Re-framer (Wrapper for Modular Reframer)

    This class serves as a wrapper around the new modular reframing package,
    maintaining compatibility with the existing pipeline while providing
    improved functionality through the modular structure.
    """

    task_name = "reframer"
    requires_gpu = True

    def __init__(self):
        super().__init__()
        # Initialize the modular reframer with pipeline configuration
        config = ReframingConfig.from_env()
        self.modular_reframer = ModularReframer(config=config, pipeline_output_dir=PIPELINE_OUTPUT_DIR)

    def process(self, job_id: str, clip_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the reframing task using the modular reframer

        Args:
            job_id: Unique identifier for the job
            clip_result: Results from the clip renderer
            params: Additional parameters for the task

        Returns:
            Dictionary containing re-framed clip results
        """
        # Delegate to the modular reframer
        return self.modular_reframer.process(job_id, clip_result, params)

    def process_with_temporal_tracking(self, job_id: str, clip_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the reframing task using temporal face tracking for perfect highlights

        Args:
            job_id: Unique identifier for the job
            clip_result: Results from the clip renderer
            params: Additional parameters including temporal tracking settings

        Returns:
            Dictionary containing re-framed clip results with temporal tracking
        """
        # Delegate to the modular reframer's temporal tracking method
        return self.modular_reframer.process_with_temporal_tracking(job_id, clip_result, params)


# Create a task instance
reframer = Reframer()


def reframe_clips(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the reframer task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return reframer.run(job_id, *args, **kwargs)
