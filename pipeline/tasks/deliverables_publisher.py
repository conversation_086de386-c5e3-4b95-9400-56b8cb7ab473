#!/usr/bin/env python3
"""
Deliverables Publisher Task for the Smart Video Highlight Generator Pipeline


This version uses direct function calls instead  for task execution.
"""

import os
import json
import tempfile
import subprocess
from typing import Dict, Any, List

import ffmpeg

from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR

class DeliverablesPublisher(BaseTask):
    """
    Stage-10: Deliverables Publisher

    Concatenates .ts segments into final output using 2-pass H.264 encoding,
    generates supplementary assets like thumbnails and subtitle files, and
    uploads all deliverables with secure access.
    """

    task_name = "deliverables_publisher"
    requires_gpu = False

    def run(self, job_id: str, caption_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Publish final deliverables

        Args:
            job_id: Unique identifier for the job
            caption_result: Results from the caption composer
            params: Additional parameters for the task

        Returns:
            Dictionary containing published deliverables
        """
        self.logger.info(f"Publishing deliverables for job {job_id}")

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Create output directory
        output_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id, "highlights")
        os.makedirs(output_dir, exist_ok=True)

        # Get clips from caption result
        clips = caption_result.get('metadata', {}).get('clips', [])
        if not clips:
            raise ValueError("No clips found in caption result")

        # Get parameters
        output_format = params.get('output_format', 'original')
        add_progress_bar = params.get('add_progress_bar', True)
        chronological_order = params.get('chronological_order', True)
        separate_clips = params.get('separate_clips', False)

        # Sort clips by start time if chronological order is requested
        if chronological_order:
            clips.sort(key=lambda x: x.get('start_time', 0))
        else:
            # Otherwise, sort by score (descending)
            clips.sort(key=lambda x: x.get('score', 0), reverse=True)

        # Create final video only if separate_clips is False
        final_video_path = None
        if not separate_clips:
            final_video_path = os.path.join(output_dir, f"highlight_{job_id}_{output_format}.mp4")
            self._create_final_video(clips, final_video_path, add_progress_bar)
            self.logger.info(f"Created concatenated final video: {final_video_path}")
        else:
            self.logger.info(f"Skipping concatenation - keeping clips separate as requested")

        # Generate thumbnails only if we have a final video
        thumbnail_paths = []
        if final_video_path:
            thumbnail_paths = self._generate_thumbnails(final_video_path, output_dir)
        else:
            self.logger.info("Skipping thumbnail generation - no concatenated video created")

        # Generate master SRT subtitle file
        srt_path = self._generate_master_srt(clips, output_dir)

        # Create deliverables manifest
        manifest = {
            'final_video': final_video_path,
            'thumbnails': thumbnail_paths,
            'subtitle': srt_path,
            'clips': [clip.get('captioned_path') for clip in clips],
            'metadata': {
                'job_id': job_id,
                'output_format': output_format,
                'clip_count': len(clips),
                'total_duration': sum(clip.get('duration', 0) for clip in clips),
                'chronological_order': chronological_order,
                'add_progress_bar': add_progress_bar,
                'separate_clips': separate_clips
            }
        }

        # Save manifest to file
        manifest_path = os.path.join(output_dir, "deliverables.json")
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)

        # Create result with deliverables data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'output_dir': output_dir,
            'final_video_path': final_video_path,
            'thumbnail_paths': thumbnail_paths,
            'srt_path': srt_path,
            'manifest_path': manifest_path,
            'metadata': manifest['metadata']
        }

        # Save state for idempotency
        self.save_state(job_id, result, self.task_name)

        return result

    def _create_final_video(self, clips: List[Dict[str, Any]], output_path: str, add_progress_bar: bool) -> None:
        """
        Create final video by concatenating clips

        Args:
            clips: List of clips to concatenate
            output_path: Path to save the final video
            add_progress_bar: Whether to add a progress bar
        """
        # Create a temporary file with the list of clips
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            concat_list_path = f.name
            for clip in clips:
                clip_path = clip.get('captioned_path', clip.get('reframed_path', clip.get('file_path')))
                if clip_path:
                    # Convert to absolute path
                    if not os.path.isabs(clip_path):
                        clip_path = os.path.abspath(clip_path)

                    if os.path.exists(clip_path):
                        f.write(f"file '{clip_path}'\n")

        try:
            # Create filter complex for progress bar if requested
            filter_complex = None
            if add_progress_bar:
                # Calculate progress bar parameters
                total_duration = sum(clip.get('duration', 0) for clip in clips)
                progress_bar_height = 10  # Height of progress bar in pixels

                # Create filter complex for progress bar
                filter_complex = (
                    f"[0:v]drawbox=x=0:y=0:w=iw:h={progress_bar_height}:color=black:t=fill[bg];"
                    f"[bg]drawbox=x=0:y=0:w=iw*t/{total_duration}:h={progress_bar_height}:color=white:t=fill[v]"
                )

            # Use ffmpeg to concatenate clips and encode final video
            if filter_complex:
                # When using filter complex, we need to re-encode
                ffmpeg_args = [
                    'ffmpeg',
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', concat_list_path,
                    '-filter_complex', filter_complex,
                    '-map', '[v]',
                    '-map', '0:a?',  # Use '?' to make audio mapping optional
                    '-c:v', 'libx264',  # Re-encode video with H.264
                    '-c:a', 'aac',      # Re-encode audio with AAC
                    '-preset', 'fast',  # Fast encoding preset
                    '-crf', '23',       # Constant Rate Factor (quality)
                    '-movflags', '+faststart',
                    '-y',
                    output_path
                ]
            else:
                # When not using filter complex, we can copy streams for speed
                ffmpeg_args = [
                    'ffmpeg',
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', concat_list_path,
                    '-c', 'copy',                # Copy streams without re-encoding for speed
                    '-movflags', '+faststart',
                    '-y',
                    output_path
                ]

            # Run ffmpeg command
            subprocess.run(ffmpeg_args, check=True)

        finally:
            # Clean up temporary file
            os.unlink(concat_list_path)

    def _generate_thumbnails(self, video_path: str, output_dir: str, count: int = 6) -> List[str]:
        """
        Generate thumbnails from the final video

        Args:
            video_path: Path to the final video
            output_dir: Directory to save thumbnails
            count: Number of thumbnails to generate

        Returns:
            List of thumbnail paths
        """
        # Create thumbnails directory
        thumbnails_dir = os.path.join(output_dir, "thumbnails")
        os.makedirs(thumbnails_dir, exist_ok=True)

        # Get video duration
        probe = ffmpeg.probe(video_path)
        duration = float(probe['format']['duration'])

        # Calculate thumbnail timestamps
        timestamps = [duration * i / (count + 1) for i in range(1, count + 1)]

        # Generate thumbnails
        thumbnail_paths = []

        for i, timestamp in enumerate(timestamps):
            # Define thumbnail path
            thumbnail_path = os.path.join(thumbnails_dir, f"thumbnail_{i+1:02d}.jpg")

            # Use ffmpeg to extract thumbnail
            (
                ffmpeg
                .input(video_path, ss=timestamp)
                .output(
                    thumbnail_path,
                    vframes=1,
                    format='image2',
                    vcodec='mjpeg',
                    q=2  # High quality
                )
                .global_args('-y')  # Overwrite output file if it exists
                .run(quiet=True, overwrite_output=True)
            )

            thumbnail_paths.append(thumbnail_path)

        return thumbnail_paths

    def _generate_master_srt(self, clips: List[Dict[str, Any]], output_dir: str) -> str:
        """
        Generate a master SRT subtitle file

        Args:
            clips: List of clips
            output_dir: Directory to save the SRT file

        Returns:
            Path to the SRT file
        """
        # Define SRT path
        srt_path = os.path.join(output_dir, "subtitles.srt")

        # Generate SRT content
        current_time = 0.0

        with open(srt_path, 'w') as f:
            for i, clip in enumerate(clips):
                # Get clip duration and caption
                duration = clip.get('duration', 0)
                caption = clip.get('caption', '')

                if caption:
                    # Calculate start and end times
                    start_time = current_time
                    end_time = current_time + duration

                    # Format times as HH:MM:SS,mmm
                    start_formatted = self._format_srt_timestamp(start_time)
                    end_formatted = self._format_srt_timestamp(end_time)

                    # Write subtitle entry
                    f.write(f"{i+1}\n")
                    f.write(f"{start_formatted} --> {end_formatted}\n")
                    f.write(f"{caption}\n\n")

                # Update current time
                current_time += duration

        return srt_path

    def _format_srt_timestamp(self, seconds: float) -> str:
        """
        Format seconds as HH:MM:SS,mmm for SRT

        Args:
            seconds: Time in seconds

        Returns:
            Formatted timestamp string
        """
        hours = int(seconds / 3600)
        minutes = int((seconds % 3600) / 60)
        seconds = seconds % 60

        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}".replace('.', ',')
# Create a task instance
deliverables_publisher = DeliverablesPublisher()

def publish_deliverables(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the deliverables_publisher task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return deliverables_publisher.run(job_id, *args, **kwargs)
