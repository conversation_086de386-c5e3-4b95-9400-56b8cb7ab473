#!/usr/bin/env python3
"""
Highlight Window Generator Task for the Smart Video Highlight Generator Pipeline


This version uses direct function calls instead  for task execution.
"""

import os
import json
import numpy as np
from typing import Dict, Any, Optional, List, Tuple


from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR, PIPELINE_MIN_HIGHLIGHT_SCORE

class HighlightWindowGenerator(BaseTask):
    """
    Stage-05: Highlight Window Generator

    Creates initial highlight windows around keyword matches, dynamically adjusts window
    boundaries based on audio events, and merges overlapping segments.
    """

    task_name = "highlight_window_generator"
    requires_gpu = False

    def run(self, job_id: str, semantic_index_result: Dict[str, Any], av_event_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate highlight windows based on keyword matches and A/V events

        Args:
            job_id: Unique identifier for the job
            semantic_index_result: Results from the semantic indexer
            av_event_result: Results from the A/V event detector
            params: Additional parameters for the task

        Returns:
            Dictionary containing highlight window results
        """
        self.logger.info(f"Generating highlight windows for job {job_id}")

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Create job directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        windows_dir = os.path.join(job_dir, "windows")
        os.makedirs(windows_dir, exist_ok=True)

        # Get keyword matches from semantic index
        metadata_path = semantic_index_result.get('metadata_path')
        if not metadata_path or not os.path.exists(metadata_path):
            raise ValueError(f"Invalid metadata path from semantic indexer: {metadata_path}")

        # Load segment metadata
        with open(metadata_path, 'r') as f:
            segment_metadata = json.load(f)

        # Get A/V events timeline
        timeline_path = av_event_result.get('timeline_path')
        if not timeline_path or not os.path.exists(timeline_path):
            raise ValueError(f"Invalid timeline path from A/V event detector: {timeline_path}")

        # Load timeline events
        with open(timeline_path, 'r') as f:
            timeline_events = json.load(f)

        # Get parameters
        max_duration = params.get('max_duration', 60)  # Default 60 seconds
        context_window = params.get('context_window', 10)  # Default 10 seconds
        pre_buffer = context_window / 2  # Buffer before keyword mention
        post_buffer = context_window / 2  # Buffer after keyword mention

        # Get keywords from params
        keywords = params.get('keywords', [])
        if not keywords:
            self.logger.warning("No keywords provided, using default 'highlight'")
            keywords = ["highlight"]

        # Create initial highlight windows
        highlight_windows = self._create_initial_windows(segment_metadata, keywords, pre_buffer, post_buffer)

        # Adjust windows based on A/V events
        highlight_windows = self._adjust_windows_with_av_events(highlight_windows, timeline_events)

        # Merge overlapping windows
        highlight_windows = self._merge_overlapping_windows(highlight_windows)

        # Limit total duration
        highlight_windows = self._limit_total_duration(highlight_windows, max_duration)

        # Save highlight windows to file
        windows_path = os.path.join(windows_dir, "highlight_windows.json")
        with open(windows_path, 'w') as f:
            json.dump(highlight_windows, f, indent=2)

        # Create result with highlight window data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'windows_path': windows_path,
            'metadata': {
                'window_count': len(highlight_windows),
                'total_duration': sum(window.get('duration', 0) for window in highlight_windows),
                'keywords': keywords
            }
        }

        # Save state for idempotency
        self.save_state(job_id, result, self.task_name)

        return result

    def _create_initial_windows(self, segment_metadata: List[Dict[str, Any]], keywords: List[str],
                               pre_buffer: float, post_buffer: float) -> List[Dict[str, Any]]:
        """
        Create initial highlight windows based on keyword matches

        Args:
            segment_metadata: List of transcript segments with timestamps
            keywords: List of keywords to search for
            pre_buffer: Number of seconds to include before keyword mention
            post_buffer: Number of seconds to include after keyword mention

        Returns:
            List of highlight windows
        """
        highlight_windows = []

        # Convert keywords to lowercase for case-insensitive matching
        keywords_lower = [k.lower() for k in keywords]

        # Iterate through segments
        for i, segment in enumerate(segment_metadata):
            text = segment.get('text', '').lower()
            start_time = segment.get('start', 0)
            end_time = segment.get('end', 0)

            # Check if any keyword is in the segment text
            for keyword in keywords_lower:
                if keyword in text:
                    # Create a window around the keyword mention
                    window_start = max(0, start_time - pre_buffer)
                    window_end = end_time + post_buffer

                    # Calculate base score based on position in text
                    # Keywords at the beginning or middle of a segment are more important
                    keyword_pos = text.find(keyword) / len(text) if len(text) > 0 else 0.5
                    position_score = 1.0 - abs(0.5 - keyword_pos)  # 1.0 for middle, lower for edges

                    # Create window
                    window = {
                        'start_time': float(window_start),
                        'end_time': float(window_end),
                        'duration': float(window_end - window_start),
                        'keyword': keyword,
                        'segment_index': i,
                        'text': segment.get('text', ''),
                        'base_score': float(0.7 + 0.3 * position_score),  # Base score between 0.7 and 1.0
                        'final_score': float(0.7 + 0.3 * position_score),  # Will be adjusted later
                        'source': 'keyword_match'
                    }

                    highlight_windows.append(window)

        return highlight_windows

    def _adjust_windows_with_av_events(self, highlight_windows: List[Dict[str, Any]],
                                      timeline_events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Adjust highlight windows based on A/V events

        Args:
            highlight_windows: List of initial highlight windows
            timeline_events: List of A/V events from the timeline

        Returns:
            List of adjusted highlight windows
        """
        adjusted_windows = []

        # Iterate through windows
        for window in highlight_windows:
            window_start = window.get('start_time', 0)
            window_end = window.get('end_time', 0)
            window_score = window.get('base_score', 0.5)

            # Find relevant A/V events
            relevant_events = []
            for event in timeline_events:
                event_start = event.get('start_time', 0)
                event_end = event.get('end_time', 0)

                # Check if event is within 2 seconds of the window
                if (event_end >= window_start - 2 and event_start <= window_end + 2):
                    relevant_events.append(event)

            # Adjust window boundaries and score based on events
            adjusted_start = window_start
            adjusted_end = window_end
            score_bonus = 0.0

            for event in relevant_events:
                event_type = event.get('type', '')
                event_start = event.get('start_time', 0)
                event_end = event.get('end_time', 0)
                event_score = event.get('score', 0.0)

                # Adjust boundaries
                if event_type == 'audio_peak':
                    # Extend window to include audio peak
                    if event_start < adjusted_start and event_start > adjusted_start - 2:
                        adjusted_start = event_start
                    if event_end > adjusted_end and event_end < adjusted_end + 2:
                        adjusted_end = event_end

                    # Add score bonus based on audio peak intensity
                    score_bonus += event_score * 0.2  # Up to 0.2 bonus for audio peaks

                elif event_type == 'scene_change':
                    # Snap to scene change if within 0.75 seconds
                    if abs(event_start - adjusted_start) < 0.75:
                        adjusted_start = event_start
                    if abs(event_end - adjusted_end) < 0.75:
                        adjusted_end = event_end

                    # Add score bonus for scene changes
                    score_bonus += event_score  # Scene bonus from config

            # Update window
            window['start_time'] = float(adjusted_start)
            window['end_time'] = float(adjusted_end)
            window['duration'] = float(adjusted_end - adjusted_start)
            window['final_score'] = float(min(1.0, window_score + score_bonus))  # Cap at 1.0
            window['score_bonus'] = float(score_bonus)
            window['event_count'] = len(relevant_events)

            adjusted_windows.append(window)

        return adjusted_windows

    def _merge_overlapping_windows(self, highlight_windows: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Merge overlapping highlight windows

        Args:
            highlight_windows: List of highlight windows

        Returns:
            List of merged highlight windows
        """
        if not highlight_windows:
            return []

        # Sort windows by start time
        sorted_windows = sorted(highlight_windows, key=lambda x: x.get('start_time', 0))

        merged_windows = []
        current_window = sorted_windows[0].copy()

        for window in sorted_windows[1:]:
            # Check if windows overlap
            if window.get('start_time', 0) <= current_window.get('end_time', 0):
                # Merge windows
                current_window['end_time'] = max(current_window.get('end_time', 0), window.get('end_time', 0))
                current_window['duration'] = current_window['end_time'] - current_window['start_time']

                # Update score to maximum of the two
                current_window['final_score'] = max(current_window.get('final_score', 0), window.get('final_score', 0))

                # Combine keywords
                if current_window.get('keyword') != window.get('keyword'):
                    current_window['keyword'] = f"{current_window.get('keyword')},{window.get('keyword')}"

                # Combine text
                if current_window.get('text') != window.get('text'):
                    current_window['text'] = f"{current_window.get('text')} {window.get('text')}"
            else:
                # Add current window to result and start a new one
                merged_windows.append(current_window)
                current_window = window.copy()

        # Add the last window
        merged_windows.append(current_window)

        return merged_windows

    def _limit_total_duration(self, highlight_windows: List[Dict[str, Any]], max_duration: float) -> List[Dict[str, Any]]:
        """
        Limit the total duration of highlight windows

        Args:
            highlight_windows: List of highlight windows
            max_duration: Maximum total duration in seconds

        Returns:
            List of highlight windows with limited total duration
        """
        if not highlight_windows:
            return []

        # Sort windows by score (descending)
        sorted_windows = sorted(highlight_windows, key=lambda x: x.get('final_score', 0), reverse=True)

        # Filter out low-scoring windows
        filtered_windows = [w for w in sorted_windows if w.get('final_score', 0) >= PIPELINE_MIN_HIGHLIGHT_SCORE]

        # If no windows pass the score threshold, keep the highest scoring one
        if not filtered_windows and sorted_windows:
            filtered_windows = [sorted_windows[0]]

        # Limit total duration
        limited_windows = []
        total_duration = 0

        for window in filtered_windows:
            window_duration = window.get('duration', 0)

            if total_duration + window_duration <= max_duration:
                limited_windows.append(window)
                total_duration += window_duration
            else:
                # If we can't fit the whole window, try to fit part of it
                remaining_duration = max_duration - total_duration
                if remaining_duration >= 5:  # Only include if we can fit at least 5 seconds
                    trimmed_window = window.copy()
                    trimmed_window['end_time'] = window.get('start_time', 0) + remaining_duration
                    trimmed_window['duration'] = remaining_duration
                    trimmed_window['trimmed'] = True
                    limited_windows.append(trimmed_window)
                break

        return limited_windows
# Create a task instance
highlight_window_generator = HighlightWindowGenerator()

def generate_highlight_windows(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the highlight_window_generator task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return highlight_window_generator.run(job_id, *args, **kwargs)
