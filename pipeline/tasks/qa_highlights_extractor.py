#!/usr/bin/env python3
"""
Question-Answer Highlights Extractor Task

This task processes intelligent highlights to extract engaging question-answer pairs
for short-form vertical video content optimized for social media platforms.

Core Algorithm:
1. Load highlights and transcript data
2. For each highlight clip, identify meaningful questions
3. Extract corresponding answers within the same clip boundaries
4. Generate engaging titles and descriptions for social media
5. Optimize for vertical video format (9:16 aspect ratio)
"""

import os
import re
import json
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from .base_task import BaseTask

# Try to import KeyBERT for automatic keyword extraction
try:
    from keybert import KeyBERT
    KEYBERT_AVAILABLE = True
except ImportError:
    KEYBERT_AVAILABLE = False


class QAHighlightsExtractor(BaseTask):
    """Extract question-answer pairs from intelligent highlights for engaging short-form content"""

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)

        # Initialize KeyBERT for automatic keyword extraction
        self.keybert_model = None
        if KEYBERT_AVAILABLE:
            try:
                self.keybert_model = KeyBERT()
                self.logger.info("KeyBERT model initialized for automatic keyword extraction")
            except Exception as e:
                self.logger.warning(f"Failed to initialize KeyBERT: {e}")
        else:
            self.logger.warning("KeyBERT not available - using fallback keyword extraction")

    def run(self, job_id: str, intelligent_highlights_result: Dict[str, Any],
            transcription_result: Dict[str, Any], params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Extract question-answer pairs from intelligent highlights

        Args:
            job_id: Unique job identifier
            intelligent_highlights_result: Result from intelligent highlights extractor
            transcription_result: Result from transcription engine
            params: Additional parameters

        Returns:
            Dictionary containing QA highlights results
        """
        params = params or {}
        start_time = time.time()

        self.logger.info(f"Starting QA highlights extraction for job {job_id}")

        try:
            # Create output directory
            from config.settings import PIPELINE_OUTPUT_DIR
            job_output_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
            qa_highlights_dir = os.path.join(job_output_dir, "qa_highlights")
            os.makedirs(qa_highlights_dir, exist_ok=True)

            # Load highlights and transcript data
            highlights, transcript_segments = self._load_data(job_id, intelligent_highlights_result, transcription_result)

            # Extract content keywords automatically from the full transcript
            content_keywords = self._extract_content_keywords(transcript_segments)
            self.logger.info(f"Extracted content keywords: {content_keywords}")

            # Extract question-answer pairs for each highlight
            qa_highlights = []
            for i, highlight in enumerate(highlights):
                qa_pair = self._extract_qa_from_highlight(highlight, transcript_segments, i + 1, content_keywords)
                if qa_pair:
                    qa_highlights.append(qa_pair)

            # Generate output files
            output_files = self._generate_qa_outputs(qa_highlights, qa_highlights_dir, job_id)

            execution_time = time.time() - start_time

            # Generate clip timing data for the clip renderer
            clip_windows = self._generate_clip_windows(qa_highlights)

            result = {
                'status': 'completed',
                'qa_highlights_count': len(qa_highlights),
                'qa_highlights_path': output_files['qa_highlights_json'],
                'social_media_ready_path': output_files['social_media_json'],
                'clip_windows': clip_windows,  # Add clip windows for clip renderer
                'execution_time': execution_time,
                'metadata': {
                    'original_highlights_count': len(highlights),
                    'qa_extraction_rate': len(qa_highlights) / len(highlights) if highlights else 0,
                    'target_keywords': params.get('keywords', []),
                    'clip_windows_count': len(clip_windows)
                }
            }

            self.logger.info(f"QA highlights extraction completed in {execution_time:.2f}s")
            self.logger.info(f"Generated {len(qa_highlights)} QA highlight clips")

            return result

        except Exception as e:
            self.logger.error(f"QA highlights extraction failed: {str(e)}")
            return {
                'status': 'failed',
                'error': str(e),
                'execution_time': time.time() - start_time
            }

    def _load_data(self, job_id: str, intelligent_highlights_result: Dict[str, Any],
                   transcription_result: Dict[str, Any]) -> Tuple[List[Dict], List[Dict]]:
        """Load highlights and transcript data"""

        # Load highlights
        highlights_path = intelligent_highlights_result.get('highlights_path')
        if not highlights_path or not os.path.exists(highlights_path):
            raise FileNotFoundError(f"Highlights file not found: {highlights_path}")

        with open(highlights_path, 'r') as f:
            highlights_data = json.load(f)

        # Handle both old format (direct list) and new world-class format (nested under 'highlights' key)
        if isinstance(highlights_data, list):
            # Old format: direct list of highlights
            highlights = highlights_data
        elif isinstance(highlights_data, dict) and 'highlights' in highlights_data:
            # New world-class format: highlights nested under 'highlights' key
            highlights = highlights_data['highlights']
            self.logger.info(f"Loaded world-class highlights using {highlights_data.get('algorithm', 'unknown')} algorithm")
        else:
            raise ValueError(f"Invalid highlights format in {highlights_path}")

        # Load transcript
        transcript_path = transcription_result.get('transcript_path')
        if not transcript_path or not os.path.exists(transcript_path):
            raise FileNotFoundError(f"Transcript file not found: {transcript_path}")

        with open(transcript_path, 'r') as f:
            transcript_data = json.load(f)

        transcript_segments = transcript_data.get('segments', [])

        self.logger.info(f"Loaded {len(highlights)} highlights and {len(transcript_segments)} transcript segments")

        return highlights, transcript_segments

    def _extract_content_keywords(self, transcript_segments: List[Dict]) -> List[str]:
        """
        Extract relevant keywords from the full transcript using KeyBERT

        Args:
            transcript_segments: List of transcript segments

        Returns:
            List of extracted keywords relevant to the content
        """
        # Combine all transcript text
        full_transcript = ' '.join(segment.get('text', '') for segment in transcript_segments)

        if not full_transcript.strip():
            return []

        # Use KeyBERT if available
        if self.keybert_model:
            try:
                # Extract top keywords using KeyBERT
                keywords = self.keybert_model.extract_keywords(
                    full_transcript,
                    keyphrase_ngram_range=(1, 2),
                    stop_words='english'
                )[:15]  # Take top 15 keywords

                # Extract just the keyword strings
                extracted_keywords = [kw[0] if isinstance(kw, tuple) else str(kw) for kw in keywords]
                self.logger.info(f"KeyBERT extracted content keywords: {extracted_keywords}")
                return extracted_keywords

            except Exception as e:
                self.logger.warning(f"KeyBERT extraction failed: {e}")

        # Fallback: simple keyword extraction based on common patterns
        fallback_keywords = self._extract_keywords_fallback(full_transcript)
        self.logger.info(f"Using fallback content keywords: {fallback_keywords}")
        return fallback_keywords

    def _extract_keywords_fallback(self, text: str) -> List[str]:
        """
        Fallback keyword extraction when KeyBERT is not available

        Args:
            text: Full transcript text

        Returns:
            List of extracted keywords
        """
        # Common topic categories and their keywords
        topic_keywords = {
            'relationships': ['relationship', 'love', 'dating', 'partner', 'marriage', 'trust', 'connection'],
            'business': ['business', 'company', 'startup', 'entrepreneur', 'investment', 'money', 'career'],
            'psychology': ['psychology', 'mind', 'behavior', 'emotion', 'mental', 'thinking', 'decision'],
            'philosophy': ['philosophy', 'meaning', 'purpose', 'life', 'existence', 'wisdom', 'truth'],
            'technology': ['technology', 'ai', 'artificial intelligence', 'robot', 'machine', 'computer'],
            'science': ['science', 'research', 'study', 'experiment', 'data', 'evidence', 'theory'],
            'health': ['health', 'fitness', 'nutrition', 'exercise', 'wellness', 'medical', 'body'],
            'education': ['education', 'learning', 'knowledge', 'skill', 'teaching', 'university', 'school']
        }

        text_lower = text.lower()
        found_keywords = []

        # Find keywords that appear in the text with frequency weighting
        keyword_counts = {}
        for category, keywords in topic_keywords.items():
            for keyword in keywords:
                count = text_lower.count(keyword)
                if count > 0:
                    keyword_counts[keyword] = count

        # Sort by frequency and take top keywords
        sorted_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)
        found_keywords = [kw[0] for kw in sorted_keywords[:12]]  # Take top 12 keywords

        return found_keywords

    def _extract_qa_from_highlight(self, highlight: Dict[str, Any],
                                   transcript_segments: List[Dict], clip_number: int,
                                   content_keywords: List[str] = None) -> Optional[Dict[str, Any]]:
        """
        Extract question-answer pair from a single highlight clip

        Args:
            highlight: Highlight data with timing and text
            transcript_segments: Full transcript segments
            clip_number: Sequential clip number

        Returns:
            QA highlight data or None if no suitable QA pair found
        """
        start_time = highlight['start_time']
        end_time = highlight['end_time']

        # Get transcript segments within the highlight timeframe
        clip_segments = self._get_segments_in_timeframe(transcript_segments, start_time, end_time)

        if not clip_segments:
            return None

        # Combine text from all segments in the clip
        full_text = ' '.join(segment.get('text', '') for segment in clip_segments)

        # Try to identify question-answer patterns
        qa_pair = self._identify_qa_pattern(full_text, clip_segments, start_time, end_time, content_keywords)

        if not qa_pair:
            return None

        # Generate engaging content for social media with content keywords
        social_content = self._generate_social_media_content(qa_pair, highlight, clip_number, content_keywords)

        return {
            'clip_number': clip_number,
            'start_time': start_time,
            'end_time': end_time,
            'duration': highlight['duration'],
            'question': qa_pair['question'],
            'answer': qa_pair['answer'],
            'question_start': qa_pair['question_start'],
            'question_end': qa_pair['question_end'],
            'answer_start': qa_pair['answer_start'],
            'answer_end': qa_pair['answer_end'],
            'full_text': full_text,
            'engagement_score': highlight.get('score', 0),
            'social_media': social_content,
            'original_highlight': highlight
        }

    def _get_segments_in_timeframe(self, transcript_segments: List[Dict],
                                   start_time: float, end_time: float) -> List[Dict]:
        """Get transcript segments that fall within the specified timeframe"""
        clip_segments = []

        for segment in transcript_segments:
            seg_start = segment.get('start', 0)
            seg_end = segment.get('end', 0)

            # Check if segment overlaps with the highlight timeframe
            if (seg_start < end_time and seg_end > start_time):
                clip_segments.append(segment)

        return clip_segments

    def _identify_qa_pattern(self, full_text: str, segments: List[Dict],
                            start_time: float, end_time: float, content_keywords: List[str] = None) -> Optional[Dict[str, Any]]:
        """
        Identify question-answer patterns in the text

        Args:
            full_text: Combined text from all segments
            segments: Individual transcript segments
            start_time: Clip start time
            end_time: Clip end time

        Returns:
            QA pattern data or None if no pattern found
        """
        # Enhanced question patterns for better detection
        question_patterns = [
            r'\b(?:what|how|why|when|where|who|which)\s+[^.!?]*\?',  # Question words with content
            r'\b(?:can|could|would|will|should|do|does|did|is|are|was|were)\s+[^.!?]*\?',  # Auxiliary verbs
            r'\b(?:tell me|explain|describe|elaborate)\s+[^.!?]*[.!?]',  # Request patterns
            r'\b(?:what do you think|what are your thoughts|how do you feel)\s+[^.!?]*[.!?]',  # Opinion questions
            r'[^.!?]*\b(?:what|how|why)\s+[^.!?]*\?',  # Questions anywhere in text
        ]

        # Find questions in the text
        questions = []
        for pattern in question_patterns:
            matches = re.finditer(pattern, full_text, re.IGNORECASE)
            for match in matches:
                question_text = match.group().strip()

                # Clean up question text
                if question_text.startswith('.') or question_text.startswith('!'):
                    question_text = question_text[1:].strip()

                # Filter for meaningful questions
                if (len(question_text) > 15 and  # Longer questions
                    question_text.count(' ') >= 3 and  # At least 4 words
                    any(word in question_text.lower() for word in ['what', 'how', 'why', 'when', 'where', 'who', 'which'])):
                    questions.append({
                        'text': question_text,
                        'start_pos': match.start(),
                        'end_pos': match.end()
                    })

        if not questions:
            return None

        # Find the best question (prefer questions with content keywords)
        best_question = self._select_best_question(questions, full_text, content_keywords)

        if not best_question:
            return None

        # Find the answer that follows the question
        answer = self._extract_answer_for_question(best_question, full_text, segments)

        if not answer:
            return None

        # Calculate timing for question and answer
        question_timing = self._calculate_text_timing(best_question['text'], segments, start_time)
        answer_timing = self._calculate_text_timing(answer, segments, start_time)

        return {
            'question': best_question['text'],
            'answer': answer,
            'question_start': question_timing['start'],
            'question_end': question_timing['end'],
            'answer_start': answer_timing['start'],
            'answer_end': answer_timing['end']
        }

    def _select_best_question(self, questions: List[Dict], full_text: str, content_keywords: List[str] = None) -> Optional[Dict]:
        """Select the most engaging question from candidates using content-aware scoring"""
        if not questions:
            return None

        # Score questions based on engagement factors
        scored_questions = []

        for question in questions:
            score = 0
            q_text = question['text'].lower()

            # Prefer questions with content-relevant keywords (dynamic)
            if content_keywords:
                for keyword in content_keywords:
                    if keyword.lower() in q_text:
                        score += 2

            # Prefer certain question types
            if any(word in q_text for word in ['what', 'how', 'why']):
                score += 1

            # Prefer questions that create curiosity
            curiosity_words = ['think', 'believe', 'feel', 'opinion', 'predict', 'expect']
            if any(word in q_text for word in curiosity_words):
                score += 1

            # Prefer questions of appropriate length (not too short, not too long)
            if 20 <= len(question['text']) <= 150:
                score += 1

            scored_questions.append((question, score))

        # Return the highest scoring question
        scored_questions.sort(key=lambda x: x[1], reverse=True)
        return scored_questions[0][0] if scored_questions[0][1] > 0 else questions[0]

    def _extract_answer_for_question(self, question: Dict, full_text: str, segments: List[Dict]) -> Optional[str]:
        """Extract the answer that follows the identified question"""
        question_end_pos = question['end_pos']

        # Look for answer in the text following the question
        remaining_text = full_text[question_end_pos:].strip()

        if not remaining_text:
            return None

        # Split into sentences and take the first few that form a coherent answer
        sentences = re.split(r'[.!?]+', remaining_text)

        answer_sentences = []
        answer_length = 0
        max_answer_length = 300  # Maximum answer length in characters

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # Stop if we've reached a good answer length
            if answer_length + len(sentence) > max_answer_length and answer_sentences:
                break

            answer_sentences.append(sentence)
            answer_length += len(sentence)

            # Stop if we have a complete thought (at least 2 sentences or one long sentence)
            if len(answer_sentences) >= 2 or len(sentence) > 50:
                break

        if not answer_sentences:
            return None

        answer = '. '.join(answer_sentences)
        if not answer.endswith('.'):
            answer += '.'

        return answer

    def _calculate_text_timing(self, text: str, segments: List[Dict], clip_start: float) -> Dict[str, float]:
        """Calculate approximate timing for a piece of text within the segments"""
        # This is a simplified approach - in practice, you might want more sophisticated timing
        # For now, we'll estimate based on text position and segment timing

        if not segments:
            return {'start': clip_start, 'end': clip_start + 5.0}

        # Find the segment that likely contains this text
        for segment in segments:
            if text.lower() in segment.get('text', '').lower():
                return {
                    'start': segment.get('start', clip_start),
                    'end': segment.get('end', clip_start + 5.0)
                }

        # Fallback: use first segment timing
        first_segment = segments[0]
        return {
            'start': first_segment.get('start', clip_start),
            'end': first_segment.get('end', clip_start + 5.0)
        }

    def _generate_social_media_content(self, qa_pair: Dict[str, Any],
                                       highlight: Dict[str, Any], clip_number: int,
                                       content_keywords: List[str] = None) -> Dict[str, Any]:
        """
        Generate engaging social media content for the QA highlight

        Args:
            qa_pair: Question-answer pair data
            highlight: Original highlight data
            clip_number: Sequential clip number

        Returns:
            Social media optimized content
        """
        question = qa_pair['question']
        answer = qa_pair['answer']

        # Generate engaging title
        title = self._generate_engaging_title(question, answer, clip_number)

        # Generate description with content keywords
        description = self._generate_description(question, answer, content_keywords)

        # Generate hashtags using content keywords
        hashtags = self._generate_hashtags(question, answer, content_keywords)

        # Generate hook (opening line to grab attention)
        hook = self._generate_hook(question)

        # Determine optimal platform recommendations
        platform_recommendations = self._get_platform_recommendations(qa_pair, highlight)

        return {
            'title': title,
            'description': description,
            'hashtags': hashtags,
            'hook': hook,
            'platform_recommendations': platform_recommendations,
            'engagement_rationale': self._generate_engagement_rationale(qa_pair, highlight),
            'vertical_video_optimized': True,
            'aspect_ratio': '9:16',
            'recommended_duration': f"{highlight['duration']:.1f}s"
        }

    def _generate_engaging_title(self, question: str, answer: str, clip_number: int) -> str:
        """Generate an engaging title for the QA highlight"""

        # Extract key concepts from question and answer
        key_concepts = self._extract_key_concepts(question + " " + answer)

        # Content-aware title generation based on actual topic
        combined_text = (question + " " + answer).lower()

        if any(word in combined_text for word in ['relationship', 'love', 'dating', 'partner', 'marriage']):
            if 'what' in question.lower():
                return f"💕 Relationship Wisdom"
            elif 'how' in question.lower():
                return f"💡 Dating Advice"
            elif 'why' in question.lower():
                return f"🤔 Why This Matters"
            else:
                return f"💕 Love & Relationships"

        elif any(word in combined_text for word in ['decision', 'choice', 'change', 'life']):
            if 'what' in question.lower():
                return f"🎯 Life Decisions"
            elif 'how' in question.lower():
                return f"💡 Decision Making"
            else:
                return f"🔥 Life Advice"

        elif any(word in combined_text for word in ['business', 'career', 'money', 'success']):
            return f"💼 Success Mindset"

        elif any(word in combined_text for word in ['ai', 'technology', 'future', 'robot']):
            return f"🤖 Tech Insights"

        else:
            # Generic engaging title based on question type
            if 'what' in question.lower():
                return f"💡 Expert Explains: Key Insights"
            elif 'how' in question.lower():
                return f"🔥 How To: Life Hacks"
            elif 'why' in question.lower():
                return f"🤔 Why This Matters"
            else:
                return f"🎯 Mind-Blowing Insight"

    def _generate_description(self, question: str, answer: str, content_keywords: List[str] = None) -> str:
        """Generate a compelling description for social media using content keywords"""

        # Clean up question and answer
        clean_question = question.strip().rstrip('?') + '?'
        clean_answer = answer.strip()

        # Create engaging description
        description = f"🔥 {clean_question}\n\n"
        description += f"💡 {clean_answer[:150]}{'...' if len(clean_answer) > 150 else ''}\n\n"
        description += "👆 What do you think? Share your thoughts below!\n\n"

        # Add relevant hashtags based on content keywords
        if content_keywords:
            relevant_hashtags = []
            for keyword in content_keywords[:5]:  # Use top 5 keywords
                hashtag = self._keyword_to_hashtag(keyword)
                if hashtag:
                    relevant_hashtags.append(hashtag)

            if relevant_hashtags:
                description += " ".join(relevant_hashtags)
            else:
                description += "#Educational #Knowledge #Insights"
        else:
            description += "#Educational #Knowledge #Insights"

        return description

    def _generate_hashtags(self, question: str, answer: str, content_keywords: List[str] = None) -> List[str]:
        """Generate relevant hashtags based on actual content keywords"""

        # Start with platform-specific hashtags
        hashtags = ['#Shorts', '#Reels', '#TikTok', '#Viral', '#Educational']

        # Prioritize content keywords over search keywords
        if content_keywords:
            self.logger.info(f"Generating hashtags from content keywords: {content_keywords}")
            for keyword in content_keywords[:8]:  # Use top 8 content keywords
                hashtag = self._keyword_to_hashtag(keyword)
                if hashtag:
                    hashtags.append(hashtag)

        # Always analyze the actual Q&A text for additional relevant hashtags
        text = (question + " " + answer).lower()
        text_hashtags = self._generate_hashtags_from_text(text)
        hashtags.extend(text_hashtags)

        # Remove duplicates and limit to 15 hashtags
        unique_hashtags = list(dict.fromkeys(hashtags))[:15]  # Preserve order while removing duplicates
        self.logger.info(f"Generated hashtags: {unique_hashtags}")
        return unique_hashtags

    def _keyword_to_hashtag(self, keyword: str) -> Optional[str]:
        """Convert a keyword to a proper hashtag format"""
        # Clean and format the keyword
        keyword = keyword.strip().lower()

        # Mapping of keywords to appropriate hashtags
        hashtag_mapping = {
            'relationship': '#Relationships',
            'relationships': '#Relationships',
            'love': '#Love',
            'dating': '#Dating',
            'partner': '#Relationships',
            'marriage': '#Marriage',
            'trust': '#Trust',
            'connection': '#Connection',
            'business': '#Business',
            'entrepreneur': '#Entrepreneur',
            'startup': '#Startup',
            'investment': '#Investment',
            'money': '#Money',
            'career': '#Career',
            'psychology': '#Psychology',
            'mind': '#Mindset',
            'behavior': '#Behavior',
            'emotion': '#Emotions',
            'mental': '#MentalHealth',
            'thinking': '#Mindset',
            'decision': '#DecisionMaking',
            'philosophy': '#Philosophy',
            'meaning': '#Meaning',
            'purpose': '#Purpose',
            'life': '#Life',
            'wisdom': '#Wisdom',
            'truth': '#Truth',
            'technology': '#Technology',
            'ai': '#AI',
            'artificial intelligence': '#AI',
            'science': '#Science',
            'health': '#Health',
            'fitness': '#Fitness',
            'education': '#Education',
            'learning': '#Learning'
        }

        # Return mapped hashtag or create one from the keyword
        if keyword in hashtag_mapping:
            return hashtag_mapping[keyword]
        elif len(keyword) > 2:
            # Create hashtag from keyword (capitalize first letter)
            return f"#{keyword.replace(' ', '').title()}"

        return None

    def _generate_hashtags_from_text(self, text: str) -> List[str]:
        """Generate hashtags by analyzing text content directly"""
        hashtags = []

        # Topic-based hashtag detection
        if any(word in text for word in ['relationship', 'love', 'dating', 'partner']):
            hashtags.extend(['#Relationships', '#Love', '#Dating'])

        if any(word in text for word in ['business', 'entrepreneur', 'startup', 'money']):
            hashtags.extend(['#Business', '#Entrepreneur', '#Success'])

        if any(word in text for word in ['psychology', 'mind', 'behavior', 'emotion']):
            hashtags.extend(['#Psychology', '#Mindset', '#MentalHealth'])

        if any(word in text for word in ['philosophy', 'meaning', 'purpose', 'life']):
            hashtags.extend(['#Philosophy', '#Life', '#Wisdom'])

        if any(word in text for word in ['ai', 'artificial intelligence', 'technology', 'robot']):
            hashtags.extend(['#AI', '#Technology', '#Future'])

        if any(word in text for word in ['science', 'research', 'study']):
            hashtags.extend(['#Science', '#Research', '#Knowledge'])

        return hashtags

    def _generate_hook(self, question: str) -> str:
        """Generate an attention-grabbing hook for the video opening"""

        question_lower = question.lower()

        if 'what' in question_lower and ('future' in question_lower or 'ai' in question_lower):
            return "🚨 The future is closer than you think..."

        elif 'how' in question_lower:
            return "🤯 You won't believe how this works..."

        elif 'why' in question_lower:
            return "🔍 The shocking reason behind this..."

        elif any(word in question_lower for word in ['think', 'believe']):
            return "💭 Here's what experts really think..."

        else:
            return "⚡ This will blow your mind..."

    def _get_platform_recommendations(self, qa_pair: Dict[str, Any],
                                      highlight: Dict[str, Any]) -> Dict[str, Any]:
        """Get platform-specific recommendations for the content"""

        duration = highlight['duration']
        engagement_score = highlight.get('score', 0)

        recommendations = {
            'youtube_shorts': {
                'recommended': True,
                'optimal_duration': '15-60s',
                'current_duration': f"{duration:.1f}s",
                'fit_score': 0.9 if 15 <= duration <= 60 else 0.6
            },
            'instagram_reels': {
                'recommended': True,
                'optimal_duration': '15-30s',
                'current_duration': f"{duration:.1f}s",
                'fit_score': 0.9 if 15 <= duration <= 30 else 0.7
            },
            'tiktok': {
                'recommended': True,
                'optimal_duration': '15-60s',
                'current_duration': f"{duration:.1f}s",
                'fit_score': 0.9 if 15 <= duration <= 60 else 0.6
            }
        }

        # Adjust recommendations based on content type
        text = qa_pair['question'] + " " + qa_pair['answer']
        if any(word in text.lower() for word in ['science', 'physics', 'complex']):
            # Educational content might need slightly longer duration
            for platform in recommendations.values():
                if platform['fit_score'] < 0.8:
                    platform['fit_score'] += 0.1

        return recommendations

    def _generate_engagement_rationale(self, qa_pair: Dict[str, Any],
                                       highlight: Dict[str, Any]) -> str:
        """Generate rationale for why this clip should be engaging"""

        factors = []

        # Question type analysis
        question = qa_pair['question'].lower()
        if any(word in question for word in ['what', 'how', 'why']):
            factors.append("Clear question-answer format creates curiosity")

        # Content analysis
        text = (qa_pair['question'] + " " + qa_pair['answer']).lower()
        if 'ai' in text or 'artificial intelligence' in text:
            factors.append("AI content is highly trending and engaging")

        if 'future' in text:
            factors.append("Future predictions generate high interest")

        # Duration analysis
        duration = highlight['duration']
        if 15 <= duration <= 30:
            factors.append("Optimal duration for social media attention spans")
        elif duration <= 60:
            factors.append("Good duration for educational content")

        # Engagement score
        score = highlight.get('score', 0)
        if score > 0.4:
            factors.append("High engagement score from intelligent analysis")

        return "; ".join(factors) if factors else "Educational content with clear structure"

    def _extract_key_concepts(self, text: str) -> List[str]:
        """Extract key concepts from text for title generation"""

        # Common important concepts in the domain
        concepts = {
            'artificial intelligence': 'AI',
            'machine learning': 'Machine Learning',
            'neural networks': 'Neural Networks',
            'quantum': 'Quantum Physics',
            'space': 'Space Exploration',
            'mars': 'Mars Colonization',
            'robots': 'Robotics',
            'brain': 'Brain Science',
            'consciousness': 'Consciousness',
            'universe': 'The Universe',
            'multiverse': 'Multiverse',
            'technology': 'Technology',
            'future': 'The Future',
            'immortality': 'Immortality',
            'aliens': 'Alien Life'
        }

        text_lower = text.lower()
        found_concepts = []

        for concept, display_name in concepts.items():
            if concept in text_lower:
                found_concepts.append(display_name)

        return found_concepts[:3]  # Return top 3 concepts

    def _generate_clip_windows(self, qa_highlights: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate clip windows from QA highlights for the clip renderer

        Args:
            qa_highlights: List of QA highlight data

        Returns:
            List of clip windows compatible with clip renderer
        """
        clip_windows = []

        for i, qa_highlight in enumerate(qa_highlights):
            clip_window = {
                'start_time': qa_highlight['start_time'],
                'end_time': qa_highlight['end_time'],
                'duration': qa_highlight['duration'],
                'clip_id': f"qa_clip_{i:03d}",
                'score': qa_highlight.get('engagement_score', 0),
                'text': qa_highlight.get('full_text', ''),
                'question': qa_highlight.get('question', ''),
                'answer': qa_highlight.get('answer', ''),
                'social_media': qa_highlight.get('social_media', {}),
                'type': 'qa_highlight'
            }
            clip_windows.append(clip_window)

        self.logger.info(f"Generated {len(clip_windows)} clip windows from QA highlights")
        return clip_windows

    def _generate_qa_outputs(self, qa_highlights: List[Dict[str, Any]],
                             output_dir: str, job_id: str) -> Dict[str, str]:
        """
        Generate output files for QA highlights

        Args:
            qa_highlights: List of QA highlight data
            output_dir: Output directory path
            job_id: Job identifier

        Returns:
            Dictionary with output file paths
        """

        # Generate comprehensive QA highlights JSON
        qa_highlights_path = os.path.join(output_dir, "qa_highlights.json")
        with open(qa_highlights_path, 'w') as f:
            json.dump(qa_highlights, f, indent=2)

        # Generate social media ready format
        social_media_data = []
        for qa in qa_highlights:
            social_media_data.append({
                'clip_number': qa['clip_number'],
                'title': qa['social_media']['title'],
                'description': qa['social_media']['description'],
                'hashtags': qa['social_media']['hashtags'],
                'hook': qa['social_media']['hook'],
                'duration': qa['duration'],
                'start_time': qa['start_time'],
                'end_time': qa['end_time'],
                'question': qa['question'],
                'answer': qa['answer'],
                'platform_recommendations': qa['social_media']['platform_recommendations'],
                'engagement_rationale': qa['social_media']['engagement_rationale']
            })

        social_media_path = os.path.join(output_dir, "social_media_ready.json")
        with open(social_media_path, 'w') as f:
            json.dump(social_media_data, f, indent=2)

        # Generate summary report
        summary_path = os.path.join(output_dir, "qa_summary.txt")
        self._generate_summary_report(qa_highlights, summary_path)

        self.logger.info(f"Generated QA highlights JSON: {qa_highlights_path}")
        self.logger.info(f"Generated social media ready JSON: {social_media_path}")
        self.logger.info(f"Generated summary report: {summary_path}")

        return {
            'qa_highlights_json': qa_highlights_path,
            'social_media_json': social_media_path,
            'summary_report': summary_path
        }

    def _generate_summary_report(self, qa_highlights: List[Dict[str, Any]], output_path: str) -> None:
        """Generate a human-readable summary report"""

        with open(output_path, 'w') as f:
            f.write("🎬 QUESTION-ANSWER HIGHLIGHTS EXTRACTION REPORT\n")
            f.write("=" * 60 + "\n\n")

            f.write(f"📊 SUMMARY\n")
            f.write(f"Total QA Clips Generated: {len(qa_highlights)}\n")
            f.write(f"Total Duration: {sum(qa['duration'] for qa in qa_highlights):.1f} seconds\n")
            f.write(f"Average Clip Duration: {sum(qa['duration'] for qa in qa_highlights) / len(qa_highlights):.1f} seconds\n\n")

            f.write("🎯 CLIP DETAILS\n")
            f.write("-" * 40 + "\n")

            for i, qa in enumerate(qa_highlights, 1):
                f.write(f"\nClip #{i}: {qa['social_media']['title']}\n")
                f.write(f"Duration: {qa['duration']:.1f}s ({qa['start_time']:.1f}s - {qa['end_time']:.1f}s)\n")
                f.write(f"Question: {qa['question']}\n")
                f.write(f"Answer: {qa['answer'][:100]}{'...' if len(qa['answer']) > 100 else ''}\n")
                f.write(f"Engagement Rationale: {qa['social_media']['engagement_rationale']}\n")
                f.write(f"Platform Fit: YouTube Shorts ✓, Instagram Reels ✓, TikTok ✓\n")
                f.write("-" * 40 + "\n")

            f.write(f"\n🚀 READY FOR SOCIAL MEDIA\n")
            f.write("All clips are optimized for vertical video format (9:16 aspect ratio)\n")
            f.write("Engaging titles, descriptions, and hashtags generated\n")
            f.write("Platform-specific recommendations included\n")
            f.write("Question-answer format optimized for viewer engagement\n")
