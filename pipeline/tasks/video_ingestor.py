#!/usr/bin/env python3
"""
Video Ingestor Task for the Smart Video Highlight Generator Pipeline

This version uses direct function calls instead  for task execution.
"""

import os
import json
import subprocess
from typing import Dict, Any, Optional, List

import ffmpeg

from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR

class VideoIngestor(BaseTask):
    """
    Stage-01: Video Ingestor

    Performs rapid metadata extraction using ffprobe, captures and stores essential metadata,
    and re-multiplexes to lossless .mkv format with -c copy to ensure reliable keyframe seeking.
    """

    task_name = "video_ingestor"
    requires_gpu = False

    def process(self, job_id: str, preflight_result: Dict[str, Any], params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process the input video file for optimal seeking

        Args:
            job_id: Unique identifier for the job
            preflight_result: Results from the preflight validator
            params: Additional parameters for the task

        Returns:
            Dictionary containing processed video path and metadata
        """
        params = params or {}
        self.logger.info(f"Processing video for job {job_id}")

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Get video path from preflight result
        video_path = preflight_result.get('video_path')
        if not video_path or not os.path.exists(video_path):
            raise ValueError(f"Invalid video path from preflight validator: {video_path}")

        # Create job directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        os.makedirs(job_dir, exist_ok=True)

        # Define output path for the optimized video
        optimized_video_path = os.path.join(job_dir, "optimized.mkv")

        # Re-multiplex the video to MKV format for better seeking
        try:
            # Use ffmpeg to copy streams without re-encoding
            (
                ffmpeg
                .input(video_path)
                .output(
                    optimized_video_path,
                    c='copy',  # Copy all streams without re-encoding
                    f='matroska',  # MKV container
                    movflags='faststart',  # Optimize for streaming
                    fflags='+genpts',  # Generate presentation timestamps
                )
                .global_args('-y')  # Overwrite output file if it exists
                .run(quiet=True, overwrite_output=True)
            )
        except ffmpeg.Error as e:
            self.logger.error(f"Error optimizing video: {e.stderr.decode() if hasattr(e, 'stderr') else str(e)}")

            # If optimization fails, use the original video
            self.logger.warning("Using original video file as fallback")
            optimized_video_path = video_path

        # Extract detailed metadata using ffprobe
        try:
            # Run ffprobe with detailed output
            probe = ffmpeg.probe(
                optimized_video_path,
                select_streams='v:0',  # Select first video stream
                show_frames=None,  # Show frame information
                read_intervals='%+#1',  # Read only the first frame
                show_entries='frame=key_frame,pkt_pts_time,pict_type'  # Show specific frame entries
            )

            # Extract keyframe information
            keyframes = []
            if 'frames' in probe:
                for frame in probe['frames']:
                    if frame.get('key_frame') == 1:
                        keyframes.append(float(frame.get('pkt_pts_time', 0)))

            # Get GOP (Group of Pictures) size if possible
            gop_size = None
            if len(keyframes) >= 2:
                # Estimate GOP size from first two keyframes
                gop_size = keyframes[1] - keyframes[0]

            # Get more detailed metadata
            detailed_probe = ffmpeg.probe(optimized_video_path)

            # Extract video stream information
            video_streams = [stream for stream in detailed_probe['streams'] if stream['codec_type'] == 'video']
            audio_streams = [stream for stream in detailed_probe['streams'] if stream['codec_type'] == 'audio']

            if not video_streams:
                raise ValueError("No video streams found in the optimized file")

            # Get primary video stream
            video_stream = video_streams[0]

            # Extract display aspect ratio (DAR) and sample aspect ratio (SAR)
            dar = video_stream.get('display_aspect_ratio', f"{video_stream.get('width')}:{video_stream.get('height')}")
            sar = video_stream.get('sample_aspect_ratio', '1:1')

            # Create result with detailed metadata
            result = {
                'status': 'completed',
                'job_id': job_id,
                'video_path': optimized_video_path,
                'original_video_path': preflight_result.get('original_video_path'),
                'metadata': {
                    **preflight_result.get('metadata', {}),  # Include metadata from preflight
                    'optimized_format': 'matroska',
                    'dar': dar,
                    'sar': sar,
                    'gop_size': gop_size,
                    'first_keyframe': keyframes[0] if keyframes else None,
                    'keyframe_count': len(keyframes),
                    'video_stream_index': video_stream.get('index'),
                    'audio_stream_index': audio_streams[0].get('index') if audio_streams else None,
                }
            }

            # Save state for idempotency
            self.save_state(job_id, result, self.task_name)

            return result

        except ffmpeg.Error as e:
            self.logger.error(f"Error probing optimized video: {e.stderr.decode() if hasattr(e, 'stderr') else str(e)}")
            raise ValueError(f"Failed to extract metadata from optimized video: {str(e)}")

# Create a task instance
video_ingestor = VideoIngestor()

def ingest_video(job_id: str, preflight_result: Dict[str, Any], params: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Run the video ingestor task

    Args:
        job_id: Unique identifier for the job
        preflight_result: Results from the preflight validator
        params: Additional parameters for the task

    Returns:
        Dictionary containing processed video path and metadata
    """
    return video_ingestor.run(job_id, preflight_result, params or {})
