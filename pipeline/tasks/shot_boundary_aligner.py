#!/usr/bin/env python3
"""
Shot-Boundary Aligner Task for the Smart Video Highlight Generator Pipeline


This version uses direct function calls instead  for task execution.
"""

import os
import json
from typing import Dict, Any, List


from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR

class ShotBoundaryAligner(BaseTask):
    """
    Stage-06: Shot-Boundary Aligner

    Refines highlight boundaries by snapping to nearest keyframes, applies maximum
    tolerance for boundary adjustment, and ensures all segment boundaries align with
    GOP (Group of Pictures) for efficient video trimming.
    Scene detection has been removed to simplify the pipeline.
    """

    task_name = "shot_boundary_aligner"
    requires_gpu = False

    def run(self, job_id: str, window_result: Dict[str, Any], ingestor_result: Dict[str, Any], av_event_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Align highlight windows to keyframe boundaries

        Args:
            job_id: Unique identifier for the job
            window_result: Results from the highlight window generator
            ingestor_result: Results from the video ingestor
            av_event_result: Results from the A/V event detector (not used since scene detection removed)
            params: Additional parameters for the task

        Returns:
            Dictionary containing aligned highlight windows
        """
        self.logger.info(f"Aligning highlight windows to shot boundaries for job {job_id}")

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Create job directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        aligned_dir = os.path.join(job_dir, "aligned")
        os.makedirs(aligned_dir, exist_ok=True)

        # Get highlight windows
        windows_path = window_result.get('windows_path')
        if not windows_path or not os.path.exists(windows_path):
            raise ValueError(f"Invalid windows path from highlight window generator: {windows_path}")

        # Load highlight windows
        with open(windows_path, 'r') as f:
            highlight_windows = json.load(f)

        # Scene detection has been removed, so we only use keyframes for alignment
        scene_changes = []  # Empty list since scene detection is disabled

        # Get keyframe information from ingestor result
        keyframes = self._extract_keyframe_info(ingestor_result)

        # Get parameters
        max_tolerance = params.get('boundary_tolerance', 0.75)  # Default 0.75 seconds

        # Align windows to shot boundaries and keyframes
        aligned_windows = self._align_windows(highlight_windows, scene_changes, keyframes, max_tolerance)

        # Save aligned windows to file
        aligned_path = os.path.join(aligned_dir, "aligned_windows.json")
        with open(aligned_path, 'w') as f:
            json.dump(aligned_windows, f, indent=2)

        # Create result with aligned window data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'aligned_path': aligned_path,
            'metadata': {
                'window_count': len(aligned_windows),
                'total_duration': sum(window.get('duration', 0) for window in aligned_windows),
                'alignment_changes': sum(1 for window in aligned_windows if window.get('aligned', False))
            }
        }

        # Save state for idempotency
        self.save_state(job_id, result, self.task_name)

        return result

    def _extract_keyframe_info(self, ingestor_result: Dict[str, Any]) -> List[float]:
        """
        Extract keyframe information from ingestor result

        Args:
            ingestor_result: Results from the video ingestor

        Returns:
            List of keyframe timestamps
        """
        # Get keyframe information from metadata
        metadata = ingestor_result.get('metadata', {})
        first_keyframe = metadata.get('first_keyframe')
        gop_size = metadata.get('gop_size')
        duration = metadata.get('duration', 0)

        # If we have both first keyframe and GOP size, generate keyframe timestamps
        if first_keyframe is not None and gop_size is not None:
            # Generate keyframe timestamps
            keyframes = []
            current_time = first_keyframe

            while current_time < duration:
                keyframes.append(current_time)
                current_time += gop_size

            return keyframes
        else:
            # If we don't have keyframe information, return an empty list
            self.logger.warning("No keyframe information available from ingestor result")
            return []

    def _align_windows(self, highlight_windows: List[Dict[str, Any]], scene_changes: List[Dict[str, Any]],
                      keyframes: List[float], max_tolerance: float) -> List[Dict[str, Any]]:
        """
        Align highlight windows to keyframes (scene detection removed)

        Args:
            highlight_windows: List of highlight windows
            scene_changes: List of scene changes (empty since scene detection removed)
            keyframes: List of keyframe timestamps
            max_tolerance: Maximum tolerance for boundary adjustment in seconds

        Returns:
            List of aligned highlight windows
        """
        aligned_windows = []

        # Extract scene change timestamps
        scene_timestamps = [sc.get('start_time', 0) for sc in scene_changes]

        # Iterate through windows
        for window in highlight_windows:
            window_start = window.get('start_time', 0)
            window_end = window.get('end_time', 0)

            # Find nearest scene change for start time
            aligned_start = window_start
            start_aligned = False

            for timestamp in scene_timestamps:
                if abs(timestamp - window_start) <= max_tolerance:
                    aligned_start = timestamp
                    start_aligned = True
                    break

            # If no scene change found, try to align to nearest keyframe
            if not start_aligned and keyframes:
                nearest_keyframe = min(keyframes, key=lambda x: abs(x - window_start))
                if abs(nearest_keyframe - window_start) <= max_tolerance:
                    aligned_start = nearest_keyframe
                    start_aligned = True

            # Find nearest scene change for end time
            aligned_end = window_end
            end_aligned = False

            for timestamp in scene_timestamps:
                if abs(timestamp - window_end) <= max_tolerance:
                    aligned_end = timestamp
                    end_aligned = True
                    break

            # If no scene change found, try to align to nearest keyframe
            if not end_aligned and keyframes:
                nearest_keyframe = min(keyframes, key=lambda x: abs(x - window_end))
                if abs(nearest_keyframe - window_end) <= max_tolerance:
                    aligned_end = nearest_keyframe
                    end_aligned = True

            # Create aligned window
            aligned_window = window.copy()
            aligned_window['start_time'] = float(aligned_start)
            aligned_window['end_time'] = float(aligned_end)
            aligned_window['duration'] = float(aligned_end - aligned_start)
            aligned_window['aligned'] = start_aligned or end_aligned
            aligned_window['start_aligned'] = start_aligned
            aligned_window['end_aligned'] = end_aligned

            # Only include windows with positive duration
            if aligned_window['duration'] > 0:
                aligned_windows.append(aligned_window)
            else:
                self.logger.warning(f"Skipping window with zero or negative duration: {aligned_window}")

        return aligned_windows
# Create a task instance
shot_boundary_aligner = ShotBoundaryAligner()

def align_shot_boundaries(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the shot_boundary_aligner task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return shot_boundary_aligner.run(job_id, *args, **kwargs)
