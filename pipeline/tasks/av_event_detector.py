#!/usr/bin/env python3
"""
A/V Event Detector Task for the Smart Video Highlight Generator Pipeline


This version uses direct function calls instead  for task execution.
"""

import os
import json
import numpy as np
from typing import Dict, Any, List

import librosa

from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR

class AVEventDetector(BaseTask):
    """
    Stage-04: A/V Event Detector

    Extracts RMS energy levels and detects audio events to identify potential highlight moments.
    Scene detection has been removed to simplify the pipeline.
    """

    task_name = "av_event_detector"
    requires_gpu = False  # Only audio processing, no GPU needed

    def run(self, job_id: str, ingestor_result: Dict[str, Any], transcription_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Detect audio events in the video

        Args:
            job_id: Unique identifier for the job
            ingestor_result: Results from the video ingestor
            transcription_result: Results from the transcription engine
            params: Additional parameters for the task

        Returns:
            Dictionary containing audio event detection results
        """
        self.logger.info(f"Detecting audio events for job {job_id}")

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Get video path from ingestor result
        video_path = ingestor_result.get('video_path')
        if not video_path or not os.path.exists(video_path):
            raise ValueError(f"Invalid video path from ingestor: {video_path}")

        # Get audio path from transcription result
        audio_path = transcription_result.get('audio_path')
        if not audio_path or not os.path.exists(audio_path):
            raise ValueError(f"Invalid audio path from transcription engine: {audio_path}")

        # Create job directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        events_dir = os.path.join(job_dir, "events")
        os.makedirs(events_dir, exist_ok=True)

        # Detect audio events
        audio_events = self._detect_audio_events(audio_path, events_dir)

        # Create timeline from audio events only (scene detection removed)
        timeline = self._create_unified_timeline(audio_events)

        # Save timeline to file
        timeline_path = os.path.join(events_dir, "timeline.json")
        with open(timeline_path, 'w') as f:
            json.dump(timeline, f, indent=2)

        # Create result with event data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'timeline_path': timeline_path,
            'audio_events_path': os.path.join(events_dir, "audio_events.json"),
            'metadata': {
                'audio_event_count': len(audio_events),
                'timeline_event_count': len(timeline)
            }
        }

        # Save state for idempotency
        self.save_state(job_id, result, self.task_name)

        return result

    def _detect_audio_events(self, audio_path: str, output_dir: str) -> List[Dict[str, Any]]:
        """
        Detect audio events such as peaks in energy

        Args:
            audio_path: Path to the audio file
            output_dir: Directory to save results

        Returns:
            List of audio events with timestamps and metadata
        """
        self.logger.info(f"Detecting audio events in {audio_path}")

        # Load audio file
        y, sr = librosa.load(audio_path, sr=None)

        # Calculate RMS energy
        frame_length = int(sr * 0.025)  # 25ms frames
        hop_length = int(sr * 0.010)    # 10ms hop
        rms = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]

        # Convert frame indices to timestamps
        timestamps = librosa.times_like(rms, sr=sr, hop_length=hop_length)

        # Calculate mean and standard deviation of RMS energy
        mean_rms = np.mean(rms)
        std_rms = np.std(rms)

        # Detect peaks (energy > mean + 2*std)
        threshold = mean_rms + 2 * std_rms
        peak_indices = np.where(rms > threshold)[0]

        # Group consecutive peaks
        audio_events = []
        if len(peak_indices) > 0:
            # Start with the first peak
            current_start = timestamps[peak_indices[0]]
            current_end = timestamps[peak_indices[0]]
            current_max = rms[peak_indices[0]]

            for i in range(1, len(peak_indices)):
                # If this peak is consecutive with the previous one
                if peak_indices[i] - peak_indices[i-1] <= 3:  # Within 3 frames
                    # Update the end time and max value
                    current_end = timestamps[peak_indices[i]]
                    current_max = max(current_max, rms[peak_indices[i]])
                else:
                    # Add the current event and start a new one
                    audio_events.append({
                        'type': 'audio_peak',
                        'start_time': float(current_start),
                        'end_time': float(current_end),
                        'duration': float(current_end - current_start),
                        'intensity': float(current_max / threshold),
                        'score': float(min(1.0, current_max / threshold))
                    })

                    # Start a new event
                    current_start = timestamps[peak_indices[i]]
                    current_end = timestamps[peak_indices[i]]
                    current_max = rms[peak_indices[i]]

            # Add the last event
            audio_events.append({
                'type': 'audio_peak',
                'start_time': float(current_start),
                'end_time': float(current_end),
                'duration': float(current_end - current_start),
                'intensity': float(current_max / threshold),
                'score': float(min(1.0, current_max / threshold))
            })

        # Save audio events to file
        audio_events_path = os.path.join(output_dir, "audio_events.json")
        with open(audio_events_path, 'w') as f:
            json.dump(audio_events, f, indent=2)

        return audio_events

    def _create_unified_timeline(self, audio_events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create a unified timeline of audio events

        Args:
            audio_events: List of audio events

        Returns:
            Timeline of audio events
        """
        # Sort audio events by start time
        timeline = sorted(audio_events, key=lambda x: x['start_time'])

        return timeline
# Create a task instance
av_event_detector = AVEventDetector()

def detect_av_events(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the av_event_detector task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return av_event_detector.run(job_id, *args, **kwargs)
