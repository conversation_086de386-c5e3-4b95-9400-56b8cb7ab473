#!/usr/bin/env python3
"""
Semantic Indexer Task for the Smart Video Highlight Generator Pipeline


This version uses direct function calls instead  for task execution.
"""

import os
import json
import pickle
import numpy as np
from typing import Dict, Any, Optional, List, Tuple

try:
    from sentence_transformers import SentenceTransformer
    import faiss
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: sentence_transformers not available: {e}")
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    SentenceTransformer = None
    faiss = None

from pipeline.tasks.base_task import BaseTask
from config.settings import PIPELINE_OUTPUT_DIR, PIPELINE_GPU_ENABLED, PIPELINE_EMBEDDING_MODEL

class SemanticIndexer(BaseTask):
    """
    Stage-03: Semantic Indexer

    Converts transcript text into semantic vector representations, utilizes pre-trained models,
    and implements FAISS vector search for efficient retrieval.
    """

    task_name = "semantic_indexer"
    requires_gpu = True  # Embedding generation benefits from GPU acceleration

    def run(self, job_id: str, transcription_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create semantic embeddings and index for the transcript

        Args:
            job_id: Unique identifier for the job
            transcription_result: Results from the transcription engine
            params: Additional parameters for the task

        Returns:
            Dictionary containing semantic index results
        """
        self.logger.info(f"Creating semantic index for job {job_id}")

        # Check if sentence_transformers is available
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            self.logger.warning("sentence_transformers not available, skipping semantic indexing")
            return {
                'status': 'skipped',
                'job_id': job_id,
                'reason': 'sentence_transformers not available',
                'metadata': {
                    'segment_count': 0,
                    'embedding_dimension': 0,
                    'model': 'none'
                }
            }

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Get transcript path from transcription result
        transcript_path = transcription_result.get('transcript_path')
        if not transcript_path or not os.path.exists(transcript_path):
            raise ValueError(f"Invalid transcript path from transcription engine: {transcript_path}")

        # Create job directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        index_dir = os.path.join(job_dir, "index")
        os.makedirs(index_dir, exist_ok=True)

        # Load transcript data
        with open(transcript_path, 'r') as f:
            transcript_data = json.load(f)

        # Extract segments from transcript
        segments = transcript_data.get('segments', [])
        if not segments:
            raise ValueError("No segments found in transcript data")

        # Prepare text segments for embedding
        segment_texts = []
        segment_metadata = []

        for segment in segments:
            text = segment.get('text', '').strip()
            if text:
                segment_texts.append(text)
                segment_metadata.append({
                    'start': segment.get('start', 0),
                    'end': segment.get('end', 0),
                    'text': text
                })

        # Load embedding model
        model_name = params.get('embedding_model', PIPELINE_EMBEDDING_MODEL)
        device = 'cuda' if PIPELINE_GPU_ENABLED and self.requires_gpu else 'cpu'

        # Double-check availability (should not happen due to earlier check)
        if faiss is None or SentenceTransformer is None:
            raise ValueError("sentence_transformers or faiss not available")

        self.logger.info(f"Loading embedding model {model_name} on {device}")
        model = SentenceTransformer(model_name, device=device)

        # Generate embeddings
        self.logger.info(f"Generating embeddings for {len(segment_texts)} segments")
        embeddings = model.encode(segment_texts, show_progress_bar=True, convert_to_numpy=True)

        # Create FAISS index
        dimension = embeddings.shape[1]
        index = faiss.IndexFlatL2(dimension)  # type: ignore # L2 distance index

        # Add embeddings to index
        index.add(embeddings.astype(np.float32))  # type: ignore

        # Save index to file
        index_path = os.path.join(index_dir, "faiss_index.bin")
        faiss.write_index(index, index_path)  # type: ignore

        # Save embeddings and metadata
        embeddings_path = os.path.join(index_dir, "embeddings.npy")
        metadata_path = os.path.join(index_dir, "segment_metadata.json")

        np.save(embeddings_path, embeddings)
        with open(metadata_path, 'w') as f:
            json.dump(segment_metadata, f, indent=2)

        # Create result with index data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'index_path': index_path,
            'embeddings_path': embeddings_path,
            'metadata_path': metadata_path,
            'model_name': model_name,
            'metadata': {
                'segment_count': len(segment_texts),
                'embedding_dimension': dimension,
                'model': model_name
            }
        }

        # Save state for idempotency
        self.save_state(job_id, result, self.task_name)

        return result

    def search_index(self, job_id: str, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Search the semantic index for similar segments

        Args:
            job_id: Unique identifier for the job
            query: Query text to search for
            top_k: Number of results to return

        Returns:
            List of matching segments with metadata
        """
        # Check if sentence_transformers is available
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            self.logger.warning("sentence_transformers not available, cannot search semantic index")
            return []

        # Load state to get paths
        state = self.load_state(job_id, self.task_name)
        if not state or state.get('status') != 'completed':
            raise ValueError(f"No completed semantic index found for job {job_id}")

        # Get paths from state
        index_path = state.get('index_path')
        metadata_path = state.get('metadata_path')
        model_name = state.get('model_name')

        if not index_path or not metadata_path or not os.path.exists(index_path) or not os.path.exists(metadata_path):
            raise ValueError(f"Index or metadata file not found for job {job_id}")

        # Load index and metadata
        if faiss is None or SentenceTransformer is None:
            raise ValueError("sentence_transformers or faiss not available")

        index = faiss.read_index(index_path)  # type: ignore
        with open(metadata_path, 'r') as f:
            segment_metadata = json.load(f)

        # Load model
        device = 'cuda' if PIPELINE_GPU_ENABLED and self.requires_gpu else 'cpu'
        model = SentenceTransformer(model_name, device=device)  # type: ignore

        # Encode query
        query_embedding = model.encode([query], convert_to_numpy=True)

        # Search index
        distances, indices = index.search(query_embedding.astype(np.float32), top_k)

        # Prepare results
        results = []
        for i, idx in enumerate(indices[0]):
            if idx < len(segment_metadata):
                results.append({
                    'segment': segment_metadata[idx],
                    'score': float(1.0 / (1.0 + distances[0][i]))  # Convert distance to similarity score
                })

        return results
# Create a task instance
semantic_indexer = SemanticIndexer()

def create_semantic_index(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the semantic_indexer task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return semantic_indexer.run(job_id, *args, **kwargs)
