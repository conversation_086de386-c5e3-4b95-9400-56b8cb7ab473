#!/usr/bin/env python3
"""
Base task for the Smart Video Highlight Generator Pipeline

This version uses direct function calls instead  for task execution.
"""

import os
import time
import json
import logging
import traceback
from typing import Dict, Any, Optional, List, Union, Tuple
from prometheus_client import Summary, Counter, Gauge

from config.settings import PIPELINE_METRICS_ENABLED, PIPELINE_GPU_ENABLED, PIPELINE_RESUME_ENABLED
from pipeline.job_state_manager import get_job_state_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Prometheus metrics
if PIPELINE_METRICS_ENABLED:
    TASK_LATENCY = Summary('task_latency_seconds', 'Task latency in seconds', ['task_name'])
    TASK_FAILURES = Counter('task_failures_total', 'Number of task failures', ['task_name'])
    TASK_SUCCESSES = Counter('task_successes_total', 'Number of task successes', ['task_name'])
    TASK_RETRIES = Counter('task_retries_total', 'Number of task retries', ['task_name'])
    TASK_RUNNING = Gauge('tasks_running', 'Number of tasks currently running', ['task_name'])
    TASK_SKIPPED = Counter('task_skipped_total', 'Number of tasks skipped due to resume', ['task_name'])
    GPU_MEMORY_USAGE = Gauge('gpu_memory_usage_bytes', 'GPU memory usage in bytes', ['device'])
    GPU_UTILIZATION = Gauge('gpu_utilization_percent', 'GPU utilization percentage', ['device'])

class BaseTask:
    """Base task with common functionality for all pipeline tasks"""

    # Abstract property to be defined by subclasses
    task_name = "base_task"

    # Whether this task requires GPU
    requires_gpu = False

    # Whether this task can be resumed
    supports_resume = True

    # Dependencies on other stages (used for determining if a stage can be skipped)
    dependencies = []

    def __init__(self):
        self.logger = logging.getLogger(self.task_name)

    def run(self, job_id: str, *args, **kwargs):
        """
        Run the task with metrics, logging, and resume functionality

        Args:
            job_id: Unique identifier for the job
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments

        Returns:
            Task result
        """
        start_time = time.time()

        # Update metrics for task start
        if PIPELINE_METRICS_ENABLED:
            TASK_RUNNING.labels(task_name=self.task_name).inc()

        self.logger.info(f"Starting task {self.task_name}")

        # Check GPU requirements
        if self.requires_gpu and not PIPELINE_GPU_ENABLED:
            self.logger.warning(f"Task {self.task_name} requires GPU but GPU is not enabled")

        # Update GPU metrics if enabled and required
        if PIPELINE_METRICS_ENABLED and PIPELINE_GPU_ENABLED and self.requires_gpu:
            self._update_gpu_metrics()

        try:
            if job_id and PIPELINE_RESUME_ENABLED and self.supports_resume:
                # Check if we can skip this task due to resume
                state_manager = get_job_state_manager(job_id)
                if state_manager.is_stage_completed(self.task_name):
                    # Get the cached result
                    cached_result = state_manager.get_stage_result(self.task_name)
                    if cached_result:
                        self.logger.info(f"Skipping task {self.task_name} for job {job_id} (resume enabled)")

                        # Update metrics for skipped task
                        if PIPELINE_METRICS_ENABLED:
                            TASK_SKIPPED.labels(task_name=self.task_name).inc()

                        return cached_result

            # Mark stage as started if we have a job_id
            if job_id:
                state_manager = get_job_state_manager(job_id)
                state_manager.mark_stage_started(self.task_name)

            # Call the task implementation
            result = self.process(job_id, *args, **kwargs)

            # Mark stage as completed if we have a job_id
            if job_id:
                state_manager = get_job_state_manager(job_id)
                state_manager.mark_stage_completed(self.task_name, result)

            # Update metrics for task success
            if PIPELINE_METRICS_ENABLED:
                TASK_SUCCESSES.labels(task_name=self.task_name).inc()
                TASK_LATENCY.labels(task_name=self.task_name).observe(time.time() - start_time)

            self.logger.info(f"Task {self.task_name} completed successfully in {time.time() - start_time:.2f}s")
            return result

        except Exception as e:
            # Update metrics for task failure
            if PIPELINE_METRICS_ENABLED:
                TASK_FAILURES.labels(task_name=self.task_name).inc()

            # Mark stage as failed if we have a job_id
            if job_id:
                state_manager = get_job_state_manager(job_id)
                state_manager.mark_stage_failed(self.task_name, str(e))

            self.logger.error(f"Task {self.task_name} failed: {str(e)}")
            self.logger.error(traceback.format_exc())
            raise
        finally:
            # Update metrics for task end
            if PIPELINE_METRICS_ENABLED:
                TASK_RUNNING.labels(task_name=self.task_name).dec()

    def process(self, *args, **kwargs):
        """
        Process the task (to be implemented by subclasses)

        Args:
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments

        Returns:
            Task result
        """
        raise NotImplementedError("Subclasses must implement this method")

    def _update_gpu_metrics(self):
        """Update GPU metrics if available"""
        try:
            import torch
            if torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    # Get GPU memory usage
                    memory_allocated = torch.cuda.memory_allocated(i)
                    memory_reserved = torch.cuda.memory_reserved(i)

                    # Update metrics
                    if PIPELINE_METRICS_ENABLED:
                        GPU_MEMORY_USAGE.labels(device=f"cuda:{i}").set(memory_allocated)

                    self.logger.debug(f"GPU {i} memory: {memory_allocated / 1024**2:.2f}MB allocated, "
                                     f"{memory_reserved / 1024**2:.2f}MB reserved")

                    # Record GPU utilization in job state if we have a job_id
                    job_id = self._get_current_job_id()
                    if job_id:
                        state_manager = get_job_state_manager(job_id)
                        state_manager.record_gpu_utilization(
                            f"cuda:{i}",
                            memory_allocated,
                            0.0  # We don't have utilization percentage from PyTorch
                        )
        except (ImportError, Exception) as e:
            self.logger.warning(f"Failed to update GPU metrics: {str(e)}")

    def _get_current_job_id(self) -> Optional[str]:
        """
        Get the job ID for the current task execution

        Returns:
            Job ID if available, None otherwise
        """
        # This is a placeholder - in a real implementation, we would need to
        # track the current job ID during task execution
        return None

    def save_state(self, job_id: str, state: Dict[str, Any], stage: str) -> str:
        """
        Save task state to a JSON file for idempotency

        Args:
            job_id: Unique identifier for the job
            state: Dictionary containing state to save
            stage: Pipeline stage name

        Returns:
            Path to the saved state file
        """
        # Use the JobStateManager to save state
        state_manager = get_job_state_manager(job_id)
        return state_manager.save_stage_result(stage, state)

    def load_state(self, job_id: str, stage: str) -> Optional[Dict[str, Any]]:
        """
        Load task state from a JSON file

        Args:
            job_id: Unique identifier for the job
            stage: Pipeline stage name

        Returns:
            Dictionary containing state or None if state file doesn't exist
        """
        # Use the JobStateManager to load state
        state_manager = get_job_state_manager(job_id)
        return state_manager.get_stage_result(stage)

    def record_api_call(self, job_id: str, service: str, endpoint: str, cost: float = 0.0) -> None:
        """
        Record an API call for cost tracking

        Args:
            job_id: Unique identifier for the job
            service: Name of the API service (e.g., 'openai', 'stability')
            endpoint: Specific API endpoint called
            cost: Estimated cost of the API call in USD
        """
        state_manager = get_job_state_manager(job_id)
        state_manager.record_api_call(service, endpoint, cost)
