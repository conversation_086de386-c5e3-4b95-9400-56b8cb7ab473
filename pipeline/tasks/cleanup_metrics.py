#!/usr/bin/env python3
"""
Cleanup & Metrics Task for the Smart Video Highlight Generator Pipeline


This version uses direct function calls instead  for task execution.
"""

import os
import json
import shutil
import time
from typing import Dict, Any, Optional, List, Tuple

from prometheus_client import Gauge, Counter

from pipeline.tasks.base_task import BaseTask
from config.settings import P<PERSON><PERSON>INE_OUTPUT_DIR, PIPELINE_METRICS_ENABLED, PIPELINE_CLEANUP_ENABLED

# Prometheus metrics
if PIPELINE_METRICS_ENABLED:
    JOB_DURATION = Gauge('job_duration_seconds', 'Total job duration in seconds', ['job_id'])
    JOB_COMPLETION = Counter('job_completion_total', 'Number of completed jobs', ['status'])
    DISK_USAGE = Gauge('disk_usage_bytes', 'Disk usage in bytes', ['job_id', 'stage'])
    CLIP_COUNT = Gauge('clip_count', 'Number of clips in the final video', ['job_id'])
    HIGHLIGHT_DURATION = Gauge('highlight_duration_seconds', 'Duration of the final highlight video', ['job_id'])

class CleanupMetrics(BaseTask):
    """
    Stage-11: Cleanup & Metrics

    Performs housekeeping by removing temporary files and scratch space,
    emits comprehensive metrics, and creates annotations for pipeline visualization.
    """

    task_name = "cleanup_metrics"
    requires_gpu = False

    def run(self, job_id: str, publisher_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform cleanup and emit metrics

        Args:
            job_id: Unique identifier for the job
            publisher_result: Results from the deliverables publisher
            params: Additional parameters for the task

        Returns:
            Dictionary containing cleanup and metrics results
        """
        self.logger.info(f"Performing cleanup and emitting metrics for job {job_id}")

        # Check if we've already processed this job
        existing_state = self.load_state(job_id, self.task_name)
        if existing_state and existing_state.get('status') == 'completed':
            self.logger.info(f"Using cached results for job {job_id}")
            return existing_state

        # Get job directory
        job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)

        # Get output directory
        output_dir = publisher_result.get('output_dir')
        if not output_dir or not os.path.exists(output_dir):
            raise ValueError(f"Invalid output directory from publisher: {output_dir}")

        # Get job start time
        job_start_time = self._get_job_start_time(job_dir)

        # Calculate job duration
        job_duration = time.time() - job_start_time

        # Collect metrics
        metrics = self._collect_metrics(job_id, job_dir, publisher_result, job_duration, job_start_time)

        # Emit metrics
        if PIPELINE_METRICS_ENABLED:
            self._emit_metrics(job_id, metrics)

        # Perform cleanup if requested and globally enabled
        cleanup_temp = params.get('cleanup_temp', PIPELINE_CLEANUP_ENABLED)
        if cleanup_temp and PIPELINE_CLEANUP_ENABLED:
            self.logger.info(f"Performing cleanup for job {job_id}")
            self._cleanup_temp_files(job_dir, output_dir)
        else:
            reason = "globally disabled" if not PIPELINE_CLEANUP_ENABLED else "cleanup_temp=False"
            self.logger.info(f"Cleanup skipped for job {job_id} ({reason})")

        # Create result with metrics data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'output_dir': output_dir,
            'final_video_path': publisher_result.get('final_video_path'),
            'job_duration': job_duration,
            'metrics': metrics,
            'cleanup_performed': cleanup_temp
        }

        # Save state for idempotency
        self.save_state(job_id, result, self.task_name)

        # Increment job completion counter
        if PIPELINE_METRICS_ENABLED:
            JOB_COMPLETION.labels(status='success').inc()

        return result

    def _get_job_start_time(self, job_dir: str) -> float:
        """
        Get the job start time from the job directory

        Args:
            job_dir: Job directory

        Returns:
            Job start time as a timestamp
        """
        # Check if params.json exists
        params_path = os.path.join(job_dir, "params.json")
        if os.path.exists(params_path):
            # Use file creation time as job start time
            return os.path.getctime(params_path)
        else:
            # Use directory creation time as fallback
            return os.path.getctime(job_dir)

    def _collect_metrics(self, job_id: str, job_dir: str, publisher_result: Dict[str, Any], job_duration: float, job_start_time: float) -> Dict[str, Any]:
        """
        Collect metrics for the job

        Args:
            job_id: Unique identifier for the job
            job_dir: Job directory
            publisher_result: Results from the deliverables publisher
            job_duration: Job duration in seconds
            job_start_time: Job start time as a timestamp

        Returns:
            Dictionary containing metrics
        """
        # Initialize metrics dictionary
        metrics = {
            'job_duration': job_duration,
            'disk_usage': {},
            'stage_durations': {},
            'final_video': {
                'clip_count': publisher_result.get('metadata', {}).get('clip_count', 0),
                'total_duration': publisher_result.get('metadata', {}).get('total_duration', 0),
                'output_format': publisher_result.get('metadata', {}).get('output_format', 'original')
            }
        }

        # Collect disk usage for each stage
        for stage_dir in os.listdir(job_dir):
            stage_path = os.path.join(job_dir, stage_dir)
            if os.path.isdir(stage_path):
                # Calculate directory size
                dir_size = self._get_directory_size(stage_path)
                metrics['disk_usage'][stage_dir] = dir_size

        # Collect stage durations
        state_dir = os.path.join(job_dir, "state")
        if os.path.exists(state_dir):
            for state_file in os.listdir(state_dir):
                if state_file.endswith('.json'):
                    stage_name = os.path.splitext(state_file)[0]
                    state_path = os.path.join(state_dir, state_file)

                    # Use file modification time as stage completion time
                    stage_time = os.path.getmtime(state_path)
                    metrics['stage_durations'][stage_name] = stage_time - job_start_time

        return metrics

    def _emit_metrics(self, job_id: str, metrics: Dict[str, Any]) -> None:
        """
        Emit metrics to Prometheus

        Args:
            job_id: Unique identifier for the job
            metrics: Metrics data
        """
        if not PIPELINE_METRICS_ENABLED:
            return

        # Set job duration
        JOB_DURATION.labels(job_id=job_id).set(metrics['job_duration'])

        # Set disk usage
        for stage, size in metrics.get('disk_usage', {}).items():
            DISK_USAGE.labels(job_id=job_id, stage=stage).set(size)

        # Set clip count
        CLIP_COUNT.labels(job_id=job_id).set(metrics.get('final_video', {}).get('clip_count', 0))

        # Set highlight duration
        HIGHLIGHT_DURATION.labels(job_id=job_id).set(metrics.get('final_video', {}).get('total_duration', 0))

    def _cleanup_temp_files(self, job_dir: str, output_dir: str) -> None:
        """
        Clean up temporary files

        Args:
            job_dir: Job directory
            output_dir: Output directory
        """
        # Get list of directories to keep
        keep_dirs = [
            'state',           # Always keep state directory
            'deliverables',    # Keep final deliverables
            'metadata',        # Keep metadata files
            'logs'             # Keep log files if they exist
        ]

        # Safety check: ensure we're only working within the job directory
        if not os.path.exists(job_dir):
            self.logger.warning(f"Job directory does not exist: {job_dir}")
            return

        # Remove directories that are not in the keep list
        removed_dirs = []
        for item in os.listdir(job_dir):
            item_path = os.path.join(job_dir, item)
            if os.path.isdir(item_path) and item not in keep_dirs:
                # Additional safety check: ensure we're not deleting the output directory
                if os.path.abspath(item_path) == os.path.abspath(output_dir):
                    self.logger.info(f"Skipping output directory: {item_path}")
                    continue

                try:
                    self.logger.info(f"Removing temporary directory: {item_path}")
                    shutil.rmtree(item_path)
                    removed_dirs.append(item)
                except Exception as e:
                    self.logger.warning(f"Error removing directory {item_path}: {str(e)}")

        if removed_dirs:
            self.logger.info(f"Cleanup completed. Removed directories: {removed_dirs}")
        else:
            self.logger.info("No temporary directories to clean up")

    def _get_directory_size(self, path: str) -> int:
        """
        Get the size of a directory in bytes

        Args:
            path: Directory path

        Returns:
            Directory size in bytes
        """
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                total_size += os.path.getsize(file_path)

        return total_size
# Create a task instance
cleanup_metrics = CleanupMetrics()

def cleanup_and_metrics(job_id: str, *args, **kwargs) -> Dict[str, Any]:
    """
    Run the cleanup_metrics task

    Args:
        job_id: Unique identifier for the job
        *args: Additional positional arguments
        **kwargs: Additional keyword arguments

    Returns:
        Task result
    """
    return cleanup_metrics.run(job_id, *args, **kwargs)
