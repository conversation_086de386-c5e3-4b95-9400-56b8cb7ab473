#!/usr/bin/env python3
"""
Job State Manager for the Smart Video Highlight Generator Pipeline

This module provides a centralized way to manage job state across pipeline stages,
enabling robust resume functionality.
"""

import os
import json
import time
import logging
from typing import Dict, Any, Optional, List, Tuple, Set
from datetime import datetime

from config.settings import PIPELINE_OUTPUT_DIR

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger("job_state_manager")

class JobStateManager:
    """
    Manages the state of pipeline jobs, providing resume functionality

    This class handles:
    - Tracking which stages have completed successfully
    - Storing and retrieving stage results
    - Determining which stages can be skipped during resume
    - Maintaining metrics about job execution
    """

    def __init__(self, job_id: str):
        """
        Initialize the job state manager

        Args:
            job_id: Unique identifier for the job
        """
        self.job_id = job_id
        self.job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
        self.state_dir = os.path.join(self.job_dir, "state")

        # Create directories if they don't exist
        os.makedirs(self.job_dir, exist_ok=True)
        os.makedirs(self.state_dir, exist_ok=True)

        # Initialize job state file if it doesn't exist
        self.job_state_file = os.path.join(self.state_dir, "job_state.json")
        if not os.path.exists(self.job_state_file):
            self._initialize_job_state()

    def _initialize_job_state(self) -> None:
        """Initialize the job state file with default values"""
        # Create state directory if it doesn't exist
        os.makedirs(self.state_dir, exist_ok=True)

        job_state = {
            'job_id': self.job_id,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'status': 'initializing',
            'completed_stages': [],
            'current_stage': None,
            'metrics': {
                'start_time': time.time(),
                'stage_durations': {},
                'api_calls': {},
                'gpu_utilization': {}
            }
        }

        # Use atomic write for initialization too
        temp_file = self.job_state_file + '.tmp'
        try:
            with open(temp_file, 'w') as f:
                json.dump(job_state, f, indent=2)
            # Atomic move
            os.rename(temp_file, self.job_state_file)
        except Exception as e:
            # Clean up temp file if it exists
            if os.path.exists(temp_file):
                os.remove(temp_file)
            raise

    def get_job_state(self) -> Dict[str, Any]:
        """
        Get the current job state

        Returns:
            Dictionary containing the job state
        """
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if not os.path.exists(self.job_state_file):
                    self._initialize_job_state()

                with open(self.job_state_file, 'r') as f:
                    content = f.read().strip()
                    if not content:
                        # Empty file, re-initialize
                        self._initialize_job_state()
                        continue
                    return json.loads(content)
            except (json.JSONDecodeError, FileNotFoundError) as e:
                logger.warning(f"Error loading job state (attempt {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    # Re-initialize and try again
                    self._initialize_job_state()
                    import time
                    time.sleep(0.1)  # Small delay before retry
                else:
                    # Last attempt failed, re-initialize and return default state
                    logger.error(f"Failed to load job state after {max_retries} attempts, re-initializing")
                    self._initialize_job_state()
                    with open(self.job_state_file, 'r') as f:
                        return json.load(f)
            except Exception as e:
                logger.error(f"Unexpected error loading job state: {str(e)}")
                if attempt < max_retries - 1:
                    self._initialize_job_state()
                    import time
                    time.sleep(0.1)
                else:
                    raise

    def update_job_state(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update the job state with the provided values

        Args:
            updates: Dictionary containing values to update

        Returns:
            Updated job state dictionary
        """
        job_state = self.get_job_state()

        # Update values
        for key, value in updates.items():
            if key == 'metrics' and isinstance(value, dict):
                # Merge metrics dictionaries
                for metric_key, metric_value in value.items():
                    if metric_key in job_state['metrics'] and isinstance(metric_value, dict):
                        job_state['metrics'][metric_key].update(metric_value)
                    else:
                        job_state['metrics'][metric_key] = metric_value
            else:
                job_state[key] = value

        # Always update the timestamp
        job_state['updated_at'] = datetime.now().isoformat()

        # Save the updated state with atomic write
        temp_file = self.job_state_file + '.tmp'
        try:
            with open(temp_file, 'w') as f:
                json.dump(job_state, f, indent=2)
            # Atomic move
            os.rename(temp_file, self.job_state_file)
        except Exception as e:
            # Clean up temp file if it exists
            if os.path.exists(temp_file):
                os.remove(temp_file)
            raise

        return job_state

    def mark_stage_started(self, stage_name: str) -> None:
        """
        Mark a stage as started

        Args:
            stage_name: Name of the stage
        """
        self.update_job_state({
            'current_stage': stage_name,
            'status': 'in_progress',
            'metrics': {
                'stage_durations': {
                    stage_name: {
                        'start_time': time.time()
                    }
                }
            }
        })

        logger.info(f"Stage {stage_name} started for job {self.job_id}")

    def mark_stage_completed(self, stage_name: str, result: Dict[str, Any]) -> None:
        """
        Mark a stage as completed

        Args:
            stage_name: Name of the stage
            result: Result dictionary from the stage
        """
        job_state = self.get_job_state()
        completed_stages = job_state.get('completed_stages', [])

        # Add to completed stages if not already there
        if stage_name not in completed_stages:
            completed_stages.append(stage_name)

        # Update stage duration
        stage_start_time = job_state.get('metrics', {}).get('stage_durations', {}).get(stage_name, {}).get('start_time')
        stage_duration = time.time() - stage_start_time if stage_start_time else 0

        self.update_job_state({
            'completed_stages': completed_stages,
            'current_stage': None,
            'metrics': {
                'stage_durations': {
                    stage_name: {
                        'end_time': time.time(),
                        'duration': stage_duration
                    }
                }
            }
        })

        # Save the stage result
        self.save_stage_result(stage_name, result)

        logger.info(f"Stage {stage_name} completed for job {self.job_id} in {stage_duration:.2f}s")

    def mark_stage_failed(self, stage_name: str, error: str) -> None:
        """
        Mark a stage as failed

        Args:
            stage_name: Name of the stage
            error: Error message
        """
        job_state = self.get_job_state()

        # Update stage duration
        stage_start_time = job_state.get('metrics', {}).get('stage_durations', {}).get(stage_name, {}).get('start_time')
        stage_duration = time.time() - stage_start_time if stage_start_time else 0

        self.update_job_state({
            'status': 'failed',
            'error': error,
            'current_stage': None,
            'metrics': {
                'stage_durations': {
                    stage_name: {
                        'end_time': time.time(),
                        'duration': stage_duration,
                        'error': error
                    }
                }
            }
        })

        logger.error(f"Stage {stage_name} failed for job {self.job_id}: {error}")

    def is_stage_completed(self, stage_name: str) -> bool:
        """
        Check if a stage has been completed

        Args:
            stage_name: Name of the stage

        Returns:
            True if the stage has been completed, False otherwise
        """
        job_state = self.get_job_state()
        completed_stages = job_state.get('completed_stages', [])
        return stage_name in completed_stages

    def save_stage_result(self, stage_name: str, result: Dict[str, Any]) -> str:
        """
        Save a stage result to a file

        Args:
            stage_name: Name of the stage
            result: Result dictionary from the stage

        Returns:
            Path to the saved result file
        """
        result_file = os.path.join(self.state_dir, f"{stage_name}.json")
        temp_file = result_file + '.tmp'

        try:
            with open(temp_file, 'w') as f:
                json.dump(result, f, indent=2)
            # Atomic move
            os.rename(temp_file, result_file)
        except Exception as e:
            # Clean up temp file if it exists
            if os.path.exists(temp_file):
                os.remove(temp_file)
            raise

        return result_file

    def get_stage_result(self, stage_name: str) -> Optional[Dict[str, Any]]:
        """
        Get a stage result from a file

        Args:
            stage_name: Name of the stage

        Returns:
            Result dictionary from the stage or None if not found
        """
        result_file = os.path.join(self.state_dir, f"{stage_name}.json")

        if not os.path.exists(result_file):
            return None

        try:
            with open(result_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading stage result for {stage_name}: {str(e)}")
            return None

    def get_all_stage_results(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all stage results

        Returns:
            Dictionary mapping stage names to their results
        """
        results = {}

        for filename in os.listdir(self.state_dir):
            if filename.endswith('.json') and filename != 'job_state.json':
                stage_name = os.path.splitext(filename)[0]
                result = self.get_stage_result(stage_name)
                if result:
                    results[stage_name] = result

        return results

    def get_job_progress(self) -> Dict[str, Any]:
        """
        Get detailed progress information for the job

        Returns:
            Dictionary containing progress information
        """
        job_state = self.get_job_state()
        stage_results = self.get_all_stage_results()

        # Calculate overall progress based on the total number of pipeline stages (12)
        # Import here to avoid circular imports
        try:
            from pipeline.controller import PIPELINE_STAGES
            total_stages = len(PIPELINE_STAGES)
        except ImportError:
            # Fallback to hardcoded value if import fails
            total_stages = 12

        completed_stages_count = len(job_state.get('completed_stages', []))
        progress_percent = (completed_stages_count / total_stages) * 100 if total_stages > 0 else 0

        return {
            'job_id': self.job_id,
            'status': job_state.get('status', 'unknown'),
            'current_stage': job_state.get('current_stage'),
            'completed_stages': job_state.get('completed_stages', []),
            'progress_percent': progress_percent,
            'created_at': job_state.get('created_at'),
            'updated_at': job_state.get('updated_at'),
            'error': job_state.get('error')
        }

    def record_api_call(self, service: str, endpoint: str, cost: float = 0.0) -> None:
        """
        Record an API call for cost tracking

        Args:
            service: Name of the API service (e.g., 'openai', 'stability')
            endpoint: Specific API endpoint called
            cost: Estimated cost of the API call in USD
        """
        job_state = self.get_job_state()
        api_calls = job_state.get('metrics', {}).get('api_calls', {})

        # Initialize service if not exists
        if service not in api_calls:
            api_calls[service] = {}

        # Initialize endpoint if not exists
        if endpoint not in api_calls[service]:
            api_calls[service][endpoint] = {
                'count': 0,
                'total_cost': 0.0
            }

        # Update counts
        api_calls[service][endpoint]['count'] += 1
        api_calls[service][endpoint]['total_cost'] += cost

        # Update job state
        self.update_job_state({
            'metrics': {
                'api_calls': api_calls
            }
        })

    def record_gpu_utilization(self, device: str, memory_used: int, utilization: float) -> None:
        """
        Record GPU utilization metrics

        Args:
            device: GPU device identifier (e.g., 'cuda:0')
            memory_used: Memory used in bytes
            utilization: GPU utilization percentage (0-100)
        """
        job_state = self.get_job_state()
        gpu_metrics = job_state.get('metrics', {}).get('gpu_utilization', {})

        # Initialize device if not exists
        if device not in gpu_metrics:
            gpu_metrics[device] = {
                'samples': 0,
                'max_memory_used': 0,
                'avg_utilization': 0.0
            }

        # Update metrics
        device_metrics = gpu_metrics[device]
        device_metrics['samples'] += 1
        device_metrics['max_memory_used'] = max(device_metrics['max_memory_used'], memory_used)

        # Update running average of utilization
        current_avg = device_metrics['avg_utilization']
        current_samples = device_metrics['samples']
        device_metrics['avg_utilization'] = (current_avg * (current_samples - 1) + utilization) / current_samples

        # Update job state
        self.update_job_state({
            'metrics': {
                'gpu_utilization': gpu_metrics
            }
        })

    def finalize_job(self, status: str = 'completed') -> Dict[str, Any]:
        """
        Finalize the job and calculate overall metrics

        Args:
            status: Final job status ('completed' or 'failed')

        Returns:
            Final job state dictionary
        """
        job_state = self.get_job_state()
        start_time = job_state.get('metrics', {}).get('start_time', time.time())
        total_duration = time.time() - start_time

        # Calculate API costs
        api_calls = job_state.get('metrics', {}).get('api_calls', {})
        total_api_cost = 0.0

        for service in api_calls.values():
            for endpoint in service.values():
                total_api_cost += endpoint.get('total_cost', 0.0)

        # Update job state with final metrics
        final_state = self.update_job_state({
            'status': status,
            'metrics': {
                'end_time': time.time(),
                'total_duration': total_duration,
                'total_api_cost': total_api_cost
            }
        })

        logger.info(f"Job {self.job_id} finalized with status {status} in {total_duration:.2f}s")
        logger.info(f"Total API cost: ${total_api_cost:.4f}")

        return final_state

# Create a function to get a JobStateManager instance for a job
def get_job_state_manager(job_id: str) -> JobStateManager:
    """
    Get a JobStateManager instance for a job

    Args:
        job_id: Unique identifier for the job

    Returns:
        JobStateManager instance
    """
    return JobStateManager(job_id)
