#!/usr/bin/env python3
"""
Multi-Part Video Processor for handling split videos in the pipeline

This module provides functionality to process multiple video parts
and combine their results appropriately.
"""

import os
import json
import logging
import time
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class MultiPartProcessor:
    """
    Processor for handling multiple video parts in the pipeline
    """

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

    def should_process_all_parts(self, preflight_result: Dict[str, Any], params: Dict[str, Any]) -> bool:
        """
        Determine if all video parts should be processed or just the first one

        Args:
            preflight_result: Results from preflight validator
            params: Pipeline parameters

        Returns:
            True if all parts should be processed
        """
        # Check if video was split
        if not preflight_result.get('needs_splitting', False):
            return False

        # Check if test mode is enabled (process only first part for testing)
        if params.get('testmode', False):
            self.logger.info("Test mode enabled: processing only first video part")
            return False

        # Check if user explicitly requested single part processing
        if params.get('process_single_part_only', False):
            self.logger.info("Single part processing requested: processing only first video part")
            return False

        return True

    def get_parts_to_process(self, preflight_result: Dict[str, Any], params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get the list of video parts to process

        Args:
            preflight_result: Results from preflight validator
            params: Pipeline parameters

        Returns:
            List of video parts to process
        """
        video_parts = preflight_result.get('video_parts', [])

        if not video_parts:
            return []

        if self.should_process_all_parts(preflight_result, params):
            self.logger.info(f"Processing all {len(video_parts)} video parts")
            return video_parts
        else:
            self.logger.info("Processing only the first video part")
            return [video_parts[0]]

    def create_part_job_id(self, base_job_id: str, part_number: int) -> str:
        """
        Create a job ID for a specific video part

        Args:
            base_job_id: Base job ID
            part_number: Part number

        Returns:
            Job ID for the part
        """
        return f"{base_job_id}_part{part_number}"

    def combine_highlights_results(self, part_results: List[Dict[str, Any]],
                                 original_video_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Combine highlights results from multiple video parts

        Args:
            part_results: List of results from each part
            original_video_info: Information about the original video

        Returns:
            Combined results
        """
        if not part_results:
            return {}

        if len(part_results) == 1:
            return part_results[0]

        # Combine highlights from all parts
        combined_highlights = []
        total_clips = 0
        total_duration = 0.0

        for i, part_result in enumerate(part_results, 1):
            part_highlights = part_result.get('highlights', [])
            part_info = original_video_info['video_parts'][i-1]  # 0-indexed
            part_start_offset = part_info['start_time']

            # Adjust timestamps to be relative to original video
            for highlight in part_highlights:
                adjusted_highlight = highlight.copy()
                adjusted_highlight['start'] += part_start_offset
                adjusted_highlight['end'] += part_start_offset
                adjusted_highlight['part_number'] = i
                adjusted_highlight['original_part_start'] = highlight['start']
                adjusted_highlight['original_part_end'] = highlight['end']
                combined_highlights.append(adjusted_highlight)

            total_clips += part_result.get('total_clips', 0)
            total_duration += part_result.get('total_duration', 0.0)

        # Sort highlights by adjusted start time
        combined_highlights.sort(key=lambda x: x['start'])

        # Create combined result
        combined_result = {
            'status': 'completed',
            'highlights': combined_highlights,
            'total_clips': total_clips,
            'total_duration': total_duration,
            'total_parts_processed': len(part_results),
            'original_video_duration': original_video_info.get('metadata', {}).get('duration', 0),
            'processing_summary': {
                'parts_processed': len(part_results),
                'highlights_per_part': [len(result.get('highlights', [])) for result in part_results],
                'duration_per_part': [result.get('total_duration', 0.0) for result in part_results]
            }
        }

        # Include metadata from the first part as base
        if part_results[0].get('metadata'):
            combined_result['metadata'] = part_results[0]['metadata'].copy()
            combined_result['metadata']['is_multi_part'] = True
            combined_result['metadata']['total_parts'] = len(part_results)

        return combined_result

    def generate_multi_part_summary(self, preflight_result: Dict[str, Any],
                                   final_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a summary of multi-part processing

        Args:
            preflight_result: Results from preflight validator
            final_results: Final processing results

        Returns:
            Summary information
        """
        video_parts = preflight_result.get('video_parts', [])

        if not video_parts:
            return {}

        summary = {
            'video_splitting_info': {
                'original_duration': preflight_result.get('metadata', {}).get('duration', 0),
                'max_part_duration': 14400,  # 240 minutes (4 hours)
                'total_parts_created': len(video_parts),
                'parts_info': [
                    {
                        'part_number': part['part_number'],
                        'filename': part['filename'],
                        'duration': part['duration'],
                        'time_range': f"{part['start_time']:.1f}s - {part['end_time']:.1f}s"
                    }
                    for part in video_parts
                ]
            },
            'processing_summary': {
                'parts_processed': final_results.get('total_parts_processed', 1),
                'total_highlights_found': len(final_results.get('highlights', [])),
                'total_highlight_duration': final_results.get('total_duration', 0.0)
            }
        }

        return summary


# Create a default instance
multi_part_processor = MultiPartProcessor()


def should_process_multiple_parts(preflight_result: Dict[str, Any], params: Dict[str, Any]) -> bool:
    """
    Check if multiple video parts should be processed

    Args:
        preflight_result: Results from preflight validator
        params: Pipeline parameters

    Returns:
        True if multiple parts should be processed
    """
    return multi_part_processor.should_process_all_parts(preflight_result, params)


def get_video_parts_to_process(preflight_result: Dict[str, Any], params: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Get video parts that should be processed

    Args:
        preflight_result: Results from preflight validator
        params: Pipeline parameters

    Returns:
        List of video parts to process
    """
    return multi_part_processor.get_parts_to_process(preflight_result, params)


def combine_multi_part_results(part_results: List[Dict[str, Any]],
                              original_video_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Combine results from multiple video parts

    Args:
        part_results: Results from each part
        original_video_info: Original video information

    Returns:
        Combined results
    """
    return multi_part_processor.combine_highlights_results(part_results, original_video_info)
