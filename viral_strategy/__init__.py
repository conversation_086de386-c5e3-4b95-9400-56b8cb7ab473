"""
Viral Strategy Module

Production-ready module for generating comprehensive viral marketing strategies
for video highlight clips using OpenAI API integration.

Features:
- Cost-effective one-shot API calls
- Comprehensive viral analysis and scoring (0-100)
- Platform-specific optimization (YouTube Shorts, Instagram Reels, TikTok)
- Intelligent caching and rate limiting
- JSON and Markdown output formats
- Content categorization and analysis
- Viral title generation with psychological triggers
- Strategic hashtag recommendations
- Thumbnail concepts with visual psychology
- Advanced engagement strategies

Usage:
    from viral_strategy import ViralStrategyGenerator, generate_viral_strategy_for_job

    # Simple usage
    strategy_path = generate_viral_strategy_for_job(job_id, highlights_dir)

    # Advanced usage
    generator = ViralStrategyGenerator(openai_model="gpt-4o-mini")
    strategy = generator.generate_clip_strategy(1, "clip.mp4", "content", 30.0, 0.85)
"""

from .viral_strategy_generator import ViralStrategyGenerator, generate_viral_strategy_for_job

__version__ = "1.0.0"
__all__ = ['ViralStrategyGenerator', 'generate_viral_strategy_for_job']
