#!/usr/bin/env python3
"""
Viral Strategy Generator

Production-ready module for generating comprehensive viral marketing strategies
for video highlight clips using OpenAI API integration.

Features:
- One-shot API calls for cost efficiency
- Comprehensive viral analysis and scoring
- Platform-specific optimization
- JSON and Markdown output formats
- Intelligent caching and rate limiting

Author: AI-Powered Video Highlights System
"""

import json
import logging
import time
from typing import List, Dict, Optional
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

# OpenAI integration
try:
    from openai import OpenAI
    from config.settings import OPENAI_API_KEY
    OPENAI_AVAILABLE = True
except ImportError as e:
    OPENAI_AVAILABLE = False
    logger.error(f"OpenAI not available: {e}")


class ViralStrategyGenerator:
    """
    Production-ready viral marketing strategy generator using OpenAI API.

    Features:
    - Cost-effective one-shot API calls
    - Comprehensive viral analysis and scoring
    - Intelligent caching and rate limiting
    - JSON and Markdown output formats
    """

    # Class constants
    CONTENT_CATEGORIES = {
        "relationships": ["relationship", "love", "partner", "dating", "marriage", "romance", "couple"],
        "science_psychology": ["science", "consciousness", "brain", "research", "study", "psychology", "neuroscience"],
        "career_success": ["career", "work", "job", "success", "money", "business", "entrepreneur", "productivity"],
        "life_advice": ["life", "decision", "choice", "important", "crucial", "wisdom", "advice", "growth"],
        "health_wellness": ["health", "wellness", "fitness", "diet", "exercise", "medical", "body", "mental health"],
        "general_wisdom": []  # Default fallback
    }

    PLATFORM_RECOMMENDATIONS = {
        "youtube_shorts": "Perfect for YouTube Shorts - vertical format optimized",
        "instagram_reels": "Ideal for Instagram Reels - engaging content",
        "tiktok": "Great for TikTok - short-form vertical video",
        "twitter": "Can be shared on Twitter/X for engagement"
    }

    def __init__(self, openai_model: str = "gpt-4o-mini", max_requests: int = 50):
        """
        Initialize the viral strategy generator.

        Args:
            openai_model: OpenAI model to use for generation
            max_requests: Maximum API requests per session for rate limiting

        Raises:
            RuntimeError: If OpenAI is not available or client initialization fails
        """
        if not OPENAI_AVAILABLE:
            raise RuntimeError("OpenAI is not available. Please install openai package and set OPENAI_API_KEY.")

        self.openai_model = openai_model
        self.max_requests_per_session = max_requests
        self.api_request_count = 0
        self.request_cache: Dict[str, Dict] = {}

        # Initialize OpenAI client
        try:
            self.openai_client = OpenAI(api_key=OPENAI_API_KEY)
            logger.info(f"OpenAI client initialized with model: {openai_model}")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            raise RuntimeError(f"OpenAI client initialization failed: {e}")

    def categorize_content(self, content_text: str) -> str:
        """
        Categorize content based on keywords for metadata purposes.

        Args:
            content_text: The text content to analyze

        Returns:
            Category name
        """
        content_lower = content_text.lower()

        # Check each category for keyword matches
        for category, keywords in self.CONTENT_CATEGORIES.items():
            if category == "general_wisdom":  # Skip default category
                continue

            if any(word in content_lower for word in keywords):
                return category

        return "general_wisdom"

    def _build_strategy_prompt(self, content_text: str, video_metadata: Dict) -> str:
        """
        Build comprehensive strategy generation prompt.

        Args:
            content_text: The video content text
            video_metadata: Video metadata dictionary

        Returns:
            Formatted prompt string
        """
        return f"""You are a world-class viral content strategist and analyst specializing in social media optimization for YouTube Shorts, Instagram Reels, and TikTok.

Analyze this video clip content and generate a COMPLETE viral strategy with analysis in ONE response:

CONTENT: "{content_text[:500]}"
DURATION: {video_metadata.get('duration', 'Unknown')} seconds
ENGAGEMENT_SCORE: {video_metadata.get('score', 'Unknown')}

Generate a comprehensive response with ALL of the following components:

1. VIRAL TITLES (5 options): Create compelling, click-worthy titles using psychological triggers, power words, and proven viral patterns.
2. OPTIMIZED DESCRIPTION: Write an engaging description with hooks, keywords, engagement encouragement, and clear call-to-action.
3. STRATEGIC HASHTAGS: Provide 10-15 hashtags for social media platforms (Instagram, TikTok) including high-volume trending tags, niche-specific tags, and long-tail keywords.
4. YOUTUBE TAGS: Based on YouTube's guidance that "Tags can be useful if content in your video is commonly misspelled. Otherwise, tags play a minimal role in helping viewers find your video," provide 5-10 YouTube tags that focus ONLY on:
   - Common misspellings of key terms/names mentioned in the video
   - Alternative spellings or variations of important keywords
   - Different ways people might search for the same concept
   - Keep total under 500 characters with comma separation
   - Do NOT use tags for general discoverability as they have minimal impact
5. THUMBNAIL CONCEPT: Describe compelling thumbnail strategy with visual elements, text overlay, color psychology, and emotional expressions.
6. CONTENT OPTIMIZATION: Suggest specific improvements for hook strength, pacing, structure, emotional peaks, and call-to-action.
7. ENGAGEMENT STRATEGIES: Recommend tactics for comment generation, share encouragement, community building, and cross-platform promotion.
8. VIRAL POTENTIAL SCORE (0-100): Rate overall viral potential based on content quality, emotional impact, hook strength, shareability, and platform optimization.
9. VIRAL MECHANICS ANALYSIS: Identify psychological triggers, social proof elements, shareability factors, timing relevance, and target audience appeal.
10. PLATFORM-SPECIFIC FACTORS: Analyze YouTube Shorts, Instagram Reels, and TikTok compatibility with specific optimization needs.
11. ENGAGEMENT PREDICTIONS: Estimate view count range, engagement rate, share probability, comment generation potential, and viral coefficient.
12. IMPROVEMENT ROADMAP: List priority changes (High/Medium/Low) with specific action items, expected impact, and implementation timeline.
13. RISK ASSESSMENT: Evaluate potential negative reactions, controversy risk level, platform policy compliance, and brand safety.

Respond with a JSON object containing all these elements with specific, actionable insights. Use this exact structure:

{{
  "viral_titles": ["title1", "title2", "title3", "title4", "title5"],
  "optimized_description": "description text",
  "strategic_hashtags": ["#tag1", "#tag2", ...],
  "youtube_tags": ["tag1", "tag2", "tag3"],
  "thumbnail_concept": "thumbnail description",
  "content_optimization": {{
    "hook_strength": "suggestions",
    "pacing_structure": "recommendations",
    "emotional_peaks": "optimization tips",
    "call_to_action": "improvements"
  }},
  "engagement_strategies": {{
    "comment_generation": "tactics",
    "share_encouragement": "methods",
    "community_building": "approaches",
    "cross_platform": "strategies"
  }},
  "viral_potential_score": 85,
  "viral_mechanics_analysis": {{
    "psychological_triggers": ["trigger1", "trigger2"],
    "social_proof_elements": "analysis",
    "shareability_factors": "factors",
    "target_audience_appeal": "assessment"
  }},
  "platform_specific_viral_factors": {{
    "youtube_shorts": "optimization needs",
    "instagram_reels": "viral elements",
    "tiktok": "algorithm compatibility"
  }},
  "engagement_prediction": {{
    "expected_view_count_range": "10K-50K",
    "likely_engagement_rate": "8-12%",
    "share_probability": "High",
    "comment_generation_potential": "Strong"
  }},
  "improvement_roadmap": {{
    "high_priority": ["change1", "change2"],
    "medium_priority": ["change3", "change4"],
    "low_priority": ["change5"]
  }},
  "risk_assessment": {{
    "controversy_risk_level": "Low",
    "platform_policy_compliance": "High",
    "brand_safety": "Safe"
  }}
}}"""

    def _generate_complete_strategy(self, content_text: str, video_metadata: Dict) -> Optional[Dict]:
        """
        Generate comprehensive viral strategy and analysis in a single API call (cost-effective)

        Args:
            content_text: The text content of the video clip
            video_metadata: Additional metadata about the video

        Returns:
            Dictionary with complete strategy and viral analysis or None if failed
        """
        if not self.openai_client:
            return None

        # Check rate limiting
        if self.api_request_count >= self.max_requests_per_session:
            logger.warning("OpenAI API rate limit reached for complete strategy generation")
            return None

        # Create cache key
        cache_key = f"complete_strategy_{hash(content_text[:200])}"
        if cache_key in self.request_cache:
            logger.info("Using cached complete strategy result")
            return self.request_cache[cache_key]

        try:
            prompt = self._build_strategy_prompt(content_text, video_metadata)

            # Make API request with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = self.openai_client.chat.completions.create(
                        model=self.openai_model,
                        messages=[{"role": "user", "content": prompt}],
                        temperature=0.7,  # Balanced creativity and consistency
                        max_tokens=3000,  # Increased for comprehensive response
                        response_format={"type": "json_object"}
                    )

                    content = response.choices[0].message.content
                    if not content:
                        logger.warning("Empty response from OpenAI complete strategy")
                        continue
                    strategy_data = json.loads(content)

                    # Cache the result
                    self.request_cache[cache_key] = strategy_data
                    self.api_request_count += 1

                    logger.info(f"Generated complete strategy in one shot (attempt {attempt + 1})")
                    return strategy_data

                except json.JSONDecodeError as e:
                    logger.warning(f"JSON decode error in complete strategy on attempt {attempt + 1}: {e}")
                    if attempt == max_retries - 1:
                        return None
                    time.sleep(1)

                except Exception as e:
                    logger.warning(f"OpenAI complete strategy error on attempt {attempt + 1}: {e}")
                    if attempt == max_retries - 1:
                        return None
                    time.sleep(2)

        except Exception as e:
            logger.error(f"Failed to generate complete strategy: {e}")
            return None

        return None

    def _process_youtube_tags(self, tags: List[str], content_text: str) -> str:
        """
        Process and validate YouTube tags according to YouTube's guidelines.

        Args:
            tags: List of tags from OpenAI
            content_text: Original content text for additional processing

        Returns:
            Comma-separated string of tags under 500 characters
        """
        if not tags:
            # Generate fallback tags focusing on misspellings if OpenAI didn't provide any
            tags = self._generate_misspelling_tags(content_text)

        # Clean and format tags
        processed_tags = []
        for tag in tags:
            # Remove # symbols and clean whitespace
            clean_tag = tag.replace('#', '').strip()
            if clean_tag and len(clean_tag) > 1:
                processed_tags.append(clean_tag)

        # Join with commas and enforce 500 character limit
        tags_string = ', '.join(processed_tags)
        if len(tags_string) > 500:
            # Truncate to fit within limit
            truncated_tags = []
            current_length = 0
            for tag in processed_tags:
                test_length = current_length + len(tag) + (2 if truncated_tags else 0)  # +2 for ", "
                if test_length <= 500:
                    truncated_tags.append(tag)
                    current_length = test_length
                else:
                    break
            tags_string = ', '.join(truncated_tags)

        return tags_string

    def _generate_misspelling_tags(self, content_text: str) -> List[str]:
        """
        Generate tags focusing on common misspellings and alternative spellings.

        Args:
            content_text: The video content text

        Returns:
            List of misspelling-focused tags
        """
        # Common misspellings and alternatives for frequent terms
        misspelling_map = {
            'psychology': ['psycology', 'phsychology', 'psycholgy'],
            'relationship': ['realationship', 'relationshp', 'relatonship'],
            'entrepreneur': ['entrepeneur', 'entreprenuer', 'enterpreneur'],
            'business': ['buisness', 'bussiness', 'busines'],
            'success': ['sucess', 'succes', 'sucesss'],
            'motivation': ['motivaton', 'motiviation', 'motovation'],
            'mindset': ['mind set', 'mind-set', 'minset'],
            'philosophy': ['phylosophy', 'philosphy', 'philosohy'],
            'consciousness': ['conciousness', 'consciousnes', 'concsiousness'],
            'neuroscience': ['neuro science', 'neuro-science', 'neurosciense'],
            'artificial intelligence': ['AI', 'artifical intelligence', 'artificial inteligence'],
            'technology': ['tecnology', 'techology', 'technolgy']
        }

        content_lower = content_text.lower()
        misspelling_tags = []

        # Find key terms in content and add their misspellings
        for correct_term, misspellings in misspelling_map.items():
            if correct_term in content_lower:
                # Add the correct term
                misspelling_tags.append(correct_term)
                # Add common misspellings (limit to 2 per term to avoid spam)
                misspelling_tags.extend(misspellings[:2])

        return misspelling_tags[:8]  # Limit to 8 tags total

    def generate_clip_strategy(self, clip_number: int, clip_filename: str,
                             content_text: str, duration: float, score: float) -> Dict:
        """
        Generate viral strategy for a single clip using OpenAI API

        Args:
            clip_number: Clip index number
            clip_filename: Name of the clip file
            content_text: Text content of the clip
            duration: Duration in seconds
            score: Engagement score

        Returns:
            Dictionary containing the viral strategy
        """
        # Prepare video metadata for OpenAI
        video_metadata = {
            "duration": duration,
            "score": score,
            "clip_number": clip_number,
            "filename": clip_filename
        }

        # Generate comprehensive strategy and viral analysis in one shot
        complete_strategy = self._generate_complete_strategy(content_text, video_metadata)

        if not complete_strategy:
            logger.error(f"Failed to generate complete strategy for clip {clip_number}")
            raise RuntimeError("OpenAI strategy generation failed - no fallback available")

        # Categorize content for metadata
        category = self.categorize_content(content_text)

        # Generate content preview
        content_preview = content_text[:150] + "..." if len(content_text) > 150 else content_text

        # Process YouTube tags according to new guidelines
        youtube_tags_raw = complete_strategy.get("youtube_tags", [])
        youtube_tags_processed = self._process_youtube_tags(youtube_tags_raw, content_text)

        # Create strategy structure with complete OpenAI-generated content and viral analysis
        strategy = {
            "clip_number": clip_number,
            "filename": clip_filename,
            "duration": f"{duration:.1f}s",
            "category": category,
            "content_preview": content_preview,
            "engagement_score": f"{score:.3f}",
            "platform_recommendations": self.PLATFORM_RECOMMENDATIONS,
            "ai_enhanced": True,
            "title_options": complete_strategy.get("viral_titles", []),
            "description": complete_strategy.get("optimized_description", ""),
            "hashtags": complete_strategy.get("strategic_hashtags", []),
            "youtube_tags": youtube_tags_processed,
            "thumbnail_concept": complete_strategy.get("thumbnail_concept", ""),
            "content_optimization": complete_strategy.get("content_optimization", {}),
            "engagement_strategies": complete_strategy.get("engagement_strategies", {}),
            "ai_insights": {
                "hook_suggestions": complete_strategy.get("content_optimization", {}).get("hook_strength", ""),
                "pacing_recommendations": complete_strategy.get("content_optimization", {}).get("pacing_structure", ""),
                "engagement_tactics": complete_strategy.get("engagement_strategies", {}).get("comment_generation", "")
            },
            "viral_analysis": {
                "viral_potential_score": complete_strategy.get("viral_potential_score", 0),
                "viral_mechanics": complete_strategy.get("viral_mechanics_analysis", {}),
                "optimization_requirements": complete_strategy.get("content_optimization", {}),
                "platform_factors": complete_strategy.get("platform_specific_viral_factors", {}),
                "engagement_prediction": complete_strategy.get("engagement_prediction", {}),
                "improvement_roadmap": complete_strategy.get("improvement_roadmap", {}),
                "risk_assessment": complete_strategy.get("risk_assessment", {}),
                "changes_needed": self._calculate_changes_needed(complete_strategy)
            }
        }

        viral_score = complete_strategy.get("viral_potential_score", "N/A")
        logger.info(f"Generated complete strategy for clip {clip_number} with viral score: {viral_score}")
        return strategy

    def _calculate_changes_needed(self, viral_analysis: Dict) -> str:
        """
        Calculate and summarize the number and type of changes needed for viral success

        Args:
            viral_analysis: The viral analysis data from OpenAI

        Returns:
            String summary of changes needed
        """
        try:
            viral_score = viral_analysis.get("viral_potential_score", 0)
            improvement_roadmap = viral_analysis.get("improvement_roadmap", {})

            # Extract priority changes
            high_priority = improvement_roadmap.get("high_priority", [])
            medium_priority = improvement_roadmap.get("medium_priority", [])
            low_priority = improvement_roadmap.get("low_priority", [])

            # Count changes
            high_count = len(high_priority) if isinstance(high_priority, list) else 0
            medium_count = len(medium_priority) if isinstance(medium_priority, list) else 0
            low_count = len(low_priority) if isinstance(low_priority, list) else 0
            total_changes = high_count + medium_count + low_count

            # Determine urgency based on viral score
            if viral_score >= 80:
                urgency = "Minor tweaks needed"
            elif viral_score >= 60:
                urgency = "Moderate optimization required"
            elif viral_score >= 40:
                urgency = "Significant changes needed"
            else:
                urgency = "Major overhaul required"

            # Create summary
            if total_changes == 0:
                return f"Score: {viral_score}/100 - {urgency}. No specific changes identified."

            changes_summary = f"Score: {viral_score}/100 - {urgency}. "
            changes_summary += f"Total changes needed: {total_changes} "
            changes_summary += f"(High: {high_count}, Medium: {medium_count}, Low: {low_count})"

            return changes_summary

        except Exception as e:
            logger.warning(f"Error calculating changes needed: {e}")
            return "Unable to calculate changes needed - analysis data incomplete"

    def create_strategy_document(self, viral_strategies: List[Dict], job_id: str) -> str:
        """
        Create comprehensive viral strategy document

        Args:
            viral_strategies: List of strategy dictionaries for each clip
            job_id: Job ID for the pipeline run

        Returns:
            Formatted markdown document string
        """
        doc = f"""# 🚀 Comprehensive Viral Strategy for Your {len(viral_strategies)} YouTube Shorts

## 📊 **Overview of Your Content**
Your clips cover powerful topics that perform exceptionally well on social media. Each clip has been analyzed and optimized for maximum viral potential.

**Job ID:** `{job_id}`
**Total Clips:** {len(viral_strategies)}
**Format:** 720x1280 (9:16 aspect ratio) - Perfect for vertical platforms

---

## 🎬 **Individual Clip Strategies**

"""

        for i, strategy in enumerate(viral_strategies, 1):
            ai_badge = " 🤖 AI-Enhanced" if strategy.get('ai_enhanced', False) else ""
            viral_score = strategy.get('viral_analysis', {}).get('viral_potential_score', 'N/A')
            viral_badge = f" 🔥 Viral Score: {viral_score}/100" if viral_score != 'N/A' else ""

            doc += f"""### **Clip {i}: {strategy['category'].replace('_', ' ').title()}**{ai_badge}{viral_badge} ({strategy['duration']})
**File:** `{strategy['filename']}`
**Content Preview:** "{strategy['content_preview']}"
**Engagement Score:** {strategy['engagement_score']}

**🎯 Title Options:**
"""
            for title in strategy['title_options']:
                doc += f"- \"{title}\"\n"

            doc += f"""
**📸 Thumbnail Strategy:**
{strategy['thumbnail_concept']}

**📝 Description:**
```
{strategy['description']}
```

**🏷️ Social Media Hashtags:**
{' '.join(strategy['hashtags']) if isinstance(strategy['hashtags'], list) else strategy['hashtags']}

**🎯 YouTube Tags (Misspelling-Focused):**
{strategy.get('youtube_tags', 'No specific tags needed - content has clear, commonly spelled terms')}
"""

            # Add viral analysis if available
            if strategy.get('viral_analysis') and viral_score != 'N/A':
                viral_analysis = strategy['viral_analysis']
                doc += f"""
**🔥 Viral Potential Analysis:**
- **Viral Score:** {viral_score}/100
- **Changes Needed:** {viral_analysis.get('changes_needed', 'N/A')}
- **Expected Views:** {viral_analysis.get('engagement_prediction', {}).get('expected_view_count_range', 'N/A')}
- **Share Probability:** {viral_analysis.get('engagement_prediction', {}).get('share_probability', 'N/A')}
- **Risk Level:** {viral_analysis.get('risk_assessment', {}).get('controversy_risk_level', 'N/A')}
"""

                # Add improvement roadmap if available
                improvement_roadmap = viral_analysis.get('improvement_roadmap', {})
                if improvement_roadmap:
                    doc += f"""
**🚀 Priority Improvements:**
"""
                    high_priority = improvement_roadmap.get('high_priority', [])
                    if high_priority:
                        doc += "**High Priority:**\n"
                        for item in (high_priority[:3] if isinstance(high_priority, list) else []):
                            doc += f"- {item}\n"

                    medium_priority = improvement_roadmap.get('medium_priority', [])
                    if medium_priority:
                        doc += "**Medium Priority:**\n"
                        for item in (medium_priority[:2] if isinstance(medium_priority, list) else []):
                            doc += f"- {item}\n"

            # Add AI insights if available
            if strategy.get('ai_enhanced') and strategy.get('ai_insights'):
                doc += f"""
**🤖 AI Optimization Insights:**
- **Hook Suggestions:** {strategy['ai_insights'].get('hook_suggestions', 'N/A')}
- **Pacing Recommendations:** {strategy['ai_insights'].get('pacing_recommendations', 'N/A')}
- **Engagement Tactics:** {strategy['ai_insights'].get('engagement_tactics', 'N/A')}
"""

            doc += f"""
**📱 Platform Recommendations:**
"""
            for platform, rec in strategy['platform_recommendations'].items():
                doc += f"- **{platform.replace('_', ' ').title()}:** {rec}\n"

            doc += "\n---\n\n"

        doc += self._get_universal_strategies()
        doc += self._get_ai_usage_summary(viral_strategies)
        return doc

    def _get_universal_strategies(self) -> str:
        """Get universal optimization strategies section"""
        return """## 🎯 **Universal Optimization Strategies**

### **Best Posting Times:**
- **Peak Hours**: 6-9 PM local time
- **Secondary**: 12-3 PM and 9-11 PM
- **Days**: Tuesday-Thursday for maximum engagement

### **Social Media Hashtag Strategy (Use 3-5 per video):**
**High-Volume Tags**: #motivation #success #relationships #mindset #lifehacks
**Niche Tags**: Based on content category (see individual strategies above)
**Trending Tags**: Check daily trends and incorporate relevant ones

### **YouTube Tags Strategy (Updated 2024):**
**Important**: YouTube has confirmed that "Tags can be useful if content in your video is commonly misspelled. Otherwise, tags play a minimal role in helping viewers find your video."

**Best Practices:**
- **Focus ONLY on misspellings** of key terms in your content
- **Include alternative spellings** of important names/concepts
- **Limit to 500 characters total** with comma separation
- **Don't rely on tags for discoverability** - titles and descriptions are far more important
- **Example**: If discussing "psychology" → include "psycology, phsychology"

### **Engagement Boosters:**
1. **Hook in First 3 Seconds**: Start with the most compelling statement
2. **End with Questions**: "What do you think?" "Have you experienced this?"
3. **Pin Comments**: Ask engaging questions in pinned comments
4. **Respond Quickly**: Reply to comments within first hour
5. **Cross-Promote**: Share on Instagram Reels, TikTok, Twitter

### **Call-to-Actions:**
- "Save this if you needed to hear it"
- "Share with someone who needs this"
- "Follow for more life-changing advice"
- "What's your experience with this?"

---

## 🚀 **Final Tips for Maximum Viral Potential**

1. **Consistency is Key**: Post regularly (daily if possible)
2. **Engage Authentically**: Respond to comments genuinely
3. **Test Different Titles**: Try A/B testing with different title approaches
4. **Monitor Analytics**: Track which clips perform best and replicate success
5. **Cross-Platform Strategy**: Adapt content for each platform's unique audience
6. **Focus on Titles & Descriptions**: These matter far more than tags for YouTube discovery

**This content has serious viral potential** - the topics are universally relatable, emotionally engaging, and provide genuine value. The vertical format with face tracking is perfect for the platform. Focus on strong hooks, emotional thumbnails, and consistent posting for maximum impact! 🚀

---

*Generated by AI-Powered Video Highlights System*
*Optimized for YouTube Shorts, Instagram Reels, and TikTok*
*Updated with YouTube's 2024 Tag Guidelines*
"""

    def _get_ai_usage_summary(self, viral_strategies: List[Dict]) -> str:
        """Generate summary of AI usage in strategy generation"""
        total_count = len(viral_strategies)

        # Calculate viral analysis statistics
        viral_scores = []
        analyzed_count = 0
        for strategy in viral_strategies:
            viral_analysis = strategy.get('viral_analysis', {})
            score = viral_analysis.get('viral_potential_score')
            if score != 'N/A' and score is not None:
                viral_scores.append(score)
                analyzed_count += 1

        avg_viral_score = sum(viral_scores) / len(viral_scores) if viral_scores else 0
        high_potential_count = sum(1 for score in viral_scores if score >= 70)

        return f"""
## 🤖 **AI Enhancement Status**

**AI-Enhanced Clips:** {total_count}/{total_count} clips optimized with OpenAI
**Viral Analysis Coverage:** {analyzed_count}/{total_count} clips analyzed
**API Requests Made:** {self.api_request_count} requests (One-shot approach)
**Enhancement Rate:** 100% (All clips AI-generated)
**Cost Efficiency:** ✅ Single API call per clip (strategy + viral analysis combined)

## 🔥 **Viral Potential Summary**

**Average Viral Score:** {avg_viral_score:.1f}/100
**High Potential Clips:** {high_potential_count}/{analyzed_count} clips (70+ score)
**Analysis Depth:** Comprehensive viral mechanics, optimization requirements, and improvement roadmaps

All clips include (generated in one API call):
- Intelligent title optimization based on viral patterns
- Customized descriptions with psychological triggers
- Strategic hashtag recommendations for social media
- **YouTube tags focused on misspellings (2024 guidelines)**
- Advanced engagement tactics
- Content optimization insights
- Personalized thumbnail concepts
- **Viral potential scoring (0-100)**
- **Detailed improvement roadmaps**
- **Platform-specific optimization analysis**
- **Engagement predictions and risk assessments**

**Tag Strategy Update:** Following YouTube's 2024 guidance, tags now focus exclusively on misspellings and alternative spellings rather than general discoverability.

**Cost Optimization:** One-shot generation reduces API costs by 50% compared to separate strategy + analysis calls.

**Note:** This system requires OpenAI API access. No template fallback is available.

---

"""

    def generate_viral_strategy(self, job_id: str, highlights_dir: str,
                               highlights_data: Optional[List[Dict]] = None) -> Optional[str]:
        """
        Generate comprehensive viral strategy for all highlight clips

        Args:
            job_id: Job ID for the pipeline run
            highlights_dir: Path to the highlights directory
            highlights_data: Optional highlights data (will load from file if not provided)

        Returns:
            Path to the generated viral strategy file, or None if failed
        """
        try:
            # Load highlights data if not provided
            if highlights_data is None:
                highlights_data = self._load_highlights_data(highlights_dir)

            if not highlights_data:
                logger.warning("No highlights data found for viral strategy generation")
                return None

            # Generate strategy for each clip
            viral_strategies = []

            for i, highlight in enumerate(highlights_data):
                clip_number = i
                clip_filename = f"reframed_clip_{clip_number:03d}_temporal_reframed.mp4"

                # Extract content information
                content_text = highlight.get("text", "")
                duration = highlight.get("duration", 0)
                score = highlight.get("final_score", 0)

                # Generate strategy for this clip
                strategy = self.generate_clip_strategy(
                    clip_number=clip_number,
                    clip_filename=clip_filename,
                    content_text=content_text,
                    duration=duration,
                    score=score
                )

                viral_strategies.append(strategy)

            # Create comprehensive strategy document
            strategy_document = self.create_strategy_document(viral_strategies, job_id)

            # Save strategy files
            highlights_path = Path(highlights_dir)
            strategy_path = highlights_path / "viral_strategy.md"
            json_strategy_path = highlights_path / "viral_strategy.json"

            # Save markdown file
            strategy_path.write_text(strategy_document, encoding='utf-8')

            # Save JSON file
            strategy_json_data = {
                "job_id": job_id,
                "total_clips": len(viral_strategies),
                "generation_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "api_requests_made": self.api_request_count,
                "cost_optimization": "one_shot_approach",
                "clips": viral_strategies
            }

            json_strategy_path.write_text(
                json.dumps(strategy_json_data, indent=2, ensure_ascii=False),
                encoding='utf-8'
            )

            logger.info(f"✅ Generated viral strategy for {len(viral_strategies)} clips")
            logger.info(f"📄 Markdown file: {strategy_path}")
            logger.info(f"📊 JSON file: {json_strategy_path}")
            return str(strategy_path)

        except Exception as e:
            logger.error(f"Failed to generate viral strategy: {str(e)}")
            return None

    def _load_highlights_data(self, highlights_dir: str) -> List[Dict]:
        """
        Load highlights data from JSON file.

        Args:
            highlights_dir: Path to the highlights directory

        Returns:
            List of highlight dictionaries
        """
        try:
            job_dir = Path(highlights_dir).parent
            highlights_json_path = job_dir / "world_class_highlights" / "world_class_highlights.json"

            if highlights_json_path.exists():
                highlights_file_data = json.loads(highlights_json_path.read_text())
                return highlights_file_data.get("highlights", [])
            else:
                logger.warning(f"Highlights JSON file not found: {highlights_json_path}")
                return []

        except Exception as e:
            logger.error(f"Failed to load highlights data: {e}")
            return []


def generate_viral_strategy_for_job(job_id: str, highlights_dir: str,
                                   openai_model: str = "gpt-4o-mini") -> Optional[str]:
    """
    Convenience function to generate viral strategy for a job using OpenAI

    Args:
        job_id: Job ID for the pipeline run
        highlights_dir: Path to the highlights directory
        openai_model: OpenAI model to use for generation

    Returns:
        Path to the generated viral strategy file, or None if failed
    """
    generator = ViralStrategyGenerator(openai_model=openai_model)
    return generator.generate_viral_strategy(job_id, highlights_dir)
