#!/usr/bin/env python3
"""
Viral Strategy CLI Tool

Production-ready command-line interface for generating viral marketing strategies
for video highlight clips using OpenAI API integration.

Usage:
    python viral_strategy/cli.py --job-id <job_id> --highlights-dir <path>
    python viral_strategy/cli.py --scan-output-dir
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import List, Tuple

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from viral_strategy import generate_viral_strategy_for_job

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def find_highlights_directories(output_dir: str = "output") -> List[Tuple[str, str]]:
    """
    Find all highlights directories in the output folder.

    Args:
        output_dir: Path to the output directory

    Returns:
        List of (job_id, highlights_dir) tuples
    """
    output_path = Path(output_dir)

    if not output_path.exists():
        logger.warning(f"Output directory not found: {output_dir}")
        return []

    highlights_dirs = []
    for job_path in output_path.iterdir():
        if job_path.is_dir():
            highlights_path = job_path / "highlights"
            if highlights_path.exists():
                highlights_dirs.append((job_path.name, str(highlights_path)))

    return highlights_dirs


def generate_strategy_for_job(job_id: str, highlights_dir: str) -> bool:
    """
    Generate viral strategy for a specific job.

    Args:
        job_id: Job ID
        highlights_dir: Path to highlights directory

    Returns:
        True if successful, False otherwise
    """
    print(f"🚀 Generating viral strategy for job: {job_id}")
    print(f"📂 Highlights directory: {highlights_dir}")

    # Check if strategy already exists
    highlights_path = Path(highlights_dir)
    strategy_path = highlights_path / "viral_strategy.md"

    if strategy_path.exists():
        print(f"⚠️  Viral strategy already exists: {strategy_path}")
        response = input("Do you want to regenerate it? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Skipping strategy generation")
            return False

    # Generate strategy
    try:
        result_path = generate_viral_strategy_for_job(job_id, highlights_dir)

        if result_path and Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"✅ Successfully generated viral strategy!")
            print(f"📄 Strategy file: {result_path}")
            print(f"📊 File size: {file_size:,} bytes")
            return True
        else:
            print("❌ Failed to generate viral strategy")
            return False
    except Exception as e:
        print(f"❌ Error generating viral strategy: {e}")
        logger.error(f"Strategy generation failed for job {job_id}: {e}")
        return False


def scan_and_generate_all(output_dir: str = "output") -> None:
    """
    Scan output directory and generate strategies for all jobs

    Args:
        output_dir: Path to the output directory
    """
    print(f"🔍 Scanning for highlights directories in: {output_dir}")

    highlights_dirs = find_highlights_directories(output_dir)

    if not highlights_dirs:
        print(f"❌ No highlights directories found in {output_dir}")
        return

    print(f"📁 Found {len(highlights_dirs)} highlights directories:")
    for i, (job_id, highlights_dir) in enumerate(highlights_dirs, 1):
        print(f"  {i}. {job_id}")

    print("\n" + "="*60)

    success_count = 0
    for job_id, highlights_dir in highlights_dirs:
        print(f"\n🎬 Processing job {job_id}...")
        if generate_strategy_for_job(job_id, highlights_dir):
            success_count += 1
        print("-" * 40)

    print(f"\n🎉 Completed! Generated strategies for {success_count}/{len(highlights_dirs)} jobs")


def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description="Generate viral marketing strategies for video highlight clips",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate strategy for specific job
  python viral_strategy/cli.py --job-id abc123 --highlights-dir output/abc123/highlights

  # Scan and generate for all jobs
  python viral_strategy/cli.py --scan-output-dir

  # Scan custom output directory
  python viral_strategy/cli.py --scan-output-dir --output-dir /path/to/output
        """
    )

    parser.add_argument(
        '--job-id',
        type=str,
        help='Job ID for the pipeline run'
    )

    parser.add_argument(
        '--highlights-dir',
        type=str,
        help='Path to the highlights directory'
    )

    parser.add_argument(
        '--scan-output-dir',
        action='store_true',
        help='Scan output directory and generate strategies for all jobs'
    )

    parser.add_argument(
        '--output-dir',
        type=str,
        default='output',
        help='Output directory to scan (default: output)'
    )

    args = parser.parse_args()

    # Validate arguments
    if args.scan_output_dir:
        scan_and_generate_all(args.output_dir)
    elif args.job_id and args.highlights_dir:
        if not Path(args.highlights_dir).exists():
            print(f"❌ Highlights directory not found: {args.highlights_dir}")
            sys.exit(1)

        success = generate_strategy_for_job(args.job_id, args.highlights_dir)
        sys.exit(0 if success else 1)
    else:
        print("❌ Error: Either provide --job-id and --highlights-dir, or use --scan-output-dir")
        parser.print_help()
        sys.exit(1)


if __name__ == "__main__":
    print("🎬 Viral Strategy Generator CLI")
    print("=" * 50)
    main()
